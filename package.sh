#!/bin/bash
set -uo pipefail
start_time=$(date +%s)

echo "git拉取最新代码"
git pull

# 确保加载 nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # 加载 nvm

# 执行 nvm list 命令
nvm use 16.20.2

root_dir=$(pwd)
target_dir="build"

# 创建所需目录
mkdir -p "$target_dir"/{android,ios,cloud}
# 定义打包失败的模块列表
build_fail_modules=()

# 进度相关变量
total_modules=0
current_module=0

# 显示进度条函数
show_progress() {
    local percent=$1
    local width=50
    local num_chars=$(($width * $percent / 100))
    local progress="["
    for ((i=0; i<$width; i++)); do
        if [ $i -lt $num_chars ]; then
            progress+="#"
        else
            progress+=" "
        fi
    done
    progress+="] $percent%"
    echo -ne "\r$progress"
}

# 清空并创建目录的函数
clean_and_create_dir() {
    rm -rf "$1" 2>/dev/null && mkdir -p "$1"
}

check_dependency_version() {
    PACKAGE_NAME=$1

    REMOTE_VERSION=$(npm view $PACKAGE_NAME version)
    echo "远端 $PACKAGE_NAME 版本：$REMOTE_VERSION"
    # 使用 awk 和 sed 提取 yarn.lock 中的版本
    VERSION=$(grep -A 1 -E "^\"?$PACKAGE_NAME@" yarn.lock | awk '/version/ {print $2}' | sed 's/"//g')

    # 输出提取到的版本
    if [ -z "$VERSION" ]; then
        echo "未找到依赖 $PACKAGE_NAME"
    else
        echo "本地 $PACKAGE_NAME 版本: $VERSION"
    fi
    [ "$REMOTE_VERSION" != "$VERSION" ] && echo "版本不匹配" && return 1
    
    return 0
}

# 包装模块的函数
package_module() {
    local module_dir=$1
    cd "${root_dir}/${module_dir}"
    
    # 更新当前处理的模块计数
    current_module=$((current_module + 1))
    local module_percent=$((current_module * 100 / total_modules))
    
    echo -e "\n[${current_module}/${total_modules}] 开始打包 ${module_dir} (总进度: ${module_percent}%)"
    show_progress $module_percent

    version=$(npm version patch)
    echo -e "\n当前version值为：$version"

    # 升级指定的依赖项并安装
    echo "正在升级依赖并安装..."
    yarn upgrade @ledvance/base @ledvance/ui-biz-bundle && yarn install --frozen-lockfile

    echo "检查依赖版本..."
    HAS_MISMATCH=0
    check_dependency_version @ledvance/base || HAS_MISMATCH=1
    check_dependency_version @ledvance/ui-biz-bundle || HAS_MISMATCH=1
    if [ "$HAS_MISMATCH" -eq 1 ]; then
        echo "依赖版本不匹配，模块 ${module_dir} 打包中断"
        build_fail_modules+=("$module_dir")
        return
    fi

    # 平台构建进度
    local platforms=("android" "ios")
    local platform_count=${#platforms[@]}
    local current_platform=0

    for platform in "${platforms[@]}"; do
        current_platform=$((current_platform + 1))
        local platform_progress=$((current_platform * 100 / platform_count))

	# 添加跳过条件：如果是 rn-module-camera 且平台是 ios，则跳过
        if [[ "$module_dir" == "rn-module-camera" && "$platform" == "ios" ]]; then
            echo -e "\n[${current_module}/${total_modules}] 跳过 ${module_dir} - ${platform} 的构建"
            continue
        fi

        echo -e "\n[${current_module}/${total_modules}] 打包 ${module_dir} - ${platform} (平台进度: ${platform_progress}%)"
        
        clean_and_create_dir "build/$platform"
        echo "正在构建 $platform bundle..."
        npx react-native bundle --platform $platform --dev false --entry-file index.js \
            --bundle-output "build/$platform/main.jsbundle" --assets-dest "build/$platform"

        if [ -z "$(ls -A "build/$platform")" ]; then
            echo "构建 ${platform} 失败，模块 ${module_dir} 打包中断"
            build_fail_modules+=("$module_dir")
            return
        fi

        echo "复制文件到目标目录..."
        clean_and_create_dir "${root_dir}/${target_dir}/$platform/${module_dir}"
        cp -rp build/$platform/* "${root_dir}/${target_dir}/$platform/${module_dir}"

        cloud_dir="${root_dir}/${target_dir}/cloud/cloud-${module_dir}/$platform"
        clean_and_create_dir "$cloud_dir"
        cp -rp "build/$platform/"* "$cloud_dir"

        echo "创建 zip 包和 MD5..."
        (cd "$cloud_dir" && zip -X -rq "../../cloud-${module_dir}-$platform.zip" *)
        (cd "${root_dir}/${target_dir}/cloud" && md5sum "cloud-${module_dir}-$platform.zip" | cut -d ' ' -f1 > "cloud-${module_dir}-$platform.md5")
    done

    cd "${root_dir}/${target_dir}/cloud"
    rm -rf "cloud-${module_dir}"
    echo -e "\n[${current_module}/${total_modules}] 打包完成 ${module_dir}"
    
    # 更新总进度
    show_progress $module_percent
}

# 主函数
main() {
    # 定义变量存储待打包模块列表
    local modules_to_package=()

    if [ $# -eq 0 ]; then
        echo "遍历目录获取需要打包的模块"
        local excludes=("build/")

        # 首先收集所有需要打包的模块
        for dir in */; do
            if [[ ! " ${excludes[*]} " =~ " ${dir} " ]]; then
                modules_to_package+=("${dir%/}")
            else
                echo "已排除目录: ${dir}"
            fi
        done
    else
        echo "从入参中获取打包模块"
        # 收集命令行参数中的模块
        for module_dir in "$@"; do
            modules_to_package+=("$module_dir")
        done
    fi

    # 计算总模块数
    total_modules=${#modules_to_package[@]}

    # 显示打包清单
    echo -e "\n===== 打包清单 ====="
    echo "总计 ${total_modules} 个模块将被打包:"
    for ((i=0; i<${#modules_to_package[@]}; i++)); do
        echo "  $((i+1)). ${modules_to_package[$i]}"
    done
    echo "================="

    # 执行打包
    for module_dir in "${modules_to_package[@]}"; do
        package_module "$module_dir"
    done

    # 确保进度条最后显示100%
    if [ $current_module -eq $total_modules ]; then
        echo -e "\n所有模块打包完成！"
        show_progress 100
        echo -e "\n"
    fi

    # 显示失败模块统计
    if [ ${#build_fail_modules[@]} -gt 0 ]; then
        echo -e "\n打包失败的模块(${#build_fail_modules[@]}/${total_modules}):"
        for fail_module in "${build_fail_modules[@]}"; do
            echo "  - ${fail_module}"
        done
    else
        echo -e "\n所有模块打包成功！"
    fi

    echo -e "\n所有打包线程执行完成, 等待压缩 ..."
    
    cd ${root_dir}
    ${root_dir}/compress.sh

    end_time=$(date +%s)
    duration=$((end_time - start_time))
    minutes=$((duration / 60))
    seconds=$((duration % 60))
    echo -e "\n打包总执行时间为: ${minutes}分${seconds}秒"
}

main "$@"
