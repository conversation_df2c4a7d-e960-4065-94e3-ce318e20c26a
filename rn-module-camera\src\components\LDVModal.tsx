import {ScrollView, Text, TouchableOpacity, View, ViewProps, StyleSheet} from "react-native";
import React, {PropsWithChildren} from "react";
import {Modal, Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'

const {convertX: cx, height} = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface LDVModalProps extends PropsWithChildren<ViewProps> {
    theme?: ThemeType
    visible: boolean,
    title: string,
    cancel?: string,
    onCancelPress?: () => void,
    confirm?: string,
    onConfirmPress?: () => void,
    onMaskPress?: () => void,
}

export default withTheme(function LDVModal(props: LDVModalProps) {

    const styles = StyleSheet.create({
        titleParent: {
            backgroundColor: props.theme?.card.head,
            flexDirection: 'row',
            height: cx(60),
            justifyContent: 'space-between',
            alignItems: 'center',
            borderTopLeftRadius: cx(10),
            borderTopRightRadius: cx(10),
            paddingHorizontal: cx(8)
        },
        title: {
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
            flex: 1,
            textAlign: 'center'
        },
        button: {
            color: props.theme?.button.primary,
            fontSize: cx(16),
            minWidth: cx(60)
        },
        content: {
            height: height - cx(100),
            paddingHorizontal: cx(24),
            backgroundColor: props.theme?.global.background
        }
    })

    return (<Modal visible={props.visible} onMaskPress={props.onMaskPress}>
        <ScrollView>
            <View>
                <View style={styles.titleParent}>
                    <TouchableOpacity onPress={() => {
                        props.onCancelPress && props.onCancelPress();
                    }}>
                        <Text style={styles.button}>{props.cancel}</Text>
                    </TouchableOpacity>
                    <Text style={styles.title}>{props.title}</Text>
                    <TouchableOpacity onPress={() => {
                        props.onConfirmPress && props.onConfirmPress();
                    }}>
                        <Text style={styles.button}>{props.confirm}</Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.content}>{props.children}</View>
            </View>
        </ScrollView>
    </Modal>)
})
