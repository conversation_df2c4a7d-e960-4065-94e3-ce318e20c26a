import {StyleProp, Text, View, ViewStyle} from "react-native";
import React from "react";
import {Picker, PickerDataProps, Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

type LDVPickerProps = {
    theme?: ThemeType
    selectedValue: string,
    onValueChange: (value: string) => void,
    dataSource: PickerDataProps[],
    label?: string,
    style?: StyleProp<ViewStyle> | undefined,
}
export default withTheme(function LDVPicker(props: LDVPickerProps) {
    const {selectedValue, onValueChange, dataSource, label} = props;
    return (<View style={[props.style, {position: 'relative'}]}>
        <Picker selectedValue={selectedValue}
                style={{backgroundColor: props.theme?.global.background}}
                theme={{fontSize: cx(18)}}
                onValueChange={onValueChange}
                textSize={cx(18)}>
            {dataSource.map(item => (
                <Picker.Item key={item.value}
                             value={item.value}
                             label={item.label}/>
            ))}
        </Picker>
        {!!label && <Text style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            right: 0,
            height: 40,
            lineHeight: 40,
            alignItems: 'center',
            marginStart: cx(16),
            textAlign: 'left',
            color: props.theme?.global.fontColor,
            textAlignVertical: 'center',
            transform: [{translateY: -20}],
            fontSize: cx(18),
        }}>{label}</Text>}
    </View>)
})
