import {View, Text, StyleSheet, ViewProps} from "react-native";
import React, {PropsWithChildren} from "react";
import {Utils} from "tuya-panel-kit";
import Card from "@ledvance/base/src/components/Card";
import {useReactive, useUpdateEffect} from "ahooks";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export type LDVRadioButtonItemData = {
    value: string,
    title: string,
    flex?: number,
}

interface LDVRadioButtonGroupProps extends PropsWithChildren<ViewProps> {
    theme?: ThemeType
    data: LDVRadioButtonItemData[]
    checkedValue: string,
    onCheckedChange: (value: LDVRadioButtonItemData) => void
}

export default withTheme(function LDVRadioButtonGroup(props: LDVRadioButtonGroupProps) {
    const {data, checkedValue, onCheckedChange} = props;
    const state = useReactive({
        checkedValue: checkedValue,
    });
    useUpdateEffect(() => {
        state.checkedValue = checkedValue;
    }, [checkedValue]);


    const styles = StyleSheet.create({
        rootBg: {
            padding: cx(2),
            flexDirection: 'row',
            backgroundColor: props.theme?.segment.background,
            borderRadius: cx(6)
        },
        line: {
            width: cx(1),
            backgroundColor: '#3C3C435B',
        },
        radioItemTitle: {
            color: props.theme?.segment.fontColor,
            paddingVertical: cx(8),
            paddingHorizontal: cx(2),
            fontSize: cx(14),
            fontFamily: 'helvetica_neue_lt_std_roman',
        },
        radioItemChecked: {
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: cx(6),
            backgroundColor: props.theme?.segment.active
        },
        radioItemUnchecked: {
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: props.theme?.segment.background,
            elevation: 0,
            shadowOpacity: 0,
            shadowRadius: 0,
            shadowOffset: {
                width: 0,
                height: 0,
            },
            borderRadius: 0
        },
    });

    const renderItem = (item: LDVRadioButtonItemData, index: number) => {
        const isLastItem = index === data.length - 1;
        return (<View key={item.value} style={{flex: item.flex || 1}}>
            <RadioButtonItem
                onPress={() => onCheckedChange(item)}
                title={item.title}
                isChecked={item.value === state.checkedValue}
                styles={styles}
            />
            {!isLastItem && <View style={styles.line}/>}
        </View>);
    }

    return (<View style={[styles.rootBg, props.style]}>
        {data !== null && data !== undefined && data.map((item, index) => renderItem(item, index))}
    </View>)
})

interface RadioButtonItemProps {
    onPress: () => void
    title: string
    isChecked: boolean
    styles: StyleSheet.NamedStyles<any>
}

function RadioButtonItem(props: RadioButtonItemProps) {
    const { styles } = props
    return (
        <Card onPress={props.onPress} style={props.isChecked ? styles.radioItemChecked : styles.radioItemUnchecked}>
            <Text style={styles.radioItemTitle} numberOfLines={1}>{props.title}</Text>
        </Card>
    );
}
