import {TouchableOpacity, View, Text, Image, StyleSheet, ViewProps} from "react-native";
import React, {PropsWithChildren} from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import {Utils} from "tuya-panel-kit";
import res from '@ledvance/base/src/res'
import Card from "@ledvance/base/src/components/Card";
import {useReactive, useUpdateEffect} from "ahooks";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export type LDVRadioItemData = {
    value: string,
    title: string,
    description?: string
    content?: string
}

interface LDVRadioGroupProps extends PropsWithChildren<ViewProps> {
    theme?: ThemeType
    data: LDVRadioItemData[]
    checkedValue: string,
    onCheckedChange: (value: LDVRadioItemData) => void
}

export default withTheme(function LDVRadioGroup(props: LDVRadioGroupProps) {
    const {data, checkedValue, onCheckedChange} = props;
    const state = useReactive({
        checkedValue: checkedValue,
    });
    useUpdateEffect(() => {
        state.checkedValue = checkedValue;
    }, [checkedValue]);

    const styles = StyleSheet.create({
        radioGroupCard: {
            paddingVertical: cx(4),
        },
        line: {
            height: cx(1),
            marginHorizontal: cx(12),
            backgroundColor: props.theme?.container.divider,
        },
        radioItemRoot: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: cx(12),
            paddingVertical: cx(8),
        },
        radioItemTextGroup: {
            flex: 1,
            marginEnd: cx(12),
            justifyContent: 'center',
        },
        radioItemTitle: {
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
        },
        radioItemDescription: {
            color: props.theme?.global.secondFontColor,
            fontSize: cx(14),
        },
        radioItemContent: {
            color: props.theme?.global.fontColor,
            fontSize: cx(13),
        },
        radioItemCheckedImage: {
            width: cx(32),
            height: cx(32),
            marginEnd: cx(4),
        },
    });

    const renderItem = (item: LDVRadioItemData, index: number) => {
        const isLastItem = index === data.length - 1;
        return (<View key={item.value}>
            <RadioItem
                onPress={() => onCheckedChange(item)}
                title={item.title}
                description={item.description}
                content={item.content}
                isChecked={item.value === state.checkedValue}
                styles={styles}
            />
            {!isLastItem && <View style={styles.line}/>}
        </View>);
    }

    return (<View style={props.style}>
        <Card style={styles.radioGroupCard}>
            {data !== null && data !== undefined && data.map((item, index) => renderItem(item, index))}
            <View>{props.children}</View>
        </Card>
    </View>)
})

interface RadioItemProps {
    onPress: () => void
    title: string
    description?: string
    content?: string
    isChecked: boolean
    styles: StyleSheet.NamedStyles<any>
}

function RadioItem(props: RadioItemProps) {
    const { styles } = props
    return (
        <TouchableOpacity onPress={props.onPress}>
            <View style={styles.radioItemRoot}>
                <View style={styles.radioItemTextGroup}>
                    <Text style={styles.radioItemTitle}>{props.title}</Text>
                    <Spacer height={cx(4)}/>
                    {!!props.description && <Text style={styles.radioItemDescription}>{props.description}</Text>}
                    {!!props.content && <View>
                        <Spacer height={cx(4)}/>
                        <Text style={styles.radioItemContent}>{props.content}</Text>
                    </View>}
                </View>
                <Image
                    source={{uri: res.ic_check}}
                    style={[
                        styles.radioItemCheckedImage,
                        {
                            opacity: props.isChecked ? 100 : 0,
                        },
                    ]}/>
            </View>
        </TouchableOpacity>
    );
}
