import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View
} from 'react-native'
import React, {useRef, useState} from 'react'
import {Utils} from 'tuya-panel-kit'
import res from '@ledvance/base/src/res'
import ThemeType from '@ledvance/base/src/config/themeType'
import {useReactive} from "ahooks";

const cx = Utils.RatioUtils.convertX
const {withTheme} = Utils.ThemeUtils

interface TextFieldProps extends TextInputProps {
  theme?: ThemeType
  showError?: boolean
  errorText?: string
  tipIcon?: ImageSourcePropType
  tipColor?: string
  editable?: boolean
}

const PasswordTextField = (props: TextFieldProps) => {
  const [isPasswordVisible, setPasswordVisible] = useState(false);
  const icon = props.tipIcon || {uri: res.ic_warning_amber}
  const color = props.tipColor || props.theme?.global.warning
  const editable = props.editable ?? !props.editable
  const state = useReactive({
    value: props.value || '',
    lastValue: props.value || '',
    flag: false,
  })
  const inputRef = useRef<TextInput>(null);
  const timer = useRef<any>(null)

  const delayUpdateFocus = () => {
    let count = 0
    timer.current = setInterval(() => {
      count++
      if (count == 1) {
        // props.onChangeText && props.onChangeText(state.lastValue)
        state.flag = false;
        state.value = state.lastValue
        clearInterval(timer.current)
        timer.current = null
        return
      }
    }, 200);
  }

  const styles = StyleSheet.create({
    topTip: {
      marginTop: cx(5),
      fontSize: cx(12),
      marginStart: cx(13),
      color: props.theme?.global.secondFontColor,
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    textInputGroup: {
      flexDirection: 'row',
      borderRadius: cx(4),
      backgroundColor: props.theme?.textInput.background,
      alignItems: 'center',
    },
    textInput: {
      flex: 1,
      height: cx(44),
      marginStart: cx(16),
      marginEnd: cx(6),
      fontSize: cx(16),
      color: props.theme?.textInput.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    iconTouchable: {
      marginEnd: cx(18),
      padding: cx(4),
    },
    iconCancelTouchable: {
      marginEnd: cx(6),
      padding: cx(4),
    },
    icon: {
      width: cx(16),
      height: cx(16),
    },
    line: {
      height: 1,
      position: 'absolute',
      start: cx(4),
      end: cx(4),
      bottom: 0,
      backgroundColor: props.theme?.textInput.line,
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginStart: cx(13),
    },
  })
  return (
    <View style={props.style}>
      <Text style={[styles.topTip, {opacity: (!!props.value) ? 1 : 0}]}>{props.placeholder}</Text>
      <View style={styles.textInputGroup}>
        <TextInput
          {...props}
          value={state.value}
          ref={inputRef}
          style={styles.textInput}
          secureTextEntry={!isPasswordVisible}
          contextMenuHidden={true}
          onChangeText={(text) => {
            if (state.flag) return
            state.value = text;
            props.onChangeText && props.onChangeText(text)
          }}
        />
        {!!props.value && editable &&
            <TouchableOpacity
                style={styles.iconCancelTouchable}
                onPress={() => {
                  state.value = ""
                }}>
                <Image source={{uri: res.ic_cancel}} style={styles.icon}/>
            </TouchableOpacity>
        }

        <TouchableOpacity
          style={styles.iconTouchable}
          onPress={() => {
            state.flag = true
            state.lastValue = state.value
            state.value = ''
            setPasswordVisible(!isPasswordVisible);
            delayUpdateFocus()
          }}>
          <Image source={{uri: isPasswordVisible ? res.icon_eyes_opens : res.icon_eyes_closed}} style={styles.icon}/>
        </TouchableOpacity>
        <View style={styles.line}/>
      </View>
      <View style={[styles.errorContainer, {opacity: !!(props.showError) ? 1 : 0}]}>
        <Image source={icon} style={[styles.icon, {tintColor: color}]}/>
        <Text
          style={{marginLeft: cx(5), color: color}}
        >{props.errorText}</Text>
      </View>
    </View>
  )
}

export default withTheme(PasswordTextField)
