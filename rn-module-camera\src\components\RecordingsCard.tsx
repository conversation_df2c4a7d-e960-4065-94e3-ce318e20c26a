import {
    Text,
    ViewProps,
    Image,
    StyleSheet, View,
} from "react-native";
import React, {PropsWithChildren} from "react";
import Card from "@ledvance/base/src/components/Card";
import {Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface RecordingsCardProps extends PropsWithChildren<ViewProps> {
    theme?: ThemeType
    title: string,
    icon: { uri: string} | number,
    onPress?: () => void
}

export default withTheme(function RecordingsCard(props: RecordingsCardProps) {
    const {title, icon, onPress} = props;
    const styles = StyleSheet.create({
        card: {
            justifyContent: 'center',
            alignItems: 'center',
        },
        containerStyle: {
            flexDirection: 'column', width: cx(154), height: cx(154), alignItems: 'center', justifyContent: 'center'
        },
        imageBox: {
            backgroundColor: props.theme?.global.thirdBrand,
            width: cx(56),
            height: cx(56),
            borderRadius: cx(50),
            alignItems: 'center',
            justifyContent: 'center'
        },
        image: {width: cx(32), height: cx(32)},
        title: {
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            marginTop: cx(20),
            textAlign: 'center',
        },
    });
    return (
        <Card style={[props.style, styles.card]} containerStyle={styles.containerStyle} onPress={onPress}>
            <View style={styles.imageBox}>
                <Image source={icon} style={styles.image}/>
            </View>
            <Text style={styles.title}>{title}</Text>
        </Card>);
})
