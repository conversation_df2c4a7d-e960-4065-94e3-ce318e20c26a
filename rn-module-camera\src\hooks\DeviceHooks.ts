import {
  useDeviceId,
  useDeviceInfo,
  useDp,
  useDps,
  useTimeSchedule
} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {Result} from '@ledvance/base/src/models/modules/Result';
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from "@ledvance/base/src/components/AdvanceCard";
import {RouterKey} from "../navigation/Router";
import I18n from "@ledvance/base/src/i18n";
import {useReactive, useUpdateEffect} from "ahooks";
import {useCallback, useEffect, useState} from "react";
import {NativeApi} from "@ledvance/base/src/api/native";
import xlog from "../utils/common";
import {NativeModules} from "react-native";
import {GlobalParams} from "@ledvance/base/src/models/GlobalParams";
import {PIR_SWITCH_OFF} from "../utils/PIRSensitivity";
import {TimeSchedulePageParams} from "@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage";
import {DeviceStateType, DeviceType} from "@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface";
import {cloneDeep} from "lodash";
import dayjs from "dayjs";
import {ApplyForItem} from "@ledvance/base/src/utils/interface";
import {RecordingsData} from "../pages/RecordingsPage";
import res from "@ledvance/base/src/res";
import {getGlobalParamsDp, isSupportFunctions} from "@ledvance/base/src/utils/common";
import {TYSdk} from "tuya-panel-kit";
import {PatrolTimeMode} from "../utils/PatrolTimeMode";
import {SiteOperationType, SitePoint} from "../utils/SitePoint";

const devicePanel = NativeModules.LDVDevicePanelManager

const dpKC = {
  basic_indicator: {key: 'basic_indicator', code: '101'},
  basic_flip: {key: 'basic_flip', code: '103'},
  basic_osd: {key: 'basic_osd', code: '104'},
  basic_private: {key: 'basic_private', code: '105'},
  motion_sensitivity: {key: 'motion_sensitivity', code: '106'},
  basic_nightvision: {key: 'basic_nightvision', code: '108'},
  sd_storge: {key: 'sd_storge', code: '109'},
  sd_status: {key: 'sd_status', code: '110'},
  sd_format: {key: 'sd_format', code: '111'},
  motion_timer_setting: {key: 'motion_timer_setting', code: '114'},
  movement_detect_pic: {key: 'movement_detect_pic', code: '115'},
  ptz_stop: {key: 'ptz_stop', code: '116'},
  sd_format_state: {key: 'sd_format_state', code: '117'},
  ptz_control: {key: 'ptz_control', code: '119'},
  ipc_auto_siren: {key: 'ipc_auto_siren', code: '120'},
  record_loop: {key: 'record_loop', code: '121'},
  nightvision_mode: {key: 'nightvision_mode', code: '124'},
  battery_report_cap: {key: 'battery_report_cap', code: '126'},
  event_linkage: {key: 'event_linkage', code: '127'},
  ptz_calibration: {key: 'ptz_calibration', code: '132'},
  motion_switch: {key: 'motion_switch', code: '134'},
  floodlight_switch: {key: 'floodlight_switch', code: '138'},
  decibel_switch: {key: 'decibel_switch', code: '139'},
  decibel_sensitivity: {key: 'decibel_sensitivity', code: '140'},
  decibel_upload: {key: 'decibel_upload', code: '141'},
  wireless_electricity: {key: 'wireless_electricity', code: '145'},
  wireless_powermode: {key: 'wireless_powermode', code: '146'},
  wireless_lowpower: {key: 'wireless_lowpower', code: '147'},
  wireless_awake: {key: 'wireless_awake', code: '149'},
  record_switch: {key: 'record_switch', code: '150'},
  record_mode: {key: 'record_mode', code: '151'},
  pir_switch: {key: 'pir_switch', code: '152'},
  floodlight_lightness: {key: 'floodlight_lightness', code: '158'},
  siren_switch: {key: 'siren_switch', code: '159'},
  basic_device_volume: {key: 'basic_device_volume', code: '160'},
  motion_tracking: {key: 'motion_tracking', code: '161'},
  device_restart: {key: 'device_restart', code: '162'},
  cry_detection_switch: {key: 'cry_detection_switch', code: '167'},
  motion_area_switch: {key: 'motion_area_switch', code: '168'},
  motion_area: {key: 'motion_area', code: '169'},
  humanoid_filter: {key: 'humanoid_filter', code: '170'},
  cruise_switch: {key: 'cruise_switch', code: '174'},
  cruise_mode: {key: 'cruise_mode', code: '175'},
  cruise_time_mode: {key: 'cruise_time_mode', code: '176'},
  cruise_time: {key: 'cruise_time', code: '177'},
  memory_point_set: {key: 'memory_point_set', code: '178'},
  cruise_status: {key: 'cruise_status', code: '179'},
  alarm_message: {key: 'alarm_message', code: '185'},
  basic_anti_flicker: {key: 'basic_anti_flicker', code: '188'},
  ipc_preset_action: {key: 'ipc_preset_action', code: '190'},
  ipc_preset_set: {key: 'ipc_preset_set', code: '199'},
  ipc_siren_duration: {key: 'ipc_siren_duration', code: '194'},
  ipc_siren_volume: {key: 'ipc_siren_volume', code: '195'},
  initiative_message: {key: 'initiative_message', code: '212'},
  ipc_usage_mode: {key: 'ipc_usage_mode', code: '231'},
  ipc_anti_dismantle: {key: 'ipc_anti_dismantle', code: '231'},
  floodlight_pir_lux: {key: 'floodlight_pir_lux', code: '231'},
  flight_warn_time1: {key: 'flight_warn_time1', code: '232'},
  cus_alarm: {key: 'cus_alarm', code: '232'},
  ipc_player_flip: {key: 'ipc_player_flip', code: '233'},
  onvif_switch: {key: 'onvif_switch', code: '237'},
  onvif_change_pwd: {key: 'onvif_change_pwd', code: '238'},
  onvif_pw_changed: {key: 'onvif_pw_changed', code: '239'},
  onvif_ip_addr: {key: 'onvif_ip_addr', code: '240'},
  onvif_iptype_config: {key: 'onvif_iptype_config', code: '241'},
  ipc_security_level: {key: 'ipc_security_level', code: '241'},
  ipc_show_sn: {key: 'ipc_show_sn', code: '242'},
  ipc_recording_time: {key: 'ipc_recording_time', code: '242'},
  ipc_security_lux: {key: 'ipc_security_lux', code: '242'},
  ipc_security_di: {key: 'ipc_security_di', code: '243'},
  pir_alarm_interval: {key: 'pir_alarm_interval', code: '243'},
  ipc_bri_body: {key: 'ipc_bri_body', code: '244'},
  ipc_bri_nobody: {key: 'ipc_bri_nobody', code: '245'},
  ipc_light_time: {key: 'ipc_light_time', code: '246'},
  ipc_light_time1: {key: 'ipc_light_time1', code: '247'},
  ipc_alarm_mode1: {key: 'ipc_alarm_mode1', code: '248'},
  ipc_security_switch: {key: 'ipc_security_switch', code: '249'},
};

export function useMotionSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp(dpKC.motion_switch.key));
}

export function useDecibelSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp(dpKC.decibel_switch.key));
}

export function useDecibelSensitivity(): [string, (value: string) => Promise<Result<any>>] {
  return useDp<string, any>(getGlobalParamsDp(dpKC.decibel_sensitivity.key));
}

export function useDetectBabyCryingSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp(dpKC.cry_detection_switch.key));
}

export function useAutoSiren(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp(dpKC.ipc_auto_siren.key));
}

export function useMotionSensitivity(): [string, (value: string) => Promise<Result<any>>] {
  return useDp<string, any>(getGlobalParamsDp(dpKC.motion_sensitivity.key));
}

export function usePIRSwitch(): [string, (value: string) => Promise<Result<any>>] {
  return useDp<string, any>(getGlobalParamsDp(dpKC.pir_switch.key));
}

export function useBasicIndicator(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp(dpKC.basic_indicator.key));
}

export function useLightSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.floodlight_switch.key));
}

export function useLightness(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.floodlight_lightness.key));
}

export function useVolume(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_device_volume.key));
}

export function useFlightWarnTime(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.flight_warn_time1.key));
}

export function useFlightPirLux(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.floodlight_pir_lux.key));
}

export function useSirenSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.siren_switch.key));
}

export function useNightVision(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_nightvision.key));
}

export function useNightVisionMode(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.nightvision_mode.key));
}

export function useRecordMode(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.record_mode.key));
}

export function useFlipScreen(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_flip.key));
}

export function useTimeWatermark(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_osd.key));
}

export function useSecurityMode(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_private.key));
}

export function useSecuritySwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_security_switch.key));
}

export function useSecurityLevel(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_security_level.key));
}

export function useSensingDistance(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_security_di.key));
}

export function useSecurityLux(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_security_lux.key));
}

export function useHumanBrightness(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_bri_body.key));
}

export function useHumanBrightnessTime(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_light_time.key));
}

export function useNoHumanBrightness(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_bri_nobody.key));
}

export function useNoHumanBrightnessTime(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_light_time1.key));
}

export function useAlarmMode(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_alarm_mode1.key));
}

export function useRecordLoop(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.record_loop.key));
}

export function useHumanoidFilter(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.humanoid_filter.key));
}

export function useMotionAreaSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.motion_area_switch.key));
}

export function useMotionTracking(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.motion_tracking.key));
}

export function useSDCardStatus(): number {
  return useDp<number, any>(getGlobalParamsDp(dpKC.sd_status.key))[0];
}

export function useExistSDCard(): boolean {
  return useDp<number, any>(getGlobalParamsDp(dpKC.sd_status.key))[0] !== 5;
}

export function useSDCardStorage(): [number, number, number] {
  const storage = useDp<string, any>(getGlobalParamsDp(dpKC.sd_storge.key))[0]
  xlog("storage==============>", storage)
  if (!storage || !storage.includes("|")) {
    return [100, 100, 0]
  }
  const storageNum = storage.split('|').map(num => parseInt(num))
  return [storageNum[0], storageNum[1], storageNum[2]];
}

export function useSDCardFormat(): (value: boolean) => Promise<Result<any>> {
  return useDp(getGlobalParamsDp(dpKC.sd_format.key))[1];
}

export function useRecordSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.record_switch.key));
}

export function useDeviceRestart(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.device_restart.key));
}

export function useOnvifSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.onvif_switch.key));
}

export function useOnvifIpTypeConfig(): [string, (value: string) => Promise<Result<any>>] {
  GlobalParams
  return useDp(getGlobalParamsDp(dpKC.onvif_iptype_config.key));
}

export function useOnvifChangePwd(): [string, (newPwd: string, oldPwd?: string) => Promise<Result<any>>] {
  const [pwd, setPwd] = useDp<string, any>(getGlobalParamsDp(dpKC.onvif_change_pwd.key))
  const setPwdFn = useCallback((newPassword: string, oldPassword?: string) => {
    const passwordObj = {newpwd: newPassword, oldpwd: oldPassword || 'admin'}
    return setPwd(JSON.stringify(passwordObj))
  }, [])
  return [pwd, setPwdFn];
}

export function useIsNeedSetOnvifPWD(): boolean {
  return !(useDp<boolean, any>(getGlobalParamsDp(dpKC.onvif_pw_changed.key))[0]);
}

export function useOnvifIpAddress(): string {
  return useDp<string, any>(getGlobalParamsDp(dpKC.onvif_ip_addr.key))[0];
}

export function useHumanoidFocus(): [boolean, (value: boolean) => Promise<Result<any>>] {
  const [usageMode, setUsageMode] = useDp(getGlobalParamsDp(dpKC.ipc_usage_mode.key))
  const CLOSE = '0'
  const OPEN = '1'
  const setHumanoidFocus = useCallback((value) => {
    const um = value ? OPEN : CLOSE
    return setUsageMode(um)
  }, [])
  return [usageMode == OPEN, setHumanoidFocus];
}

export function useWirelessElectricity(): number {
  return useDp<number, any>(getGlobalParamsDp(dpKC.wireless_electricity.key))[0];
}

export function useAntiFlicker(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.basic_anti_flicker.key));
}

export function useVideoRecordingDuration(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_recording_time.key));
}

export function useAlarmInterval(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.pir_alarm_interval.key));
}

export function useAntiDismantle(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_anti_dismantle.key));
}

export function useWirelessLowPower(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.wireless_lowpower.key));
}

export function useWirelessPowerMode(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.wireless_powermode.key));
}

export function usePatrolSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.cruise_switch.key));
}

export function usePatrolMode(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.cruise_mode.key));
}

export function usePatrolTimeMode(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.cruise_time_mode.key));
}

export function useSirenVolume(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ipc_siren_volume.key));
}

export function useCameraCalibration(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp(dpKC.ptz_calibration.key));
}

export function useSirenDuration(): [number, (value: number) => Promise<Result<any>>] {
  const [duration, setDuration] = useDp<number, any>(getGlobalParamsDp(dpKC.ipc_siren_duration.key));
  const sirenDuration = duration / 10;
  const setSirenDuration = useCallback((value: number) => {
    return setDuration(value * 10);
  }, [])
  return [sirenDuration, setSirenDuration];
}

export function usePatrolTime(): [[string, string], (startTime: string, endTime: string) => Promise<Result<any>>] {
  const [patrolTimeJson,] = useDp<string, any>(getGlobalParamsDp(dpKC.cruise_time.key));
  const patrolTime = !!patrolTimeJson ? JSON.parse(patrolTimeJson) : undefined
  const now = dayjs()
  const [, setDps] = useDps();
  const startTime = patrolTime?.t_start || now.format('HH:mm');
  const endTime = patrolTime?.t_end || now.add(2, 'hour').format('HH:mm');
  const setPatrolTime = useCallback((startTime, endTime) => {
    const json = {t_start: startTime, t_end: endTime}
    const dps = {};
    dps[getGlobalParamsDp(dpKC.cruise_time.key)] = JSON.stringify(json);
    dps[getGlobalParamsDp(dpKC.cruise_time_mode.key)] = PatrolTimeMode.Timed
    return setDps(dps);
  }, [])
  return [[startTime, endTime], setPatrolTime];
}

export function useOperationSite(): [boolean | undefined, (params: {
  type: SiteOperationType,
  name?: string,
  sitePoint?: SitePoint,
}) => Promise<Result<any>>] {
  const [site, setSite] = useDp<string, any>(getGlobalParamsDp(dpKC.memory_point_set.key));
  const result = !!site ? JSON.parse(site) : undefined
  const isSuccess = result?.data?.error != undefined ? result?.data?.error == 0 : undefined;
  const addOrDeleteSite = useCallback((params: { type: SiteOperationType, name?: string, sitePoint?: SitePoint }) => {
    const json = {
      type: params.type,
      data: {
        name: params.name,
        num: 1,
        mpId: params?.sitePoint?.mpId,
        sets: [{devId: params?.sitePoint?.id, mpId: params?.sitePoint?.mpId}]
      }
    };
    return setSite(JSON.stringify(json));
  }, [])
  return [isSuccess, addOrDeleteSite];
}

export function useSitePointData(): [SitePoint[], (id: string, name: string) => Promise<Result<any>>, boolean, (refresh: boolean) => void] {
  const devInfo = useDeviceInfo();
  const state = useReactive({
    memoryPointList: [] as SitePoint[],
  });
  const [onLoading, setOnLoading] = useState(true);
  const [refresh, setRefresh] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const memoryPointList = await TYSdk.apiRequest<SitePoint[]>("tuya.m.ipc.memory.point.list", {devId: devInfo?.devId}, '1.0');
        state.memoryPointList = memoryPointList;
        xlog("useSitePointData==============>", memoryPointList)
      } catch (error) {
        console.log("useMemoryPontData", error)
      }
      setOnLoading(false);
    };
    if (devInfo?.devId && refresh) {
      setRefresh(false)
      fetchData().then();
    }
  }, [devInfo?.devId, refresh]);
  const updateMemoryPoint = async (id: string, name: string) => {
    const res = await TYSdk.apiRequest<Boolean>('tuya.m.ipc.memory.point.rename', {
      devId: devInfo.devId,
      id,
      name
    }, '1.0');
    xlog("updateMemoryPoint==============>", res)
    return {
      success: !!res
    }
  }
  return [state.memoryPointList, updateMemoryPoint, onLoading, setRefresh]
}

export function useShowCameraPreview(): [boolean, (value: boolean) => Promise<Result<any>>] {
  const deviceId = useDeviceId();
  const showCameraPreviewFeatureId = 'IpcPre';
  const [showCameraPreviewState, setShowCameraPreviewState] = useState(true);

  // 封装安全的 JSON 解析方法
  const safeParseJSON = <T, >(json: string, fallback: T): T => {
    try {
      return JSON.parse(json);
    } catch (e) {
      console.warn('Failed to parse JSON:', e);
      return fallback;
    }
  };

  useEffect(() => {
    devicePanel.getFeature(deviceId, showCameraPreviewFeatureId, (res) => {
      console.log('getFeature', showCameraPreviewFeatureId)
      if (res.result) {
        const data = res.data ? safeParseJSON<{ value: boolean }>(res.data, {value: true}) : {value: true};
        setShowCameraPreviewState(data.value);
        return;
      }
      setShowCameraPreviewState(true);
    });
  }, [deviceId, showCameraPreviewFeatureId]); // 明确依赖项

  const setShowCameraPreview = useCallback((showCameraPreview: boolean) => {
    return new Promise<Result<any>>((resolve, reject) => {
      devicePanel.putFeature(
        deviceId,
        showCameraPreviewFeatureId,
        JSON.stringify(showCameraPreview),
        (res) => {
          if (res.result) {
            resolve(res);
          } else {
            reject(new Error('Failed to set camera preview feature'));
          }
        }
      );
    });
  }, [deviceId]);

  return [showCameraPreviewState, setShowCameraPreview];
}


export const isSupportBasicIndicator = () => {
  return isSupportFunctions(dpKC.basic_indicator.key);
}

export const isSupportFloodlightSwitch = () => {
  return isSupportFunctions(dpKC.floodlight_switch.key);
}

export const isSupportFloodlightLightness = () => {
  return isSupportFunctions(dpKC.floodlight_lightness.key);
}

export const isSupportVolume = () => {
  return isSupportFunctions(dpKC.basic_device_volume.key);
}

export const isSupportDirectionControl = () => {
  return isSupportFunctions(dpKC.ptz_control.key);
}

export const isSupportSecuritySwitch = () => {
  return isSupportFunctions(dpKC.ipc_security_switch.key);
}
export const isSupportAutoSiren = () => {
  return isSupportFunctions(dpKC.ipc_auto_siren.key);
}

export const isSupportSecurityMode = () => {
  return isSupportFunctions(dpKC.basic_private.key);
}

export const isSupportFlipScreen = () => {
  return isSupportFunctions(dpKC.basic_flip.key);
}

export const isSupportTimeWatermark = () => {
  return isSupportFunctions(dpKC.basic_osd.key);
}

export const isSupportNightVision = () => {
  return isSupportFunctions(dpKC.basic_nightvision.key);
}

export const isSupportNightVisionMode = () => {
  return isSupportFunctions(dpKC.nightvision_mode.key);
}

export const isSupportRecordMode = () => {
  return isSupportFunctions(dpKC.record_mode.key);
}

export const isSupportRecordLoop = () => {
  return isSupportFunctions(dpKC.record_loop.key);
}

export const isSupportHumanoidFilter = () => {
  return isSupportFunctions(dpKC.humanoid_filter.key);
}

export const isSupportMotionAreaSwitch = () => {
  return isSupportFunctions(dpKC.motion_area_switch.key);
}

export const isSupportMotionTracking = () => {
  return isSupportFunctions(dpKC.motion_tracking.key);
}

export const isSupportSirenDuration = () => {
  return isSupportFunctions(dpKC.ipc_siren_duration.key);
}

export const isSupportSirenVolume = () => {
  return isSupportFunctions(dpKC.ipc_siren_volume.key);
}

export const isWirless = () => {
  // 是否为低功耗设备,一般根据是否存在 dpCode: wireless_awake
  return false;
}

// 静音状态、对讲状态、录制状态
export const initCameraStatus = async () => {
  try {
    const muting = await TYIpcPlayerManager.isMuting();
    const recording = await TYIpcPlayerManager.isRecording();
    const talking = await TYIpcPlayerManager.isTalkBacking();
    return {...muting, ...recording, ...talking};
  } catch (err) {
    console.log(err);
  }
};

export const isSupportPIRSwitch = () => {
  return isSupportFunctions(dpKC.pir_switch.key);
}

export const isSupportMotionSensitivity = () => {
  return isSupportFunctions(dpKC.motion_sensitivity.key);
}

export const isSupportSchedule = () => {
  return isSupportLightSwitch() || isSupportSDCardRecordSwitch() || isSupportMotionSwitch() || isSupportDecibelSwitch()
}

export const isSupportLightSwitch = () => {
  return isSupportFunctions(dpKC.floodlight_switch.key)
}

export const isSupportLightness = () => {
  return isSupportFunctions(dpKC.floodlight_lightness.key)
}

export const isSupportFlightPIRLux = () => {
  return GlobalParams.dpSchemaMap.hasOwnProperty(dpKC.floodlight_pir_lux.key)
}

export const isSupportFlightWarnTime = () => {
  return GlobalParams.dpSchemaMap.hasOwnProperty(dpKC.flight_warn_time1.key)
}

export const isSupportFlightPIR = () => {
  return isSupportFunctions(dpKC.pir_switch.key)
}

export const isSupportMotionSwitch = () => {
  return isSupportFunctions(dpKC.motion_switch.key)
}

export const isSupportDecibelSwitch = () => {
  return isSupportFunctions(dpKC.decibel_switch.key)
}

export const isSupportDecibelSensitivity = () => {
  return isSupportFunctions(dpKC.decibel_sensitivity.key)
}

export const isSupportDetectBabyCryingSwitch = () => {
  return isSupportFunctions(dpKC.cry_detection_switch.key)
}

export const isSupportSDCardRecordSwitch = () => {
  return isSupportFunctions(dpKC.record_switch.key)
}

export const isSupportDeviceRestart = () => {
  return isSupportFunctions(dpKC.device_restart.key)
}

export const isSupportOnvifSwitch = () => {
  return isSupportFunctions(dpKC.onvif_switch.key)
}

export const isSupportOnvifIpTypeConfig = () => {
  return isSupportFunctions(dpKC.onvif_iptype_config.key)
}

export const isSupportOnvifIpAddress = () => {
  return isSupportFunctions(dpKC.onvif_ip_addr.key)
}

export const isSupportOnvifResetPassword = () => {
  return isSupportFunctions(dpKC.onvif_change_pwd.key)
}

export const isSupportHumanoidFocus = () => {
  return isSupportFunctions(dpKC.ipc_usage_mode.key)
}

export const isSupportWirelessElectricity = () => {
  return isSupportFunctions(dpKC.wireless_electricity.key)
}

export const isSupportAntiFlicker = () => {
  return isSupportFunctions(dpKC.basic_anti_flicker.key)
}

export const isSupportMotionRecordingTime = () => {
  return isSupportFunctions(dpKC.ipc_recording_time.key)
}

export const isSupportPIRAlarmInterval = () => {
  return isSupportFunctions(dpKC.pir_alarm_interval.key)
}

export const isSupportAntiDismantle = () => {
  return isSupportFunctions(dpKC.ipc_anti_dismantle.key)
}

export const isSupportWirelessPowerMode = () => {
  return isSupportFunctions(dpKC.wireless_powermode.key)
}

export const isSupportWirelessLowPower = () => {
  return isSupportFunctions(dpKC.wireless_lowpower.key)
}

export const isSupportPatrolSwitch = () => {
  return isSupportFunctions(dpKC.cruise_switch.key)
}

export const isSupportPatrolMode = () => {
  return isSupportFunctions(dpKC.cruise_mode.key)
}

export const isSupportPatrolTimeMode = () => {
  return isSupportFunctions(dpKC.cruise_time_mode.key)
}

export const isSupportCruiseTime = () => {
  return isSupportFunctions(dpKC.cruise_time.key)
}

export const isSupportSitePointSet = () => {
  return isSupportFunctions(dpKC.memory_point_set.key)
}

export const isSupportPresetPoint = () => {
  return isSupportFunctions(dpKC.ipc_preset_set.key)
}

export const isSupportCameraCalibration = () => {
  return isSupportFunctions(dpKC.ptz_calibration.key)
}

export const getDirectionRange = () => {
  return GlobalParams.dpSchemaMap[dpKC.ptz_control.key]?.property?.range || []
}

export function toCameraAlbumPage(tuyaDeviceId: string) {
  toPage("LDVCameraAlbum", {tuyaDevId: tuyaDeviceId})
}

export function toCameraChangeActivityArea(tuyaDeviceId: string) {
  toPage("LDVCameraChangeActivityArea", {tuyaDevId: tuyaDeviceId})
}

export function toCameraPresetPointPage(tuyaDeviceId: string) {
  toPage("LDVCameraPresetPoint", {tuyaDevId: tuyaDeviceId})
}

export const checkIsGuestMode = async () => {
  const isGuestMode = await devicePanel.isGuestMode()
  if (isGuestMode) {
    toPage("LDVGuestModeBindAccount", {})
  }
  return isGuestMode
}

function toPage(target: string, params: any) {
  devicePanel.toPage(target, params)
}

export function useRecordingsData(): RecordingsData[] {
  const data: RecordingsData[] = [];
  data.push({
    title: I18n.getLang('recordings_overview_field1_text'),
    icon: {uri: res.sd_card},
    router: {key: RouterKey.sd_card},
  });
  data.push({
    title: I18n.getLang('recordings_overview_field2_text'),
    icon: {uri: res.cloud},
    router: {key: RouterKey.cloud_storage},
  });
  data.push({
    title: I18n.getLang('recordings_overview_field3_text'),
    icon: {uri: res.smart_phone},
    router: {key: RouterKey.smart_phone},
  });
  return data
}

export function useAdvancedData(): AdvancedData[] {
  const res: AdvancedData[] = [];
  const deviceId = useDeviceId()
  const existSDCard = useExistSDCard();
  const [sirenSwitch] = useSirenSwitch();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [motionSwitch,] = useMotionSwitch();
  const [pirSwitch,] = usePIRSwitch();
  const [securitySwitch,] = useSecuritySwitch();
  const [motionTracking,] = useMotionTracking();
  const [autoSiren,] = useAutoSiren();
  const [decibelSwitch,] = useDecibelSwitch();
  const [patrolSwitch,] = usePatrolSwitch();
  const state = useReactive({
    existSDCard: existSDCard,
    sirenSwitch: sirenSwitch,
    patrolSwitch: patrolSwitch,
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    motionDetectionStatus: (motionSwitch || (pirSwitch && pirSwitch !== PIR_SWITCH_OFF) || securitySwitch || motionTracking || autoSiren || decibelSwitch) ? AdvancedStatus.Enable : AdvancedStatus.Disable
  });
  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.existSDCard = existSDCard;
    state.sirenSwitch = sirenSwitch;
    state.patrolSwitch = patrolSwitch;
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
    state.motionDetectionStatus = (motionSwitch || (pirSwitch && pirSwitch !== PIR_SWITCH_OFF) || securitySwitch || motionTracking || autoSiren || decibelSwitch) ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule, motionSwitch, pirSwitch, securitySwitch, motionTracking, autoSiren, existSDCard, decibelSwitch, sirenSwitch, patrolSwitch]);

  if (isSupportSchedule()) {
    const cameraApplyFor: ApplyForItem[] = [];
    if (isSupportLightSwitch()) {
      cameraApplyFor.push({
        type: 'light',
        name: I18n.getLang('Onoff_button_socket'),
        key: I18n.getLang('camera_tile_dim_lighting_headline'),
        dp: getGlobalParamsDp(dpKC.floodlight_switch.key),
        enable: true,
      });
    }
    if (state.existSDCard && isSupportSDCardRecordSwitch()) {
      cameraApplyFor.push({
        type: 'socket',
        key: I18n.getLang('device_menu_camera_fourthbox_topic'),
        dp: getGlobalParamsDp(dpKC.record_switch.key),
        enable: true,
      });
    }
    if (isSupportMotionSwitch()) {
      cameraApplyFor.push({
        type: 'socket',
        key: I18n.getLang('motion_detection_headline_text'),
        dp: getGlobalParamsDp(dpKC.motion_switch.key),
        enable: true,
      });
    }
    if (isSupportDecibelSwitch()) {
      cameraApplyFor.push({
        type: 'socket',
        key: I18n.getLang('motion_detection_sound_detection'),
        dp: getGlobalParamsDp(dpKC.decibel_switch.key),
        enable: true,
      });
    }
    const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
      const deviceState: DeviceStateType = {
        // @ts-ignore
        deviceData: {
          type: DeviceType.LightSource,
          deviceData: {
            h: 0,
            s: 100,
            v: 100,
            brightness: 100,
            temperature: 0,
            isColorMode: false,
          },
        },
        isManual: true,
        mood: undefined,
      };
      if (dps.hasOwnProperty(getGlobalParamsDp(dpKC.floodlight_lightness.key))) {
        deviceState.deviceData.deviceData.brightness = dps[getGlobalParamsDp(dpKC.floodlight_lightness.key)];
      }
      return deviceState;
    }, []);

    const manualDataObj2Dp = useCallback(
      (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
        const manualDps = {};
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
          if (apply.dp === getGlobalParamsDp(dpKC.floodlight_switch.key) && apply.enable && isSupportLightness()) {
            manualDps[getGlobalParamsDp(dpKC.floodlight_lightness.key)] = deviceState.deviceData.deviceData.brightness;
          }
        })
        return manualDps;
      },
      []
    );
    res.push({
      title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
      statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
      dp: {key: '', code: ''}, // schedule 没有dp值
      router: {
        key: RouterKey.ui_biz_time_schedule_new,
        params: {
          applyForList: cloneDeep(cameraApplyFor),
          applyForDisabled: false,
          manualDataDp2Obj,
          manualDataObj2Dp,
          isSupportColor: false,
          isSupportBrightness: isSupportLightness(),
          isSupportTemperature: false,
        } as TimeSchedulePageParams,
      },
    })
  }

  res.push({
    title: I18n.getLang('motion_detection_headline_text'),
    statusColor: getAdvancedStatusColor(state.motionDetectionStatus),
    router: {
      key: RouterKey.motion_detection,
    }
  });

  if (isSupportPatrolSwitch()) {
    res.push({
      title: I18n.getLang('camera_patrol'),
      statusColor: getAdvancedStatusColor(state.patrolSwitch ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      router: {
        key: RouterKey.patrol,
      }
    });
  }

  if (isSupportSitePointSet()) {
    res.push({
      title: I18n.getLang('camera_site'),
      router: {
        key: RouterKey.site,
      }
    });
  }

  res.push({
    title: I18n.getLang('camera_feature_1_headline'),
    statusColor: getAdvancedStatusColor(state.sirenSwitch ? AdvancedStatus.Enable : AdvancedStatus.Disable),
    router: {
      key: RouterKey.siren,
    }
  });

  res.push({
    title: I18n.getLang('camera_settings_talk_mode_topic'),
    router: {
      key: RouterKey.talk,
    }
  });

  res.push({
    title: I18n.getLang('camera_feature_5_headline'),
    router: {
      key: RouterKey.recordings,
    }
  });
  res.push({
    title: I18n.getLang('contact_sensor_specific_settings'),
    router: {
      key: RouterKey.settings,
    }
  });
  return res;
}
