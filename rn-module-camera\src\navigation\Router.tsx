import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from "../pages/home/<USER>";
import SirenPage from "../pages/SirenPage";
import TalkModePage from "../pages/TalkModePage";
import MotionDetectionPage from "../pages/motionDetection/MotionDetectionPage";
import {ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers";
import SettingsPage from "../pages/settings/SettingsPage";
import SDCardConfigPage from "../pages/SDCardConfigPage";
import NightVisionPage from "../pages/settings/NightVisionPage";
import RecordingModePage from "../pages/settings/RecordingModePage";
import NewTimeSchedulePageRouters from "@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router";
import NightVisionModePage from "../pages/settings/NightVisionModePage";
import RecordingsPage from "../pages/RecordingsPage";
import SelectPagePageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'
import OnvifPage from "../pages/settings/onvif/OnvifPage";
import OnvifIpTypePage from "../pages/settings/onvif/OnvifIpTypePage";
import OnvifPasswordResetPage from "../pages/settings/onvif/OnvifPasswordResetPage";
import AntiFlickerPage from "../pages/settings/AntiFlickerPage";
import PowerManagementSettingsPage from "../pages/settings/PowerManagementSettingsPage";
import PatrolPage from "../pages/PatrolPage";
import SitePage from "../pages/SitePage";
import AddSitePage from "../pages/AddSitePage";
import VolumePage from "../pages/settings/VolumePage";

export const RouterKey = {
  main: 'main',
  siren: 'siren',
  talk: 'talk',
  motion_detection: 'motion_detection',
  recordings: 'recordings',
  settings: 'settings',
  sd_card: 'sd_card',
  sd_card_config: 'sd_card_config',
  cloud_storage: 'cloud_storage',
  smart_phone: 'smart_phone',
  night_vision: 'night_vision',
  night_vision_mode: 'night_vision_mode',
  recording_mode: 'recording_mode',
  onvif: 'onvif',
  onvif_mode: 'onvif_mode',
  onvif_reset_password: 'onvif_reset_password',
  anti_flicker: 'anti_flicker',
  power_management_settings: 'power_management_settings',
  patrol: 'patrol',
  site: 'site',
  add_site: 'add_site',
  volume: 'volume',
  ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.siren,
    component: SirenPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.talk,
    component: TalkModePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.motion_detection,
    component: MotionDetectionPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.settings,
    component: SettingsPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.sd_card_config,
    component: SDCardConfigPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.night_vision,
    component: NightVisionPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.night_vision_mode,
    component: NightVisionModePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.recording_mode,
    component: RecordingModePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.recordings,
    component: RecordingsPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.onvif,
    component: OnvifPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.onvif_mode,
    component: OnvifIpTypePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.onvif_reset_password,
    component: OnvifPasswordResetPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.anti_flicker,
    component: AntiFlickerPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.power_management_settings,
    component: PowerManagementSettingsPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.patrol,
    component: PatrolPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.site,
    component: SitePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.add_site,
    component: AddSitePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.volume,
    component: VolumePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...SelectPagePageRouters,
  ...NewTimeSchedulePageRouters,
]
