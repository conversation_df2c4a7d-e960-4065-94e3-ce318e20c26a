import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'
import I18n from "@ledvance/base/src/i18n";
import res from "@ledvance/base/src/res/index";
import {useReactive, useUpdateEffect} from "ahooks";
import TextField from "@ledvance/base/src/components/TextField";
import TYIpcPlayer from "../components/TYIpcPlayer";
import {getDirectionRange, isWirless, useOperationSite, useSecurityMode} from "../hooks/DeviceHooks";
import xlog, {fullPlayerHeight, fullPlayerWidth} from "../utils/common";
import {View} from "react-native";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import Spacer from "@ledvance/base/src/components/Spacer";
import {SiteOperationType} from "../utils/SitePoint";
import {useNavigation} from '@react-navigation/native'
import {StreamStatus} from "../utils/StreamStatus";
import TYIpcPtz from '../components/TYIpcPtz';
import {CameraDirectionControl} from "../utils/CameraDirectionControl";

const {withTheme} = Utils.ThemeUtils
const cx = Utils.RatioUtils.convertX;
const AddSitePage = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation();
  const devInfo = useDeviceInfo();
  const [securityMode] = useSecurityMode();
  const [isSuccess, operationSite] = useOperationSite();
  const {deviceOnline} = devInfo;
  const directionRange = getDirectionRange();
  const state = useReactive({
    name: "",
    loading: false,
    showDirection: false,
    deviceOnline: deviceOnline === undefined ? true : deviceOnline,
    securityMode: securityMode,
  });

  useUpdateEffect(() => {
    state.securityMode = securityMode;
    state.deviceOnline = deviceOnline;
  }, [isSuccess, securityMode, deviceOnline]);

  useUpdateEffect(() => {
    if (isSuccess != undefined) {
      state.loading = false;
      navigation.goBack()
    }
  }, [isSuccess])

  const onChangeStreamStatus = async (status: number) => {
    xlog("onChangeStreamStatus======>", status)
    if (status === StreamStatus.VideoPlay) {
      state.showDirection = true;
    }
  };

  return (
    <Page backText={I18n.getLang('camera_site')}
          headlineText={I18n.getLang('camera_site_overview_empty_button_add_text')}
          loading={state.loading}
          rightButtonIcon={state.name.length > 0 && state.name.length <= 30 ? res.ic_check : res.ic_uncheck}
          rightButtonIconClick={async () => {
            if (state.name.length > 0 && state.name.length <= 30) {
              state.loading = true;
              await operationSite({
                type: SiteOperationType.Add,
                name: state.name,
              })
            }
          }}
    >

      <TextField
        style={{marginHorizontal: cx(24),}}
        value={state.name}
        showError={state.name?.length > 30}
        maxLength={31}
        errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}
        placeholder={I18n.getLang('add_new_trigger_time_inputfield_value_text')}
        onChangeText={(t: string) => {
          state.name = t;
        }}
      />

      <Spacer/>
      <View style={{marginHorizontal: cx(24), aspectRatio: 16 / 9, overflow: 'hidden'}}>
        <TYIpcPlayer
          isWirless={isWirless()}
          privateMode={state.securityMode}
          deviceOnline={state.deviceOnline}
          micTalkImage={-1}
          showOneWayMic={false}
          showTwoWayMic={false}
          playerProps={{
            scalable: false,
          }}
          hightScaleMode={true}
          onChangeStreamStatus={onChangeStreamStatus}
          onNormalScreenTapView={() => {
            xlog("onNormalScreenTapView============+>onNormalScreenTapView")
          }}
          onChangePreview={() => {
            xlog("onChangePreview============++>")
          }}
          getCloudCameraConfig={(data) => {
            xlog("getCloudCameraConfig=============>", JSON.stringify(data));
          }}
          initStatus={(data) => {
            xlog("initStatus=============>", JSON.stringify(data));
          }}

          onChangeRecording={(isRecording) => {
            xlog("onChangeRecording================>", isRecording);
          }}
          renderFullComArr={[
            {}
          ]}
          showCutScreen={false}
          showTimeInterval={false}
          fullPlayerHeight={fullPlayerHeight}
          fullPlayerWidth={fullPlayerWidth}
          isFullScreen={false}
          onChangeScreenOrientation={(value) => {
            xlog("onChangeScreenOrientation========>", value);
          }}
          onFullScreenTapView={(hideFullMenu) => {
            xlog("onFullScreenTapView========================>", hideFullMenu)
          }}
          onChangeZoomStatus={(zoomStatus) => {
            xlog("onChangeZoomStatus===============>", zoomStatus)
          }}
          onChangeActiveZoomStatus={(zoomStatusObj) => {
            const {zoomStatus} = zoomStatusObj;
            xlog("onChangeActiveZoomStatus===============>", zoomStatus)
          }}
          onListenIsTalking={(isTalking) => {
            xlog("onListenIsTalking=============>", isTalking)
          }}
          onListenTalkingChangeMute={(voiceStatus) => {
            xlog("onListenTalkingChangeMute================>", JSON.stringify(voiceStatus))
          }}
          onChangeSupportedMicWay={(data) => {
            xlog("onChangeSupportedMicWay=============>", JSON.stringify(data));
          }}
        />
      </View>
      <TYIpcPtz
        themeType={props.theme?.type || 'light'}
        hasPtzUp={directionRange.includes("0")}
        hasPtzDown={directionRange.includes("4")}
        hasPtzLeft={directionRange.includes("6")}
        hasPtzRight={directionRange.includes("2")}
        pressIn={(index) => {
          switch (index) {
            case 0:
              CameraDirectionControl.startPtzUp();
              break;
            case 1:
              CameraDirectionControl.startPtzRight();
              break;
            case 2:
              CameraDirectionControl.startPtzLeft();
              break;
            case 3:
              CameraDirectionControl.startPtzDown();
              break;
            default:
              break;
          }
        }}
        pressOut={() => {
          CameraDirectionControl.stopPtz();
        }}
      />
    </Page>
  )
}

export default withTheme(AddSitePage);