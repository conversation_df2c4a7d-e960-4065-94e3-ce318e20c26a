import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import React from "react";
import {Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'
import I18n from "@ledvance/base/src/i18n";
import ItemView from "../components/ItemView";
import {
  isSupportPatrolMode,
  isSupportPatrolTimeMode,
  usePatrolMode,
  usePatrolSwitch,
  usePatrolTime,
  usePatrolTimeMode,
  useSitePointData
} from "../hooks/DeviceHooks";
import {useReactive, useUpdateEffect} from "ahooks";
import {Text, View} from "react-native";
import LDVRadioGroup, {LDVRadioItemData} from "../components/LDVRadioGroup";
import {PatrolMode} from "../utils/PatrolMode";
import Spacer from "@ledvance/base/src/components/Spacer";
import {PatrolTimeMode} from "../utils/PatrolTimeMode";
import TimeRangeModal from "./modal/TimeRangeModal";
import {showDialog} from "@ledvance/base/src/utils/common";

const {toFixedString} = Utils.NumberUtils;
const {withTheme} = Utils.ThemeUtils
const cx = Utils.RatioUtils.convertX;
const getPatrolModeItemList = (): LDVRadioItemData[] => {
  return Object.entries(PatrolMode).map(([_, value]) => {
    const [title, description] = getPatrolModeItem(value);
    return {
      title: title,
      value: value,
      description: description,
    } as LDVRadioItemData
  });
}

const getPatrolTimeItemList = (startTime: string, endTime: string): LDVRadioItemData[] => {
  return Object.entries(PatrolTimeMode).map(([_, value]) => {
    const [title, description] = getPatrolTimeItem(value);
    let content = ''
    if (value == PatrolTimeMode.Timed) {
      const startTimeMinutes = getFormatTime(startTime) as number;
      const endTimeMinutes = getFormatTime(endTime) as number;
      const isNextDay = startTimeMinutes >= endTimeMinutes;
      content = `${I18n.formatValue('camera_patrol_cruise_time', startTime, endTime)} ${isNextDay ? I18n.getLang('scene_next_day') : ''}`
    }
    return {
      title: title,
      value: value,
      description: description,
      content: content,
    } as LDVRadioItemData
  });
}


const getPatrolModeItem = (value: string): [string, string] => {
  switch (value) {
    case PatrolMode.Site:
      return [I18n.getLang('camera_select_patrol_mode_site'), I18n.getLang('camera_select_patrol_mode_site_desc')];
    case PatrolMode.Panoramic:
    default:
      return [I18n.getLang('camera_select_patrol_mode_panoramic'), I18n.getLang('camera_select_patrol_mode_panoramic_desc')]
  }
}

const getPatrolTimeItem = (value: string): [string, string] => {
  switch (value) {
    case PatrolTimeMode.Timed:
      return [I18n.getLang('camera_set_patrol_time_timed'), I18n.getLang('camera_set_patrol_time_timed_desc')]
    case PatrolTimeMode.AllDay:
    default:
      return [I18n.getLang('camera_set_patrol_time_all_day'), I18n.getLang('camera_set_patrol_time_all_day_desc')];
  }
}

export const getFormatTime = (time: number | string) => {
  if (typeof time === 'number') {
    return `${toFixedString(Math.trunc(time / 60), 2)}:${toFixedString(time % 60, 2)}`;
  }
  const t = time.split(':');
  return Number(t[0]) * 60 + Number(t[1]);
}

const PatrolPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const [patrolSwitch, setPatrolSwitch] = usePatrolSwitch();
  const [patrolMode, setPatrolMode] = usePatrolMode();
  const [patrolTimeMode, setPatrolTimeMode] = usePatrolTimeMode();
  const [[startTime, endTime], setPatrolTime] = usePatrolTime();
  const [sitePointData] = useSitePointData();

  const state = useReactive({
    patrolMode: patrolMode,
    patrolTimeMode: patrolTimeMode,
    patrolSwitch: patrolSwitch,
    startTime: startTime,
    endTime: endTime,
    sitePointData: sitePointData,
    loading: false,
    showTimedPatrolModal: false,
    patrolModeItemList: getPatrolModeItemList(),
    patrolTimeItemList: getPatrolTimeItemList(startTime, endTime),
  });
  useUpdateEffect(() => {
    state.patrolSwitch = patrolSwitch;
    state.patrolMode = patrolMode;
    state.patrolTimeMode = patrolTimeMode;
    state.startTime = startTime;
    state.endTime = endTime;
    state.sitePointData = sitePointData;
  }, [patrolSwitch, patrolMode, startTime, endTime, patrolTimeMode, sitePointData]);

  useUpdateEffect(() => {
    state.patrolTimeItemList = getPatrolTimeItemList(state.startTime, state.endTime);
  }, [state.startTime, state.endTime]);


  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('camera_patrol')}
    >
      <View style={{marginHorizontal: cx(24)}}>
        <ItemView
          title={I18n.getLang('camera_patrol_enable')}
          switchValue={state.patrolSwitch}
          onSwitchChange={async (value) => {
            state.loading = true;
            await setPatrolSwitch(value);
            state.patrolSwitch = value;
            state.loading = false;
          }}/>

        {state.patrolSwitch && isSupportPatrolMode() && <View>
            <Spacer/>
            <Text style={{
              color: props.theme?.global.fontColor,
              fontSize: cx(16),
            }}>{I18n.getLang('camera_select_patrol_mode')}</Text>
            <Spacer height={cx(10)}/>
            <LDVRadioGroup
                data={state.patrolModeItemList}
                checkedValue={state.patrolMode}
                onCheckedChange={async (item: LDVRadioItemData) => {
                  if (item.value == PatrolMode.Site && state.sitePointData.length < 2) {
                    showDialog({
                      method: 'alert',
                      title: I18n.getLang('camera_errmsg_site_point_limit'),
                      confirmText: I18n.getLang('ceiling_fan_direction_info_button_label'),
                      onConfirm: (_, {close}) => {
                        close()
                      }
                    })
                    return
                  }
                  await setPatrolMode(item.value);
                  state.patrolMode = item.value;
                }}/>
            <Spacer/>
        </View>}


        {state.patrolSwitch && isSupportPatrolTimeMode() && <View>
            <Text style={{
              color: props.theme?.global.fontColor,
              fontSize: cx(16),
            }}>{I18n.getLang('camera_set_patrol_time')}</Text>
            <Spacer height={cx(10)}/>
            <LDVRadioGroup
                data={state.patrolTimeItemList}
                checkedValue={state.patrolTimeMode}
                onCheckedChange={async (item: LDVRadioItemData) => {
                  if (item.value == PatrolTimeMode.AllDay) {
                    state.loading = true;
                    await setPatrolTimeMode(item.value);
                    state.patrolMode = item.value;
                    state.loading = false;
                  } else {
                    state.showTimedPatrolModal = true;
                  }
                }}/>
            <Spacer/>
        </View>}
      </View>
      <TimeRangeModal
        visible={state.showTimedPatrolModal}
        startTime={getFormatTime(state.startTime) as number}
        endTime={getFormatTime(state.endTime) as number}
        onCancel={() => {
          state.showTimedPatrolModal = false;
        }}
        onConfirm={async (startTime, endTime) => {
          state.showTimedPatrolModal = false
          state.loading = true;
          state.startTime = getFormatTime(startTime) as string;
          state.endTime = getFormatTime(endTime) as string;
          await setPatrolTime(state.startTime, state.endTime);
          state.loading = false;
        }}/>
    </Page>
  )
}

export default withTheme(PatrolPage);