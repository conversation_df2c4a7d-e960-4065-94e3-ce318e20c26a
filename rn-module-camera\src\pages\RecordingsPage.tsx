import Page from "@ledvance/base/src/components/Page";
import React, {useCallback} from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {FlatList, View} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import {Utils} from "tuya-panel-kit";
import RecordingsCard from "../components/RecordingsCard";
import {checkIsGuestMode, toCameraAlbumPage, useRecordingsData} from "../hooks/DeviceHooks";
import {RouterKey} from "../navigation/Router";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import ThemeType from '@ledvance/base/src/config/themeType'

const { withTheme } = Utils.ThemeUtils

export interface RecordingsData {
    title: string
    router: { key: string, params?: any }
    icon: { uri: string} | number
}

const cx = Utils.RatioUtils.convertX;
const RecordingsPage = (props: { theme?: ThemeType }) => {
    const devInfo = useDeviceInfo();
    const recordingsData = useRecordingsData();
    const Header = useCallback(() => <Spacer height={cx(10)}/>, []);
    const Separator = useCallback(() => <Spacer/>, []);
    const Footer = useCallback(() => <Spacer height={cx(30)}/>, []);

    const handleRouterKey = useCallback((item) => {
        const theme = props.theme?.type === 'light' ? 2 : 1
        switch (item.router.key) {
            case RouterKey.cloud_storage:
                checkIsGuestMode().then((isGuestMode) => {
                    if (!isGuestMode) {
                        TYIpcPlayerManager.enterParamCloudBack({theme: theme, time: -1});
                    }
                })
                return;
            case RouterKey.sd_card:
                TYIpcPlayerManager.enterParamPlayBack({theme: theme});
                return;
            case RouterKey.smart_phone:
                toCameraAlbumPage(devInfo.devId);
                return;
            default:
                return;
        }
    }, [devInfo.devId, props.theme?.type]);
    const renderItem = useCallback(({item}) => {
        return (
            <View style={{flex: .5, justifyContent: 'center', alignItems: 'center',}}>
                <RecordingsCard
                    icon={item.icon}
                    title={item.title}
                    onPress={() => {
                        handleRouterKey(item);
                    }}/>
            </View>
        )
    }, [handleRouterKey])
    return (
        <Page backText={devInfo.name}
              headlineText={I18n.getLang('camera_feature_5_headline')}>
            <FlatList
                numColumns={2}
                style={{ marginHorizontal: cx(14)}}
                data={recordingsData}
                renderItem={renderItem}
                ListHeaderComponent={Header}
                ItemSeparatorComponent={Separator}
                ListFooterComponent={Footer}/>
        </Page>
    )
}

export default withTheme(RecordingsPage)
