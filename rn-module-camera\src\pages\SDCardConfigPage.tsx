import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import {Utils} from "tuya-panel-kit";
import {
    isSupportRecordLoop,
    useRecordLoop,
    useSDCardFormat,
    useSDCardStatus,
    useSDCardStorage
} from "../hooks/DeviceHooks";
import Spacer from "@ledvance/base/src/components/Spacer";
import {Text, View} from "react-native";
import ItemView from "../components/ItemView";
import DeleteButton from "@ledvance/base/src/components/DeleteButton";
import {showDialog} from "@ledvance/base/src/utils/common";
import {useReactive} from "ahooks";
import {SDCardStatus} from "../utils/SDCardStatus";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

const SDCardConfigPage = (props: { theme?: ThemeType }) => {
    const [totalStorage, usedStorage, availableStorage] = useSDCardStorage()
    const sdCardStatus = useSDCardStatus();
    const setSDCardFormat = useSDCardFormat();
    const [recordLoop, setRecordLoop] = useRecordLoop();
    const state = useReactive({
        totalStorage: totalStorage,
        usedStorage: usedStorage,
        availableStorage: availableStorage,
        recordLoop: recordLoop,
        usedParent: 0,
        formattingLoading: false,
    });

    useEffect(() => {
        state.formattingLoading = sdCardStatus === SDCardStatus.Formatting.valueOf();
        state.totalStorage = totalStorage;
        state.usedStorage = usedStorage;
        state.availableStorage = availableStorage;
        state.recordLoop = recordLoop;
        state.usedParent = (state.usedStorage / state.totalStorage) * 100;
    }, [sdCardStatus, totalStorage, usedStorage, availableStorage, recordLoop]);

    return (
        <Page backText={I18n.getLang('contact_sensor_specific_settings')}
              headlineText={I18n.getLang('camera_settings_sd_storage_topic')}
              loading={state.formattingLoading}
        >
            <View style={{marginHorizontal: cx(24)}}>
                <Spacer/>
                <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                    <Text style={{color: props.theme?.global.fontColor}}>{`${(state.usedStorage / 1024 / 1024).toFixed(2)}GB`}</Text>
                    <Text style={{color: props.theme?.global.fontColor}}>{`${(state.totalStorage / 1024 / 1024).toFixed(2)}GB`}</Text>
                </View>
                <View style={{height: 35, width: '100%', backgroundColor: props.theme?.card.head, borderRadius: 8,overflow:'hidden'}}>
                    <View style={{height: 35, width: `${state.usedParent.toFixed(2)}%`, backgroundColor: props.theme?.icon.primary}}/>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'center'}}>
                    <Text style={{
                        fontSize: cx(40),
                        fontWeight: '300',
                        color: props.theme?.global.fontColor
                    }}>{`${state.usedParent.toFixed(2)}%`}</Text>
                    <Text style={{fontSize: cx(14), color: props.theme?.global.fontColor, paddingBottom: cx(8)}}>{I18n.getLang('camera_settings_sd_storage_used_storage_percent')}</Text>
                </View>
                <Spacer height={cx(40)}/>
                {isSupportRecordLoop() && <ItemView
                    title={I18n.getLang('camera_settings_sd_storage_firstbox_text')}
                    description={I18n.getLang('camera_settings_sd_storage_firstbox_note')}
                    switchValue={state.recordLoop}
                    onSwitchChange={async (value) => {
                        await setRecordLoop(value);
                        state.recordLoop = value;
                    }}
                />}

                {isSupportRecordLoop() && <Spacer height={cx(40)}/>}

                <DeleteButton text={I18n.getLang('camera_settings_button_text')} onPress={() => {
                    showDialog({
                        method: 'confirm',
                        title: I18n.getLang('camera_settings_format_sd_card_topic'),
                        subTitle: I18n.getLang('camera_settings_format_sd_card_note'),
                        cancelText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_no_text'),
                        confirmText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_yes_text'),
                        onConfirm: async (_, {close}) => {
                            close()
                            state.formattingLoading = true
                            await setSDCardFormat(true);
                        },
                    });
                }}/>

            </View>
        </Page>
    )
}

export default withTheme(SDCardConfigPage)
