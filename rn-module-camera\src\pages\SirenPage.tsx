import React from "react";
import ItemView from "../components/ItemView";
import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {useReactive, useUpdateEffect} from "ahooks";
import {
  isSupportSirenDuration,
  isSupportSirenVolume,
  useSirenDuration,
  useSirenSwitch,
  useSirenVolume
} from "../hooks/DeviceHooks";
import {Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import Card from "@ledvance/base/src/components/Card";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import ThemeType from "@ledvance/base/src/config/themeType";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const SirenPage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo();
  const [sirenSwitch, setSirenSwitch] = useSirenSwitch();
  const [sirenVolume, setSirenVolume] = useSirenVolume();
  const [sirenDuration, setSirenDuration] = useSirenDuration();
  const state = useReactive({
    loading: false,
    sirenSwitch: sirenSwitch,
    sirenVolume: sirenVolume,
    sirenDuration: sirenDuration,
  });

  useUpdateEffect(() => {
    state.sirenSwitch = sirenSwitch;
    state.sirenVolume = sirenVolume;
    state.sirenDuration = sirenDuration;
  }, [sirenSwitch, sirenVolume, sirenDuration])
  return (<Page
    backText={devInfo.name}
    headlineText={I18n.getLang('camera_feature_1_headline')}
    loading={state.loading}
  >
    <ItemView
      title={I18n.getLang('motion_detection_with_safe_mode_subheadline3_text')}
      style={{marginHorizontal: cx(24)}}
      switchValue={state.sirenSwitch}
      onSwitchChange={async (value) => {
        state.loading = true;
        await setSirenSwitch(value);
        state.loading = false;
        state.sirenSwitch = value;
      }}
    />
    <Spacer/>
    {isSupportSirenVolume() && <Card style={{marginHorizontal: cx(24)}}
                                     containerStyle={{flexDirection: 'column'}}>
        <LdvSlider
            title={I18n.getLang('siren_volume')}
            titleStyle={{
              fontSize: cx(16),
              fontWeight: 'bold',
              maxWidth: cx(220),
              color: props.theme?.global.fontColor
            }}
            subTitleStr={`${state.sirenVolume}%`}
            value={state.sirenVolume}
            max={100}
            min={0}
            onValueChange={(value) => {
              state.sirenVolume = value;
            }}
            style={{paddingBottom: cx(20), paddingTop: cx(10)}}
            onSlidingComplete={(value) => {
              setSirenVolume(value).then()
              state.sirenVolume = value;
            }}/>
    </Card>}
    <Spacer/>
    {isSupportSirenDuration() && <Card style={{marginHorizontal: cx(24)}}
                                       containerStyle={{flexDirection: 'column'}}>
        <LdvSlider
            title={I18n.getLang('siren_duration')}
            titleStyle={{
              fontSize: cx(16),
              fontWeight: 'bold',
              maxWidth: cx(220),
              color: props.theme?.global.fontColor
            }}
            subTitleStr={`${(state.sirenDuration * 10)}s`}
            value={state.sirenDuration}
            max={60}
            min={1}
            onValueChange={(value) => {
              state.sirenDuration = value;
            }}
            style={{paddingBottom: cx(20), paddingTop: cx(10)}}
            onSlidingComplete={(value) => {
              setSirenDuration(value).then()
              state.sirenDuration = value;
            }}/>
    </Card>}
  </Page>)
}

export default withTheme(SirenPage)