import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import React, {useCallback} from "react";
import {Dialog, Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'
import I18n from "@ledvance/base/src/i18n";
import res from "@ledvance/base/src/res/index";
import {useReactive, useUpdateEffect} from "ahooks";
import {useOperationSite, usePatrolSwitch, useSitePointData} from "../hooks/DeviceHooks";
import {cloneDeep} from "lodash";
import {SiteOperationType, SitePoint} from "../utils/SitePoint";
import Spacer from "@ledvance/base/src/components/Spacer";
import {FlatList, Image, ScrollView, StyleSheet, Text, TouchableOpacity, View} from "react-native";
import {showDialog} from "@ledvance/base/src/utils/common";
import InfoText from "@ledvance/base/src/components/InfoText";
import Strings from "@ledvance/base/src/i18n/index";
import DeleteButton from "@ledvance/base/src/components/DeleteButton";
import {useNavigation} from "@react-navigation/core";
import {RouterKey} from "../navigation/Router";

const {withTheme} = Utils.ThemeUtils
const cx = Utils.RatioUtils.convertX;


const MAX_SITE_COUNT = 6;

const SitePage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const [patrolSwitch] = usePatrolSwitch();
  const navigation = useNavigation();
  const [sitePointList, updateSitePoint, loading, setRefresh] = useSitePointData();
  const [isSuccess, operationSite] = useOperationSite();
  const state = useReactive({
    loading: false,
    isClicked: false,
    patrolSwitch: patrolSwitch,
    operationType: SiteOperationType.Add,
    sitePointList: cloneDeep(sitePointList),
  });

  useUpdateEffect(() => {
    if (isSuccess != undefined) {
      state.loading = false;
      if (state.operationType != SiteOperationType.Position) {
        setRefresh(isSuccess);
      }
    }
    state.patrolSwitch = patrolSwitch;
  }, [isSuccess, patrolSwitch]);

  useUpdateEffect(() => {
    state.loading = loading;
    state.sitePointList = cloneDeep(sitePointList);
  }, [JSON.stringify(sitePointList), loading]);

  const styles = StyleSheet.create({
    item: {
      flexDirection: 'row',
      paddingVertical: cx(5),
      alignItems: 'center',
      justifyContent: 'center',
    },
    editIcon: {
      width: cx(24),
      height: cx(24),
      tintColor: props.theme?.icon.normal,
      marginEnd: cx(20)
    },
    deleteIcon: {width: cx(24), height: cx(24), tintColor: props.theme?.icon.normal},
    emptyContainer: {
      marginHorizontal: cx(24),
      alignItems: 'center',
    },
    addBtn: {
      height: cx(40),
      width: 'auto',
      minWidth: cx(150),
      paddingHorizontal: cx(16),
      backgroundColor: props.theme?.button.primary,
    },
    emptyImage: {
      width: cx(225),
      height: cx(198),
    },
  });

  const showErrorDialog = useCallback(() => {
    if (state.isClicked) return;
    state.isClicked = true;
    showDialog({
      method: 'alert',
      title: I18n.getLang('camera_operation_site_error'),
      confirmText: I18n.getLang('ceiling_fan_direction_info_button_label'),
      onConfirm: async (_, {close}) => {
        close();
        state.isClicked = false;
      }
    });
  }, []);

  const addOrEditSiteDialog = (isEdit: boolean = false, sitePoint?: SitePoint) => {
    if (state.isClicked) return
    state.isClicked = true;
    Dialog.prompt({
      title: I18n.getLang(isEdit ? 'camera_edit_site_name' : 'camera_site_name'),
      value: isEdit ? sitePoint?.name : '',
      cancelText: I18n.getLang('auto_scan_system_cancel'),
      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
      inputWrapperStyle: {backgroundColor: props.theme?.textInput.background, borderRadius: cx(10)},
      onChangeText: text => {
        return text.length <= 30 ? text : text.slice(0, 30)
      },
      motionConfig: {hideDuration: 0},
      onConfirm: async (text, {close}) => {
        if (text.length <= 0) {
          return
        }
        state.isClicked = false;
        close()
        if (isEdit && sitePoint) {
          state.loading = true;
          await updateSitePoint(sitePoint.id, text);
          state.sitePointList = state.sitePointList.map(site => {
            if (site.id == sitePoint.id) {
              site.name = text;
            }
            return site
          });
          state.loading = false;
        } else if (!isEdit) {
          state.loading = true;
          state.operationType = SiteOperationType.Add;
          await operationSite({
            type: SiteOperationType.Add,
            name: text,
          })
        }
      },
    }, {
      onDismiss: () => {
        state.isClicked = false;
      }
    });
  }

  const showDeleteSiteDialog = useCallback((sitePoint: SitePoint) => {
    if (state.isClicked) return;
    state.isClicked = true;
    showDialog({
      method: 'confirm',
      title: I18n.getLang('camera_site_delete_dialog_title'),
      cancelText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_no_text'),
      confirmText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_yes_text'),
      onConfirm: async (_, {close}) => {
        state.isClicked = false;
        close()
        state.loading = true;
        state.operationType = SiteOperationType.Delete;
        await operationSite({type: SiteOperationType.Delete, sitePoint: sitePoint});
      },
      option2: {
        onDismiss: () => {
          state.isClicked = false;
        }
      }
    });
  }, []);

  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('camera_site')}
          headlineIcon={state.sitePointList.length < MAX_SITE_COUNT ? res.add : undefined}
          onHeadlineIconClick={() => {
            if (state.patrolSwitch) {
              showErrorDialog();
              return
            }
            navigation.navigate(RouterKey.add_site);
          }}>
      <ScrollView>
        <View style={{marginHorizontal: cx(24)}}>
          {state.sitePointList.length >= MAX_SITE_COUNT && (
            <View>
              <Spacer height={cx(10)}/>
              <InfoText
                icon={res.ic_warning_amber}
                text={Strings.getLang('camera_site_overview_warning_max_number_text')}
                contentColor={props.theme?.global.warning}
              />
              <Spacer height={cx(6)}/>
            </View>
          )}
          {state.sitePointList.length > 0 ? <FlatList
            scrollEnabled={false}
            data={state.sitePointList}
            renderItem={({item}) => {
              return (
                <View>
                  <TouchableOpacity onPress={() => {
                    state.loading = true;
                    state.operationType = SiteOperationType.Position;
                    operationSite({
                      type: SiteOperationType.Position,
                      sitePoint: item
                    }).then()
                  }}>
                    <View style={styles.item}>
                      <Image style={{width: cx(124), height: cx(72)}} source={{uri: item.pic}}/>
                      <Text style={{
                        flex: 1,
                        marginHorizontal: cx(5)
                      }}>{item.name}</Text>
                      <TouchableOpacity onPress={() => {
                        if (state.patrolSwitch) {
                          showErrorDialog();
                          return
                        }
                        addOrEditSiteDialog(true, item);
                      }}>
                        <Image
                          source={{uri: res.icon_edit_scene}}
                          style={styles.editIcon}/>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => {
                        if (state.patrolSwitch) {
                          showErrorDialog();
                          return
                        }
                        showDeleteSiteDialog(item);
                      }}>
                        <Image
                          source={{uri: res.delete}}
                          style={styles.deleteIcon}/>
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                  <View style={{height: cx(1), backgroundColor: props.theme?.dialog.lineColor}}/>
                </View>
              );
            }}
            ListHeaderComponent={() => <Spacer height={cx(10)}/>}
            ListEmptyComponent={() => <Spacer/>}
            ItemSeparatorComponent={() => <Spacer/>}
            ListFooterComponent={() => <Spacer/>}
            keyExtractor={item => `${item.id}_${item.mpId}`}
          /> : <View style={styles.emptyContainer}>
            <Spacer height={cx(60)}/>
            <Image
              style={styles.emptyImage}
              source={{uri: res.ldv_timer_empty}}
              resizeMode="contain"
            />
            <InfoText
              icon={res.device_panel_schedule_alert}
              text={I18n.getLang('camera_site_overview_empty_information_text')}
              style={{width: 'auto', alignItems: 'center'}}
              textStyle={{color: props.theme?.global.fontColor, flex: undefined}}
              iconStyle={{tintColor: props.theme?.global.fontColor}}
            />
            <Spacer height={cx(16)}/>
            {!state.sitePointList.length && <DeleteButton
                style={styles.addBtn}
                text={`${I18n.getLang('camera_site_overview_empty_button_add_text')}`}
                textStyle={{fontSize: cx(12)}}
                onPress={() => {
                  if (state.patrolSwitch) {
                    showErrorDialog();
                    return
                  }
                  navigation.navigate(RouterKey.add_site);
                }}
            />}
          </View>}
          <Spacer height={cx(40)}/>
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(SitePage);