import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import LDVRadioGroup, {LDVRadioItemData} from "../components/LDVRadioGroup";
import {useReactive, useUpdateEffect} from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import {Storage} from "../utils/Storage";
import {StorageKey} from "../utils/StorageKey";
import {Utils} from "tuya-panel-kit";
import {Text} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import xlog from "../utils/common";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

const OneWayItem: LDVRadioItemData = {
    title: I18n.getLang('camera_settings_talk_mode_firstbox_option1_topic'),
    value: I18n.getLang('camera_settings_talk_mode_firstbox_option1_topic'),
    description: I18n.getLang('camera_settings_talk_mode_firstbox_option1_description')
}
const TwoWayItem: LDVRadioItemData = {
    title: I18n.getLang('camera_settings_talk_mode_firstbox_option2_topic'),
    value: I18n.getLang('camera_settings_talk_mode_firstbox_option2_topic'),
    description: I18n.getLang('camera_settings_talk_mode_firstbox_option2_description')
}

const TalkModePage = (props: { theme?: ThemeType }) => {
    const dev = useDeviceInfo();
    const state = useReactive({
        checkedValue: TwoWayItem.value,
        isTwoWayTalkMode: true,
    });

    useEffect(() => {
        Storage.getBoolean(StorageKey.wayTalkModeKey(dev.devId), true).then((value) => {
            state.isTwoWayTalkMode = value;
            state.checkedValue = value ? TwoWayItem.value : OneWayItem.value;
            xlog("useEffect isTwoWayTalkMode and checkedValue=========>", state.isTwoWayTalkMode, state.checkedValue)
        });
    }, []);

    useUpdateEffect(() => {
        state.checkedValue = state.isTwoWayTalkMode ? TwoWayItem.value : OneWayItem.value;
    }, [state.isTwoWayTalkMode])

    return (
        <Page backText={dev.name}
              headlineText={I18n.getLang('camera_settings_talk_mode_topic')}
        >
            <Spacer height={cx(10)}/>
            <Text style={{
                marginHorizontal: cx(24),
                color: props.theme?.global.fontColor,
                fontSize: cx(14)
            }}>{I18n.getLang('camera_settings_talk_mode_secondtopic')}</Text>
            <Spacer/>
            <LDVRadioGroup
                style={{marginHorizontal: cx(24)}}
                data={[OneWayItem, TwoWayItem]}
                checkedValue={state.checkedValue}
                onCheckedChange={async (item: LDVRadioItemData) => {
                    Storage.setBoolean(StorageKey.wayTalkModeKey(dev.devId), item.value === TwoWayItem.value);
                    state.isTwoWayTalkMode = item.value === TwoWayItem.value;
                }}/>
        </Page>
    )
}

export default withTheme(TalkModePage)
