import {Image, ImageSourcePropType, TouchableOpacity, ViewProps} from "react-native";
import React from "react";
import {Utils} from "tuya-panel-kit";

const cx = Utils.RatioUtils.convertX;

interface CameraControlImageProps extends ViewProps {
    source: ImageSourcePropType;
    onPress?: () => void;
    visible?: boolean
}

export default function CameraControlImage(props: CameraControlImageProps) {
    const {source, onPress, visible} = props;
    const isShow: boolean = visible === undefined ? true : visible
    return (<TouchableOpacity onPress={() => {
        if (isShow) {
            onPress();
        }
    }} style={props.style}>
        <Image source={source}
               style={{
                   width: cx(24),
                   height: cx(24),
                   resizeMode: 'contain',
                   backgroundColor: '#00000033',
                   opacity: isShow ? 100 : 0,
               }}
        />
    </TouchableOpacity>)
}