import {Image, StyleSheet, TouchableOpacity, View} from "react-native";
import React from "react";
import {Utils} from "tuya-panel-kit";
import cameraRes from "@res";
import {CameraDirectionControl} from "../../utils/CameraDirectionControl";
import {getDirectionRange} from "../../hooks/DeviceHooks";

interface CameraDirectionViewProps {
  showDirection: boolean,
  onControlling: (isControlling: boolean) => void,
  onShowDirection: () => void,
}

const cx = Utils.RatioUtils.convertX;
export default function CameraDirectionView(props: CameraDirectionViewProps) {
  const {showDirection, onControlling, onShowDirection} = props;
  const directionRange = getDirectionRange();
  return (<View style={{
    position: 'absolute',
    bottom: 0,
    top: 0,
    right: 0,
    left: 0,
    alignItems: 'center',
    justifyContent: 'center',
  }}>
    <TouchableOpacity onPress={() => {
      onShowDirection()
    }} style={{width: '60%', height: '60%'}}/>
    {showDirection && <View style={{
      position: 'absolute',
      bottom: 0,
      top: 0,
      right: 0,
      left: 0,
      alignItems: 'center',
      justifyContent: 'center',
    }}>
      {/*TOP*/}
      {directionRange.includes("0") && <TouchableOpacity
          onPressIn={() => {
            onControlling(true);
            CameraDirectionControl.startPtzUp();
          }}
          onPressOut={() => {
            onControlling(false);
            CameraDirectionControl.stopPtz();
          }}
          style={[styles.directionParent, {
            transform: [{translateX: -cx(12)}, {translateY: -cx(72)}, {rotate: '90deg'}],
          }]}>
          <Image source={cameraRes.camera_direction} style={[styles.directionIcon, {}]}/>
      </TouchableOpacity>}
      {/*RIGHT*/}
      {directionRange.includes("2") && <TouchableOpacity
          style={[styles.directionParent, {
            transform: [{translateX: cx(48)}, {translateY: -cx(12)}, {rotate: '180deg'}],
          }]}
          onPressIn={() => {
            onControlling(true);
            CameraDirectionControl.startPtzRight();
          }}
          onPressOut={() => {
            onControlling(false);
            CameraDirectionControl.stopPtz();
          }}>
          <Image source={cameraRes.camera_direction} style={[styles.directionIcon]}/>
      </TouchableOpacity>}
      {/*BOTTOM*/}
      {directionRange.includes("4") && <TouchableOpacity
          style={[styles.directionParent, {
            transform: [{translateX: -cx(12)}, {translateY: cx(48)}, {rotate: '270deg'}],
          }]}
          onPressIn={() => {
            onControlling(true);
            CameraDirectionControl.startPtzDown();
          }}
          onPressOut={() => {
            onControlling(false);
            CameraDirectionControl.stopPtz();
          }}>
          <Image source={cameraRes.camera_direction} style={[styles.directionIcon, {}]}/>
      </TouchableOpacity>}
      {/*LEFT*/}
      {directionRange.includes("6") && <TouchableOpacity
          style={[styles.directionParent, {
            transform: [{translateX: -cx(72)}, {translateY: -cx(12)}],
          }]}
          onPressIn={() => {
            onControlling(true);
            CameraDirectionControl.startPtzLeft();
          }}
          onPressOut={() => {
            onControlling(false);
            CameraDirectionControl.stopPtz();
          }}>
          <Image source={cameraRes.camera_direction} style={[styles.directionIcon, {}]}/>
      </TouchableOpacity>}
    </View>}

  </View>)
}
const styles = StyleSheet.create({
  directionParent: {
    position: 'absolute',
    left: '50%',
    top: '50%',
    width: cx(24),
    height: cx(24),
  },
  directionIcon: {
    resizeMode: 'contain',
    width:cx(24),
    height:cx(24),
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  }
})
