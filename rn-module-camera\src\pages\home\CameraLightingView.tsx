import React, {useEffect} from "react";
import Card from "@ledvance/base/src/components/Card";
import {Utils} from "tuya-panel-kit";
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import {useReactive} from "ahooks";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import {isSupportFloodlightLightness, useLightness, useLightSwitch} from "../../hooks/DeviceHooks";
import {View} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface CameraLightingViewProp extends LoadingCallback {
  theme?: ThemeType
}

export default withTheme(function CameraLightingView(props: CameraLightingViewProp) {
    const {onLoading} = props;
    const [lightSwitch, setLightSwitch] = useLightSwitch();
    const [lightness, setLightness] = useLightness();
    const state = useReactive({
        lightSwitch: lightSwitch,
        lightness: lightness
    });

    useEffect(() => {
        state.lightSwitch = lightSwitch;
    }, [lightSwitch]);

    useEffect(() => {
        state.lightness = lightness;
    }, [lightness]);

    return (
        <View>
            <Spacer/>
            <Card style={{marginHorizontal: cx(24)}}
                  containerStyle={{flexDirection: 'column'}}>
                <LdvSwitch
                    title={I18n.getLang('camera_tile_dim_lighting_headline')}
                    color={props.theme?.card.background}
                    colorAlpha={0}
                    enable={state.lightSwitch}
                    setEnable={async (value) => {
                        onLoading && onLoading(true);
                        await setLightSwitch(value);
                        onLoading && onLoading(false);
                        state.lightSwitch = value;
                    }}/>

              {state.lightSwitch && isSupportFloodlightLightness() && <LdvSlider
                  title={I18n.getLang('add_new_trigger_time_field_brightness_text')}
                  value={state.lightness}
                  style={{paddingBottom: cx(20)}}
                  onSlidingComplete={(value) => {
                    setLightness(value).then()
                    state.lightness = value;
                  }}/>}
            </Card>
        </View>)
})
