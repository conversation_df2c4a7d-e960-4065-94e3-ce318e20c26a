import {View} from "react-native";
import React, {useCallback, useEffect} from "react";
import {Utils} from "tuya-panel-kit";
import res from "@ledvance/base/src/res";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useReactive} from "ahooks";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import CameraControlImage from "./CameraControlImage";
import {Storage} from "../../utils/Storage";
import {StorageKey} from "../../utils/StorageKey";
import xlog from "../../utils/common";
import {useDeviceId} from "@ledvance/base/src/models/modules/NativePropsSlice";

const cx = Utils.RatioUtils.convertX;

export interface NormalPlayerBottomLeftProps {
    isHD: boolean,
    isMuting: boolean,
    isTalkBacking: boolean,
    isSupportMic: boolean,
    onHdChange?: (enableHd: boolean) => void,
    onIsMutingChange?: (isMuting: boolean) => void,
    onIsTalkBackingChange?: (isTalkBacking: boolean) => void,
}

export default function CameraPreviewBottomFunction(props: NormalPlayerBottomLeftProps) {
    const {isHD, isMuting, isTalkBacking, isSupportMic} = props;
    const deviceId = useDeviceId();
    const state = useReactive({
        isMuting: isMuting,
        isHD: isHD,
        isTalkBacking: isTalkBacking,
        isSupportMic: isSupportMic,
    });
    useEffect(() => {
        state.isMuting = isMuting;
        state.isHD = isHD;
        state.isTalkBacking = isTalkBacking;
        state.isSupportMic = isSupportMic;
    }, [isMuting, isHD, isTalkBacking, isSupportMic]);

    const setClarityAction = useCallback((isHD: boolean) => {
        TYIpcPlayerManager.enableHd(isHD ? 'HD' : 'SD').then(data => {
            console.log("setClarityAction =========>", isHD, JSON.stringify(data));
            // @ts-ignore
            const {success} = data;
            if (success) {
                state.isHD = isHD;
            }
            props.onHdChange && props.onHdChange(isHD);
        });
    }, []);

    const setIsMutingAction = useCallback((isMuting: boolean) => {
        TYIpcPlayerManager.enableMute(isMuting ? 'OFF' : 'ON').then(data => {
            console.log("setIsMutingAction =========>", isMuting, JSON.stringify(data));
            props.onIsMutingChange && props.onIsMutingChange(isMuting);
        });
    }, []);

    const setTalkBacking = useCallback(async (enable: boolean) => {
        const isTwoWayTalkMode = await Storage.getBoolean(StorageKey.wayTalkModeKey(deviceId), true)
        xlog("setTalkBacking============>", isTwoWayTalkMode)
        if (enable) {
            TYIpcPlayerManager.enableStartTalk(isTwoWayTalkMode)
        } else {
            TYIpcPlayerManager.enableStopTalk(isTwoWayTalkMode)
        }
        props.onIsTalkBackingChange && props.onIsTalkBackingChange(enable);
    }, []);

    return (<View style={{
        position: 'absolute',
        bottom: 0,
        right: cx(12),
        left: cx(12),
        height: cx(71),
        flexDirection: 'column',
    }}>
        <CameraControlImage
            visible={state.isSupportMic}
            source={{uri: state.isTalkBacking ? res.mic_on : res.mic_off}}
            onPress={() => {
                setTalkBacking(!state.isTalkBacking).then()
            }}/>
        <View style={{flexDirection: 'row', height: cx(47), alignItems: 'center',}}>
            <CameraControlImage
                source={{ uri: state.isMuting ? res.volume_off : res.volume_on}}
                onPress={() => {
                    setIsMutingAction(!state.isMuting);
                }}/>

            <CameraControlImage
                source={{ uri: state.isHD ? res.clarity_hd : res.clarity_sd}}
                onPress={() => {
                    setClarityAction(!state.isHD);
                }}
                style={{marginStart: cx(24)}}
            />
            <Spacer style={{flex: 1}}/>
            <CameraControlImage
                source={{ uri: res.screen_full}}
                onPress={() => {
                    TYIpcPlayerManager.setScreenOrientation(1);
                }}
                style={{marginStart: cx(24)}}
            />
        </View>
    </View>)
}
