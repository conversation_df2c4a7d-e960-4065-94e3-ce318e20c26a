import {View} from "react-native";
import React, {useCallback} from "react";
import {Utils} from "tuya-panel-kit";
import res from "@ledvance/base/src/res";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useReactive, useUpdateEffect} from "ahooks";

const cx = Utils.RatioUtils.convertX;
import xlog, {fullPlayerWidth, realFullPlayerWidth} from "../../utils/common";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import CameraControlImage from "./CameraControlImage";
import {Storage} from "../../utils/Storage";
import {StorageKey} from "../../utils/StorageKey";
import {useDeviceId} from "@ledvance/base/src/models/modules/NativePropsSlice";

export interface CameraPreviewFullscreenBottomFunctionProps {
    isHD: boolean,
    isMuting: boolean,
    isTalkBacking: boolean,
    isSupportMic: boolean,
    onHdChange?: (enableHd: boolean) => void,
    onIsMutingChange?: (isMuting: boolean) => void,
    onIsTalkBackingChange?: (isTalkBacking: boolean) => void,
}

export default function CameraPreviewFullscreenBottomFunction(props: CameraPreviewFullscreenBottomFunctionProps) {
    xlog("CameraPreviewFullscreenBottomFunction==============>", JSON.stringify(props))
    const {isHD, isMuting, isTalkBacking, isSupportMic} = props;
    const deviceId = useDeviceId();
    const state = useReactive({
        isHD: isHD,
        isMuting: isMuting,
        isTalkBacking: isTalkBacking,
        isSupportMic: isSupportMic,
    });
    useUpdateEffect(() => {
        state.isHD = isHD;
        state.isMuting = isMuting;
        state.isTalkBacking = isTalkBacking;
        state.isSupportMic = isSupportMic;
    }, [isHD, isMuting, isTalkBacking, isSupportMic]);

    const setClarityAction = useCallback((isHD: boolean) => {
        TYIpcPlayerManager.enableHd(isHD ? 'HD' : 'SD').then(data => {
            console.log("setClarityAction =========>", isHD, JSON.stringify(data));
            // @ts-ignore
            const {success} = data;
            if (success) {
                state.isHD = isHD;
            }
            props.onHdChange && props.onHdChange(isHD);
        });
    }, []);

    const setTalkBacking = useCallback( async (enable: boolean) => {
        const isTwoWayTalkMode = await Storage.getBoolean(StorageKey.wayTalkModeKey(deviceId), true)
        xlog("setTalkBacking============>", isTwoWayTalkMode)
        if (enable) {
            TYIpcPlayerManager.enableStartTalk(isTwoWayTalkMode)
        } else {
            TYIpcPlayerManager.enableStopTalk(isTwoWayTalkMode)
        }
        props.onIsTalkBackingChange && props.onIsTalkBackingChange(enable);
    }, []);

    const setIsMutingAction = useCallback((isMuting: boolean) => {
        TYIpcPlayerManager.enableMute(isMuting ? 'OFF' : 'ON').then(data => {
            xlog("setIsMutingAction =========>", isMuting, JSON.stringify(data));
            // state.isMuting = isMuting;
            props.onIsMutingChange && props.onIsMutingChange(isMuting);
        });
    }, []);

    return (<View style={{
        position: 'absolute',
        left: (fullPlayerWidth - realFullPlayerWidth) / 2 + cx(20),
        right: (fullPlayerWidth - realFullPlayerWidth) / 2 + cx(20),
        bottom: 0,
        height: cx(80),
        flexDirection: 'column'
    }}>
        <CameraControlImage
            visible={state.isSupportMic}
            source={{ uri: state.isTalkBacking ? res.mic_on : res.mic_off}}
            onPress={() => {
                setTalkBacking(!state.isTalkBacking).then()
            }}/>
        <View style={{flexDirection: 'row', alignItems: 'center', height: cx(50), marginTop: cx(6)}}>
            <CameraControlImage
                source={{ uri: state.isMuting ? res.volume_off : res.volume_on}}
                onPress={() => {
                    setIsMutingAction(!state.isMuting)
                }}/>
            <CameraControlImage
                source={{ uri: state.isHD ? res.clarity_hd : res.clarity_sd}}
                onPress={() => {
                    setClarityAction(!state.isHD);
                }}
                style={{marginStart: cx(24)}}
            />
            <Spacer style={{flex: 1}}/>
            <CameraControlImage
                source={{ uri: res.screen_normal}}
                onPress={() => {
                    TYIpcPlayerManager.setScreenOrientation(0);
                }}/>
        </View>
    </View>)
}
