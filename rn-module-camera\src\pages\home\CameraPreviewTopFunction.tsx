import {Image, Text, View} from "react-native";
import React, {useEffect} from "react";
import {Battery, Utils} from "tuya-panel-kit";
import res from "@ledvance/base/src/res";
import {useReactive, useUpdateEffect} from "ahooks";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import ThemeType from "@ledvance/base/src/config/themeType";
import {isSupportWirelessElectricity, useWirelessElectricity} from "../../hooks/DeviceHooks";

const {withTheme} = Utils.ThemeUtils

const cx = Utils.RatioUtils.convertX;
export default withTheme(function CameraPreviewTopFunction(_: { theme?: ThemeType }) {
  const electricity = useWirelessElectricity();
  const state = useReactive({
    wifiSignal: '100',
    enableElectricity:false,
    electricity: electricity
  });
  useUpdateEffect(() => {
    state.electricity = electricity;
  }, [electricity])
  useEffect(() => {
    fetchWifiSignal();
    const interval = setInterval(() => {
      fetchWifiSignal();
    }, 5000);
    return () => {
      clearInterval(interval)
    };
  }, []);

  const fetchWifiSignal = () => {
    TYIpcPlayerManager.getWifiSignal().then(res => {
      const result = res as { signalValue: string | number }
      state.wifiSignal = `${result.signalValue || '0'}`
    }).catch(() => {
      state.wifiSignal = '0'
    });
  }

  const calcColor = (_, normalColor, lowColor, emptyColor) => {
    if (state.electricity >= 70) {
      return normalColor;
    } else if (state.electricity >= 30) {
      return lowColor;
    } else {
      return emptyColor;
    }
  };


  return (<View style={{
    position: 'absolute',
    top: cx(10),
    right: cx(12),
    left: cx(12),
    flexDirection: 'row',
    alignItems: 'center'
  }}>

    {isSupportWirelessElectricity() && state.enableElectricity && <View style={{
      marginTop: cx(5),
      padding: cx(5),
      alignItems: 'center',
      width: cx(50),
      flexDirection: 'row',
    }}>
        <Battery
            value={state.electricity}
            theme={{batteryColor: '#ffffff'}}
            size={cx(6)}
            onCalcColor={calcColor}
        />
        <Text style={{color: '#FFF', fontSize: cx(10)}}>{`${state.electricity}%`}</Text>
    </View>}
    <View style={{flex: 1}}/>

    {!!state.wifiSignal && <View style={{
      backgroundColor: '#00000033',
      padding: cx(5),
      marginTop: cx(5),
      alignItems: 'center',
      flexDirection: 'row',
      borderRadius: cx(8)
    }}>
        <Image source={{uri: res.wifi}} style={{width: cx(14), height: cx(14)}}/>
        <Text style={{color: '#FFF', fontSize: cx(10), marginStart: cx(4)}}>{`${state.wifiSignal}%`}</Text>
    </View>}


  </View>);
})