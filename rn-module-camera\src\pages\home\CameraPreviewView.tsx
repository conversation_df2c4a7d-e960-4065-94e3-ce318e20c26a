import {View, Text, StatusBar, StyleSheet} from "react-native";
import React from "react";
import {
    initCameraStatus,
    isSupportDirectionControl,
    isWirless,
    useSecurityMode,
    useShowCameraPreview,
} from "../../hooks/DeviceHooks";
import {useDeviceCategory, useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {SwitchButton, Utils} from "tuya-panel-kit";
import Card from "@ledvance/base/src/components/Card";
import {useReactive, useUpdateEffect} from "ahooks";
import CameraPreviewBottomFunction from "./CameraPreviewBottomFunction";
import {decodeClarityDic} from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native/cameraData";
import TakePhotoAndRecordVideoView from "./TakePhotoAndRecordVideoView";
import CameraDirectionView from "./CameraDirectionView";
import {StreamStatus} from "../../utils/StreamStatus";
import CameraPreviewFullscreenBottomFunction, {
    CameraPreviewFullscreenBottomFunctionProps
} from "./CameraPreviewFullscreenBottomFunction";
import xlog, {fullPlayerHeight, fullPlayerWidth} from "../../utils/common";
import I18n from "@ledvance/base/src/i18n";
import TYIpcPlayer from "../../components/TYIpcPlayer";
import CameraPreviewTopFunction from "./CameraPreviewTopFunction";
import ThemeType from '@ledvance/base/src/config/themeType'
import {UADeviceCategory} from "@ledvance/base/src/api/native";

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface CameraPreviewViewProp extends LoadingCallback {
    theme?: ThemeType
    onFullscreen: (isFullscreen: boolean) => void
}

export default withTheme(function CameraPreviewView(prop: CameraPreviewViewProp) {
    const {onLoading, onFullscreen} = prop;
    const devInfo = useDeviceInfo()
    const deviceCategory = useDeviceCategory()
    const {deviceOnline} = devInfo;
    const [securityMode] = useSecurityMode();
    const [showCameraPreview, setShowCameraPreview] = useShowCameraPreview();

    const state = useReactive({
        deviceOnline: deviceOnline === undefined ? true : deviceOnline,
        securityMode: securityMode,
        streamStatus: -1,
        isHD: true,
        isMuting: true,
        isRecording: false,
        isTalkBacking: false,
        isShowVideoPreview: false,
        isShowDirectionControl: false,
        isDirectionControlling: false,
        isFullscreen: false,
        zoomStatus: -1,
        isSupportMic: false,
        isVideoDoorbell: deviceCategory == UADeviceCategory.VideoDoorbell,
        showCameraPreview: showCameraPreview,
    });

    useUpdateEffect(() => {
        state.securityMode = securityMode;
        state.deviceOnline = deviceOnline;
        state.showCameraPreview = showCameraPreview;
    }, [securityMode, deviceOnline, showCameraPreview]);

    useUpdateEffect(() => {
        let firstInterval:boolean = true
        if (state.isShowDirectionControl && !state.isDirectionControlling && !state.isFullscreen && isSupportDirectionControl()) {
            const timer = setInterval(() => {
              if (!firstInterval){
                state.isShowDirectionControl = false;
              }
              firstInterval=false
            }, 3000)
            return () => {
                clearTimeout(timer)
            };
        }
    }, [state.isShowDirectionControl, state.isDirectionControlling, state.isFullscreen]);

    useUpdateEffect(() => {
        state.isShowVideoPreview = state.streamStatus === 6;
        if (state.isShowVideoPreview) {
            state.isShowDirectionControl = true;
        }
    }, [state.streamStatus])

    useUpdateEffect(() => {
        StatusBar.setHidden(state.isFullscreen);
        if (state.isFullscreen) {
            state.isShowDirectionControl = false;
        }
    }, [state.isFullscreen])

    const onChangeStreamStatus = async (status: number) => {
        xlog("onChangeStreamStatus======>", status)
        state.streamStatus = status;
        if (state.streamStatus === StreamStatus.VideoPlay) {
            initCameraStatus().then((data) => {
                xlog("==============>", JSON.stringify(data))
                const {isMuting, isRecording, isTalkBacking} = data;
                state.isMuting = isMuting;
                state.isRecording = isRecording;
                state.isTalkBacking = isTalkBacking;
            });
        }
    };

    const styles = StyleSheet.create({
        fullscreenContainer: {
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#000000',
            elevation: 0,
            shadowColor: '#000000',
            shadowOpacity: 0,
            shadowRadius: 0,
            shadowOffset: {
                width: 0,
                height: 0,
            },
            borderRadius: 0
        },
        smallScreenContainer: {marginHorizontal: cx(24)},
        smallScreenVideo: {width: '100%', aspectRatio: 16 / 9, overflow: 'hidden'},
        titleContainer: {flexDirection: 'row', margin: cx(16), alignItems: 'center'},
        title: {
            flex: 1,
            color: prop.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
        },
        fullscreenVideo: {
            width: fullPlayerWidth,
            height: fullPlayerHeight,
            alignItems: 'center',
        },
    });

    return (<Card style={state.isFullscreen ? styles.fullscreenContainer : styles.smallScreenContainer}
                  containerStyle={{flexDirection: 'column'}}>
        {!state.isFullscreen && <View style={styles.titleContainer}>
            <Text style={styles.title}> {I18n.getLang('camera_tile_camera_headline')}</Text>
            <SwitchButton
                value={state.showCameraPreview}
                onValueChange={async (value) => {
                    onLoading && onLoading(true);
                    await setShowCameraPreview(value);
                    onLoading && onLoading(false);
                    state.showCameraPreview = value;
                }}
            />
        </View>}
        {state.showCameraPreview&&<View style={!state.isFullscreen ? styles.smallScreenVideo : styles.fullscreenVideo}>
            <TYIpcPlayer
                isWirless={isWirless()}
                privateMode={state.securityMode}
                deviceOnline={state.deviceOnline}
                micTalkImage={-1}
                showOneWayMic={false}
                showTwoWayMic={false}
                playerProps={{
                    scalable: state.isFullscreen,
                    rotateZ: state.isFullscreen && state.isVideoDoorbell ? 270 : 0
                }}
                hightScaleMode={true}
                onNormalScreenTapView={() => {
                    xlog("onNormalScreenTapView============+>onNormalScreenTapView")
                }}
                onChangePreview={() => {
                    xlog("onChangePreview============++>")
                }}
                getCloudCameraConfig={(data) => {
                    xlog("getCloudCameraConfig=============>", JSON.stringify(data));
                    const {vedioClarity} = data;
                    state.isHD = decodeClarityDic.HD === vedioClarity;
                    xlog("getCloudCameraConfig=============>", vedioClarity);
                    xlog("getCloudCameraConfig=============>", decodeClarityDic.HD === vedioClarity);
                }}
                initStatus={(data) => {
                    xlog("initStatus=============>", JSON.stringify(data));
                }}

                onChangeRecording={(isRecording) => {
                    xlog("onChangeRecording================>", isRecording);
                    state.isRecording = isRecording;
                }}
                renderFullComArr={[
                    {
                        component: CameraPreviewFullscreenBottomFunction, propData: {
                            isHD: state.isHD,
                            onHdChange: (isHD) => {
                                state.isHD = isHD;
                            },
                            isMuting: state.isMuting,
                            isTalkBacking: state.isTalkBacking,
                            isSupportMic: state.isSupportMic,
                            onIsMutingChange: ((isMuting) => {
                                console.log("onIsMutingChange===========>", isMuting)
                                state.isMuting = isMuting;
                            }),
                            onIsTalkBackingChange: ((isTalkBacking) => {
                                console.log("onIsTalkBackingChange===========>", isTalkBacking)
                                state.isTalkBacking = isTalkBacking;
                            }),
                        } as CameraPreviewFullscreenBottomFunctionProps
                    }
                ]}
                onChangeStreamStatus={onChangeStreamStatus}
                showCutScreen={false}
                showTimeInterval={false}
                fullPlayerHeight={fullPlayerHeight}
                fullPlayerWidth={fullPlayerWidth}
                isFullScreen={state.isFullscreen}
                onChangeScreenOrientation={(value) => {
                    xlog("onChangeScreenOrientation========>", value);
                    state.isFullscreen = value;
                    onFullscreen(value);
                }}
                onFullScreenTapView={(hideFullMenu) => {
                    xlog("onFullScreenTapView========================>", hideFullMenu)
                }}
                scaleMultiple={state.zoomStatus}
                onChangeZoomStatus={(zoomStatus) => {
                    xlog("onChangeZoomStatus===============>", zoomStatus)
                }}
                onChangeActiveZoomStatus={(zoomStatusObj) => {
                    const {zoomStatus} = zoomStatusObj;
                    xlog("onChangeActiveZoomStatus===============>", zoomStatus)
                    state.zoomStatus = zoomStatus === -1 ? 1 : zoomStatus;
                }}
                onListenIsTalking={(isTalking) => {
                    xlog("onListenIsTalking=============>", isTalking)
                }}
                onListenTalkingChangeMute={(voiceStatus) => {
                    xlog("onListenTalkingChangeMute================>", JSON.stringify(voiceStatus))
                    state.isMuting = voiceStatus === 'OFF';
                }}
                onChangeSupportedMicWay={(data) => {
                    xlog("onChangeSupportedMicWay=============>", JSON.stringify(data));
                    const {isSupportMic, isTwoWayTalk} = data;
                    state.isSupportMic = isSupportMic;
                }}
            />
            {state.isShowVideoPreview && !state.isFullscreen && <CameraPreviewTopFunction/>}
            {state.isShowVideoPreview && !state.isFullscreen && <CameraPreviewBottomFunction
                isHD={state.isHD}
                isMuting={state.isMuting}
                isTalkBacking={state.isTalkBacking}
                isSupportMic={state.isSupportMic}
                onHdChange={(isHD) => {
                    state.isHD = isHD;
                }}
                onIsMutingChange={(isMuting) => {
                    state.isMuting = isMuting;
                }}
                onIsTalkBackingChange={(isTalkBacking) => {
                    state.isTalkBacking = isTalkBacking;
                }}
            />}
            {!state.isFullscreen && isSupportDirectionControl() && state.isShowVideoPreview &&
                <CameraDirectionView
                  showDirection={state.isShowDirectionControl}
                  onShowDirection={() => {
                      state.isShowDirectionControl = true
                  }}
                  onControlling={(isControlling) => {
                    state.isDirectionControlling = isControlling;
                }}/>}
        </View>}
        {state.showCameraPreview && !state.isFullscreen && <TakePhotoAndRecordVideoView
            isRecording={state.isRecording}
        />}
    </Card>);
})
