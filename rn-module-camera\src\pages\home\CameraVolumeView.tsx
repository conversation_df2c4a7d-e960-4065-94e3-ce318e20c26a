import React, {useEffect} from "react";
import Card from "@ledvance/base/src/components/Card";
import {Utils} from "tuya-panel-kit";
import {useReactive} from "ahooks";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import {useVolume} from "../../hooks/DeviceHooks";
import {View} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX
const {withTheme} = Utils.ThemeUtils

interface CameraVolumeViewProp {
  theme?: ThemeType
}

export default withTheme(function CameraVolumeView(props: CameraVolumeViewProp) {
  const [volume, setVolume] = useVolume();
  const state = useReactive({
    volume: volume
  });

  useEffect(() => {
    state.volume = volume;
  }, [volume]);

  return (
    <View>
      <Spacer/>
      <Card style={{marginHorizontal: cx(24)}}
            containerStyle={{flexDirection: 'column'}}>
        <LdvSlider
          title={I18n.getLang('dotit_volume')}
          titleStyle={{
            fontSize: cx(16),
            fontWeight: 'bold',
            maxWidth: cx(220),
            color: props.theme?.global.fontColor
          }}
          subTitleStr={state.volume}
          value={state.volume}
          max={10}
          onValueChange={(value)=>{
            state.volume = value;
          }}
          style={{paddingBottom: cx(20), paddingTop: cx(10)}}
          onSlidingComplete={(value) => {
            setVolume(value).then()
            state.volume = value;
          }}/>
      </Card>
    </View>)
})
