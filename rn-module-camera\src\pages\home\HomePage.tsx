import React from 'react'
import Page from '@ledvance/base/src/components/Page'
import {useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import {NativeApi} from "@ledvance/base/src/api/native";
import CameraPreviewView from "./CameraPreviewView";
import CameraLightingView from "./CameraLightingView";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useReactive} from "ahooks";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import {ScrollView, StyleSheet} from "react-native";
import {Utils} from "tuya-panel-kit";
import CameraFeatureList from "./CameraFeatureList";
import {isSupportFloodlightSwitch, isSupportWirelessElectricity, useWirelessElectricity} from "../../hooks/DeviceHooks";
import BatteryPercentageView from "@ledvance/base/src/components/BatteryPercentageView";
const cx = Utils.RatioUtils.convertX;
const HomePage = () => {
  const devInfo = useDeviceInfo();
  const familyName = useFamilyName();
  const electricity = useWirelessElectricity();
  const state = useReactive({
    loading: false,
    isFullscreen: false,
  });

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => {
        if (state.isFullscreen) {
          TYIpcPlayerManager.setScreenOrientation(0);
          return
        }
        NativeApi.back();
      }}
      onHeadlineIconClick={() => NativeApi.toDeviceSettingsPage(devInfo.devId)}
      loading={state.loading}
    >
      <ScrollView bounces={false} overScrollMode={'never'}
                  style={state.isFullscreen ? styles.fullscreenScrollView : {}}
                  contentContainerStyle={styles.fullscreenScrollViewContent}>
        {!state.isFullscreen && <Spacer height={cx(10)}/>}
        {!state.isFullscreen && isSupportWirelessElectricity() && <BatteryPercentageView value={electricity}/>}
        {!state.isFullscreen && isSupportWirelessElectricity() && <Spacer/>}
        <CameraPreviewView
          onLoading={(loading) => state.loading = loading}
          onFullscreen={((isFullscreen) => state.isFullscreen = isFullscreen)}
        />
        {!state.isFullscreen && isSupportFloodlightSwitch() &&
            <CameraLightingView onLoading={(loading) => state.loading = loading}/>}
        {!state.isFullscreen && <CameraFeatureList/>}
        {!state.isFullscreen && <Spacer height={cx(40)}/>}
      </ScrollView>
    </Page>
  )
}

const styles = StyleSheet.create({
  fullscreenScrollView: {position: 'absolute', top: 0, right: 0, bottom: 0, left: 0},
  fullscreenScrollViewContent: {flexGrow: 1},
});

export default HomePage
