import {Image, Text, TouchableOpacity, View} from "react-native";
import React, {useEffect} from "react";
import TYIpcPlayerManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native";
import res from "@ledvance/base/src/res";
import {Divider, Utils} from "tuya-panel-kit";
import {useReactive, useUpdateEffect} from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface TakePhotoAndRecordVideoViewProp {
    theme?: ThemeType
    isRecording: boolean
}

export default withTheme(function TakePhotoAndRecordVideoView(prop: TakePhotoAndRecordVideoViewProp) {
    const {isRecording} = prop;
    const state = useReactive({
        isRecording: isRecording,
        isShowTakePhotoTip: false,
        isShowRecordVideoTip: false,
        recordTime: 0,
    });

    useUpdateEffect(() => {
        let firstInterval:boolean = true
        if (state.isShowTakePhotoTip) {
            const timer = setInterval(() => {
                if (!firstInterval){
                    state.isShowTakePhotoTip = false;
                }
                firstInterval=false
            }, 3000);
            return () => {
                clearTimeout(timer)
            };
        }
    }, [state.isShowTakePhotoTip]);
    useUpdateEffect(() => {
        let firstInterval:boolean = true
        if (state.isShowRecordVideoTip) {
            const timer = setInterval(() => {
                if (!firstInterval){
                    state.isShowRecordVideoTip = false;
                }
                firstInterval=false
            }, 3000);
            return () => {
                clearTimeout(timer)
            };
        }
    }, [state.isShowRecordVideoTip]);

    useUpdateEffect(() => {
        if (!state.isRecording) {
            state.recordTime = 0;
            state.isShowRecordVideoTip = true;
            return
        }
        const timer = setInterval(() => {
            state.recordTime++;
        }, 1000)
        return () => {
            clearTimeout(timer)
        };
    }, [state.isRecording])

    useEffect(() => {
        state.isRecording = isRecording;
    }, [isRecording]);

    return (<View style={{flexDirection: 'column'}}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity style={{flex: 1,}} onPress={() => {
                TYIpcPlayerManager.snapShoot().then();
                state.isShowTakePhotoTip = true;
            }}>
                <View style={{
                    flexDirection: 'column',
                    alignItems: 'center',
                    paddingTop:cx(20),
                    justifyContent: 'center',
                }}>
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                        <Image source={{ uri: res.take_photo}} style={{width: cx(20), height: cx(20), tintColor: prop.theme?.button.primary}}/>
                        <Text style={{
                            color: prop.theme?.button.primary,
                            fontSize: cx(16),
                            marginStart: cx(4),
                            maxWidth: cx(120),
                        }}>{I18n.getLang('camera_tile_camera_button_label_photo')}</Text>
                    </View>
                </View>
            </TouchableOpacity>
            <Divider style={{flexDirection: 'column'}}/>
            <TouchableOpacity style={{flex: 1,}} onPress={() => {
                TYIpcPlayerManager.enableRecord().then();
            }}>
                <View style={{
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingTop:cx(20),
                }}>
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',

                    }}>
                        <Image source={{ uri: res.record_video}} style={{width: cx(20), height: cx(20), tintColor: prop.theme?.button.primary}}/>
                        <Text style={{
                            color: prop.theme?.button.primary,
                            fontSize: cx(16),
                            marginStart: cx(4),
                            maxWidth: cx(120)
                        }}>{I18n.getLang('camera_tile_camera_button_label_video')}</Text>
                    </View>
                </View>
            </TouchableOpacity>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap'}}>
            <View style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                paddingBottom:cx(20)
            }}>
                {state.isShowTakePhotoTip && <Text
                    style={{
                        color: prop.theme?.global.secondFontColor,
                        marginTop: cx(3),
                        textAlign: 'center'
                    }}>{I18n.getLang('camera_tile_camera_save_msg')}</Text>}

            </View>
            <Divider style={{flexDirection: 'column'}}/>
            <View style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                paddingBottom:cx(20)
            }}>
                {state.isRecording && <View style={{flexDirection: 'row', alignItems: 'center', marginTop: cx(3)}}>
                    <View style={{
                        width: cx(10),
                        height: cx(10),
                        backgroundColor: 'red',
                        borderRadius: cx(5),
                        borderColor: prop.theme?.global.background,
                        borderWidth: cx(1),
                        marginEnd: cx(3),
                    }}/>
                    <Text style={{textAlign: 'center', color: prop.theme?.global.secondFontColor,}}>{`Rec ${getTime(state.recordTime)}`}</Text>
                </View>}
                {state.isShowRecordVideoTip && <Text
                    style={{
                        color: prop.theme?.global.secondFontColor,
                        marginTop: cx(3),
                        textAlign: 'center'
                    }}>{I18n.getLang('camera_tile_camera_save_msg')}</Text>}
            </View>
        </View>
    </View>)
})

const getTime = (time: number): string => {
    const hour = String(Math.floor(time / 3600)).padStart(2, '0');
    const minutes = String(Math.floor((time % 3600) / 60)).padStart(2, '0')
    const seconds = String(time % 60).padStart(2, '0');
    return `${hour}:${minutes}:${seconds}`;
}
