import React from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import Card from "@ledvance/base/src/components/Card";
import {Modal, TimerPicker, Utils} from "tuya-panel-kit";
import {Text, TouchableOpacity, View} from "react-native";
import ThemeType from "@ledvance/base/src/config/themeType";
import I18n from "@ledvance/base/src/i18n/index";
import {useSystemTimeFormate} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import {getFormatTime} from "../PatrolPage";

const {withTheme} = Utils.ThemeUtils
const {convertX: cx} = Utils.RatioUtils

type TimeRangeModalProps = {
  theme?: ThemeType,
  visible: boolean,
  startTime: number,
  endTime: number,
  onCancel: () => void,
  onConfirm: (startTime: number, endTime: number) => void,
}
export default withTheme(function TimeRangeModal(props: TimeRangeModalProps) {
  const {theme, visible, startTime, endTime, onCancel, onConfirm} = props;
  const is24HourClock = useSystemTimeFormate();

  const state = useReactive({
    startTime: startTime,
    endTime: endTime,
  });


  return (<Modal
    visible={visible}
    onMaskPress={onCancel}
  >
    <Card>
      <Spacer height={cx(20)}/>
      <View style={{
        flexDirection: 'row',
      }}>
        <Spacer style={{flex: 1}}/>
        <View style={{alignItems: 'center'}}>
          <Text style={{fontWeight: 'bold', fontSize: cx(16), color: theme?.global.fontColor}}>{getFormatTime(state.startTime) as string}</Text>
          <Text style={{
            fontSize: cx(12),
            color: theme?.global.secondFontColor,
            marginTop: cx(3)
          }}>{I18n.getLang('thermostat_starttime')}</Text>
        </View>
        <View style={{
          flexDirection: 'row',
          width: cx(80),
          justifyContent: 'center',
          marginTop: cx(12)
        }}>
          <View style={{width: cx(12), height: cx(2), backgroundColor: theme?.global.secondFontColor,borderRadius:cx(5)}}/>
        </View>
        <View style={{alignItems: 'center'}}>
          <Text style={{fontWeight: 'bold', fontSize: cx(16), color: theme?.global.fontColor}}>{getFormatTime(state.endTime) as string}</Text>
          <Text style={{
            fontSize: cx(12),
            color: theme?.global.secondFontColor,
            marginTop: cx(3)
          }}>{I18n.getLang('thermostat_endtime')}</Text>
        </View>
        <View style={{flex: 1}}>
          {state.startTime > state.endTime &&
              <Text style={{
                fontSize: cx(10),
                color: theme?.global?.fontColor,
                marginTop: cx(6)
              }}>{I18n.getLang('scene_next_day')}</Text>}
        </View>
      </View>

      <View>
        <TimerPicker
          key={props.theme?.type}
          itemTextColor="#aeadb5"
          style={{paddingVertical: cx(0), marginVertical: cx(0), backgroundColor: props.theme?.global.background}}
          pickerFontColor={props.theme?.global.fontColor}
          is12Hours={!is24HourClock}
          singlePicker={false}
          prefixPosition={'left'}
          amText={I18n.getLang('manage_user_calendar_label_am')}
          pmText={I18n.getLang('manage_user_calendar_label_pm')}
          startTime={state.startTime}
          endTime={state.endTime}
          symbol={''}
          onTimerChange={(startTime, endTime) => {
            state.startTime = startTime
            state.endTime = endTime
          }}
        />
      </View>

      <View style={{width: '100%', height: cx(1), backgroundColor: theme?.dialog.lineColor}}/>
      <View style={{flexDirection: 'row', height: cx(46)}}>
        <TouchableOpacity style={{flex: 1}} onPress={() => {
          onCancel();
        }}>
          <Text
            style={{
              width: '100%',
              textAlign: 'center',
              textAlignVertical: 'center',
              height: cx(46),
              color: theme?.dialog.cancelFontColor
            }}>{I18n.getLang('auto_scan_system_cancel')}</Text>
        </TouchableOpacity>
        <View style={{width: cx(1), height: cx(46), backgroundColor: theme?.dialog.lineColor}}/>
        <TouchableOpacity style={{flex: 1}} onPress={() => {
          onConfirm(state.startTime, state.endTime);
        }}>
          <Text style={{
            width: '100%',
            textAlign: 'center',
            textAlignVertical: 'center',
            height: cx(46),
            color: theme?.dialog?.confirmFontColor
          }}>{I18n.getLang('auto_scan_system_wifi_confirm')}</Text>
        </TouchableOpacity>
      </View>
    </Card>
  </Modal>)
});

