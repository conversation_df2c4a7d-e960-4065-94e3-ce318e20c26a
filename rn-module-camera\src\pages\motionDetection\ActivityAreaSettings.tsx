import {Text, TouchableOpacity, View} from "react-native";
import React from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import {IconFont, SwitchButton, Utils} from "tuya-panel-kit";
import {useReactive, useUpdateEffect} from "ahooks";
import {
    isSupportHumanoidFilter,
    toCameraChangeActivityArea,
    useHumanoidFilter,
    useMotionAreaSwitch
} from "../../hooks/DeviceHooks";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function ActivityAreaSettings(props: { theme?: ThemeType }) {
    const devInfo = useDeviceInfo();
    const [motionAreaSwitch, setMotionAreaSwitch] = useMotionAreaSwitch();
    const [humanoidFilter, setHumanoidFilter] = useHumanoidFilter();
    const state = useReactive({
        motionAreaSwitch: motionAreaSwitch,
        humanoidFilter: humanoidFilter,
    });
    useUpdateEffect(() => {
        state.motionAreaSwitch = motionAreaSwitch;
        state.humanoidFilter = humanoidFilter;
    }, [motionAreaSwitch, humanoidFilter]);
    return (<View>
        <View style={{
            flexDirection: 'row',
            alignItems: 'center',
        }}>
            <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(14),
                flex: 1
            }}>{I18n.getLang('motion_detection_cloud_camera_settings_text')}</Text>
            <SwitchButton
                value={state.motionAreaSwitch}
                onValueChange={async (value) => {
                    await setMotionAreaSwitch(value);
                    state.motionAreaSwitch = value;
                }}
            />
        </View>
        {state.motionAreaSwitch &&
            <TouchableOpacity onPress={() => {
                toCameraChangeActivityArea(devInfo.devId)
            }}>
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: cx(20)}}>
                    <Text style={{
                        color: props.theme?.global.fontColor,
                        fontSize: cx(14),
                        flex: 1
                    }}>{I18n.getLang('motion_detection_cloud_camera_settings_text2')}</Text>
                    <IconFont name={'arrow'} color={props.theme?.global.secondFontColor} size={cx(11)}/>
                </View>
            </TouchableOpacity>}
        <Spacer/>
        {isSupportHumanoidFilter() &&
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
            }}>
                <Text style={{
                    color: props.theme?.global.fontColor,
                    fontSize: cx(14),
                    flex: 1
                }}>{I18n.getLang('camera_human_body_filtering')}</Text>
                <SwitchButton
                    value={state.humanoidFilter}
                    onValueChange={async (value) => {
                        await setHumanoidFilter(value);
                        state.humanoidFilter = value;
                    }}
                />
            </View>}
        {isSupportHumanoidFilter() && <Spacer/>}
    </View>);
})
