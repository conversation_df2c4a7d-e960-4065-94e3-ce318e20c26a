import {Text, View} from "react-native";
import React from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {SwitchButton, Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useAutoSiren} from "../../hooks/DeviceHooks";
import {useReactive, useUpdateEffect} from "ahooks";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function AutoSirenView(props: { theme?: ThemeType }) {
    const [autoSiren, setAutoSiren] = useAutoSiren();
    const state = useReactive({
        autoSiren: autoSiren,
    });

    useUpdateEffect(() => {
        state.autoSiren = autoSiren;
    }, [autoSiren])
    return (<View style={{marginHorizontal:cx(24)}}>
        <Text style={{
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
        }}>{I18n.getLang('camera_feature_1_headline')}</Text>
        <Spacer/>
        <View style={{
            flexDirection: 'row',
            alignItems: 'center',
        }}>
            <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(14),
                flex: 1
            }}>{I18n.getLang('motion_detection_with_safe_mode_siren_text')}</Text>
            <SwitchButton
                value={state.autoSiren}
                onValueChange={async (value) => {
                    await setAutoSiren(value);
                    state.autoSiren = value;
                }}
            />
        </View>
        <Spacer height={cx(40)}/>
    </View>)
})
