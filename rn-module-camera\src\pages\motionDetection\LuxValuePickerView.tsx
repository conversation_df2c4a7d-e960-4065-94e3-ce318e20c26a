import {View} from "react-native";
import React, {useCallback, useEffect} from "react";
import {useReactive, useUpdateEffect} from "ahooks";
import LDVPicker from "../../components/LDVPicker";
import {PickerDataProps, Utils} from "tuya-panel-kit";
import xlog from "../../utils/common";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface LuxValuePickerViewProps {
    theme?: ThemeType
    value: number,
    minValue: number,
    maxValue: number,
    label?: string[],
    onValueChange: (value: number) => void,
}

export default withTheme(function LuxValuePickerView(props: LuxValuePickerViewProps) {
    const {value, maxValue, minValue, label, onValueChange} = props;
    const [label1, label2, label3] = label || [];
    const state = useReactive({
        minValue: minValue,
        maxValue: maxValue,
        luxFirstValue: 1,
        luxSecondValue: 1,
        luxThirdValue: 1,
        luxFirstItemList: getNumberItemList(0, 9),
        luxSecondItemList: getNumberItemList(0, 9),
        luxThirdItemList: getNumberItemList(0, 9),
    });

    const handleLuxValueChange = useCallback((isChange: boolean = false) => {
        xlog(`handleLuxValueChange=============>${state.luxFirstValue} ${state.luxSecondValue} ${state.maxValue} ${state.minValue}`)
        const maxFirstValue = Math.floor(state.maxValue / 100);
        const minFirstValue = Math.floor(state.minValue / 100);

        const minSecondValue = Math.floor((state.minValue % 100) / 10);
        const maxSecondValue = Math.floor((state.maxValue % 100) / 10);

        const minThirdValue = state.minValue % 10;
        const maxThirdValue = state.maxValue % 10;
        xlog(`handleLuxValueChange firstValue====================>${state.luxFirstValue} ${minFirstValue} ${maxFirstValue}`)
        if (state.luxFirstValue === minFirstValue) {
            state.luxSecondItemList = getNumberItemList(minSecondValue, 9);
            if (state.luxSecondValue < minSecondValue) {
                state.luxSecondValue = minSecondValue;
            }
        } else if (state.luxFirstValue === maxFirstValue) {
            state.luxSecondItemList = getNumberItemList(0, maxSecondValue)
            if (state.luxSecondValue > maxSecondValue) {
                state.luxSecondValue = maxSecondValue;
            }
        } else {
            state.luxSecondItemList = getNumberItemList(0, 9)
        }
        xlog(`handleLuxValueChange secondValue====================>${state.luxSecondValue} ${minSecondValue} ${maxSecondValue}`)

        if (state.luxFirstValue === minFirstValue && state.luxSecondValue === minSecondValue) {
            state.luxThirdItemList = getNumberItemList(minThirdValue, 9);
            if (state.luxThirdValue < minThirdValue) {
                state.luxThirdValue = minThirdValue;
            }
        } else if (state.luxFirstValue === maxFirstValue && state.luxSecondValue === maxSecondValue) {
            state.luxThirdItemList = getNumberItemList(0, maxThirdValue);
            if (state.luxThirdValue > maxThirdValue) {
                state.luxThirdValue = maxThirdValue;
            }
        } else {
            state.luxThirdItemList = getNumberItemList(0, 9);
        }
        if (isChange) {
            onValueChange((state.luxFirstValue * 100) + (state.luxSecondValue * 10) + state.luxThirdValue);
        }
    }, []);

    useEffect(() => {
        state.maxValue = maxValue;
        state.minValue = minValue;
        xlog(`valuevaluevaluevalue============>${value}`)
        state.luxFirstValue = Math.floor(value / 100);
        state.luxSecondValue = Math.floor((value % 100) / 10);
        state.luxThirdValue = value % 10;
        state.luxFirstItemList = getNumberItemList(Math.floor(minValue / 100), Math.floor(maxValue / 100))
        handleLuxValueChange();
    }, [value, minValue, maxValue]);

    useUpdateEffect(() => {
        handleLuxValueChange(true);
    }, [state.luxFirstValue, state.luxSecondValue, state.luxThirdValue]);

    return (<View style={{flexDirection: 'row', alignItems: 'center', marginHorizontal: cx(20)}}>
        <LDVPicker
            style={{flex: 1}}
            label={label1}
            selectedValue={`${state.luxFirstValue}`}
            onValueChange={(value) => {
                state.luxFirstValue = parseInt(value);
            }}
            dataSource={state.luxFirstItemList}
        />
        <LDVPicker
            style={{flex: 1}}
            label={label2}
            selectedValue={`${state.luxSecondValue}`}
            onValueChange={(value) => {
                state.luxSecondValue = parseInt(value);
            }}
            dataSource={state.luxSecondItemList}
        />
        <LDVPicker
            style={{flex: 1}}
            label={label3}
            selectedValue={`${state.luxThirdValue}`}
            onValueChange={(value) => {
                state.luxThirdValue = parseInt(value);
            }}
            dataSource={state.luxThirdItemList}
        />
    </View>);
})

const getNumberItemList = (minValue: number = 0, maxValue: number): PickerDataProps[] => {
    return Array.from({length: maxValue === minValue ? 1 : (maxValue - minValue) + 1}, (_, i) => i + minValue).map(value => {
        return {label: `${value}`, value: `${value}`}
    });
}
