import {View} from "react-native";
import React, {useCallback, useEffect} from "react";
import {useReactive, useUpdateEffect} from "ahooks";
import LDVPicker from "../../components/LDVPicker";
import {Utils} from "tuya-panel-kit";
import I18n from "@ledvance/base/src/i18n";
import xlog, {getNumberItemList} from "../../utils/common";

const cx = Utils.RatioUtils.convertX;

interface MinuteSecondsPickViewProps {
    value: number,
    minSeconds: number,
    maxSeconds: number,
    onSecondsChange: (value: number) => void
}

export default function MinuteSecondsPickView(props: MinuteSecondsPickViewProps) {
    const {value, minSeconds, maxSeconds, onSecondsChange} = props;
    const state = useReactive({
        minSeconds: minSeconds,
        maxSeconds: maxSeconds,
        minuteValue: 1,
        secondsValue: 1,
        minuteItemList: getNumberItemList(0, 59),
        secondsItemList: getNumberItemList(0, 59),
    });

    const handleTimeValueChange = useCallback((isChange: boolean = false) => {
        const minMinuteValue = Math.floor(state.minSeconds / 60);
        const maxMinuteValue = Math.floor(state.maxSeconds / 60);
        const minSecondsValue = Math.floor((state.minSeconds % 60));
        const maxSecondsValue = Math.floor(state.maxSeconds % 60);
        xlog(`handleTimeValueChange===========${state.minuteValue} ${minMinuteValue} ${maxMinuteValue}`)

        if (state.minuteValue === minMinuteValue) {
            state.secondsItemList = getNumberItemList(minSecondsValue, 59);
            if (state.secondsValue < minSecondsValue) {
                state.secondsValue = minSecondsValue;
            }
        } else if (state.minuteValue === maxMinuteValue) {
            state.secondsItemList = getNumberItemList(0, maxSecondsValue)
            if (state.secondsValue > maxSecondsValue) {
                state.secondsValue = maxSecondsValue;
            }
        } else {
            state.secondsItemList = getNumberItemList(0, 59);
        }
        if (isChange) {
            onSecondsChange((state.minuteValue * 60) + state.secondsValue);
        }
    }, []);

    useEffect(() => {
        state.minSeconds = minSeconds;
        state.maxSeconds = maxSeconds;
        state.minuteValue = Math.floor(value / 60);
        state.secondsValue = Math.floor(value % 60);
        state.minuteItemList = getNumberItemList(Math.floor(minSeconds / 60), Math.floor(maxSeconds / 60));
        handleTimeValueChange();
    }, [value, minSeconds, maxSeconds]);

    useUpdateEffect(() => {
        handleTimeValueChange(true);
    }, [state.minuteValue, state.secondsValue]);

    return (<View style={{flexDirection: 'row', alignItems: 'center', marginHorizontal: cx(20)}}>
        <LDVPicker
            style={{flex: 1}}
            label={I18n.getLang('socket_settings_switch_off_min')}
            selectedValue={`${state.minuteValue}`}
            onValueChange={(value) => {
                state.minuteValue = parseInt(value);
            }}
            dataSource={state.minuteItemList}
        />
        <LDVPicker
            style={{flex: 1}}
            label={I18n.getLang('socket_settings_switch_off_s')}
            selectedValue={`${state.secondsValue}`}
            onValueChange={(value) => {
                state.secondsValue = parseInt(value);
            }}
            dataSource={state.secondsItemList}
        />
    </View>);
}