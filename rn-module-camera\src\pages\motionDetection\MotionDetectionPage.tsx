import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {useDeviceCategory, useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {ScrollView, Text} from "react-native";
import {Utils} from "tuya-panel-kit";
import TriggerNotificationsView from "./TriggerNotificationsView";
import TriggerLightingView from "./TriggerLightingView";
import {
  isSupportAutoSiren,
  isSupportDecibelSwitch,
  isSupportFlightPIR,
  isSupportFlightPIRLux,
  isSupportLightSwitch,
  isSupportMotionAreaSwitch,
  isSupportMotionRecordingTime,
  isSupportMotionSensitivity,
  isSupportMotionSwitch,
  isSupportPIRAlarmInterval,
  isSupportSecuritySwitch
} from "../../hooks/DeviceHooks";
import SafetyModeView from "./SafetyModeView";
import AutoSirenView from "./AutoSirenView";
import ThemeType from '@ledvance/base/src/config/themeType'
import SoundDetectionView from "./SoundDetectionView";
import {UADeviceCategory} from "@ledvance/base/src/api/native";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const MotionDetectionPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const deviceCategory = useDeviceCategory()
  const isFirstShowPIR = deviceCategory === UADeviceCategory.LECamera || deviceCategory === UADeviceCategory.VideoDoorbell
  return (
    <Page backText={dev.name}
          headlineText={I18n.getLang(isSupportMotionAreaSwitch() ? 'motion_detection_headline_text' : 'motion_detection_headline_text')}>
      <ScrollView>
        <Text style={{
          color: props.theme?.global.fontColor,
          fontSize: cx(14),
          marginHorizontal: cx(24),
        }}>{I18n.getLang('motion_detection_description_text')}</Text>
        {/*根据需求低功耗摄像头以及门铃需要把PIR展示到最前面*/}
        {isSupportFlightPIR() && isFirstShowPIR &&
            <TriggerLightingView isSupportLight={isSupportLightSwitch() && isSupportFlightPIRLux()}/>}
        {(isSupportMotionSwitch() || isSupportMotionSensitivity() || isSupportMotionRecordingTime() || isSupportPIRAlarmInterval()) &&
            <TriggerNotificationsView/>}
        {isSupportFlightPIR() && !isFirstShowPIR &&
            <TriggerLightingView isSupportLight={isSupportLightSwitch() && isSupportFlightPIRLux()}/>}
        {isSupportSecuritySwitch() && <SafetyModeView/>}
        {isSupportAutoSiren() && <AutoSirenView/>}
        {isSupportDecibelSwitch() && <SoundDetectionView/>}

      </ScrollView>
    </Page>
  )
}

export default withTheme(MotionDetectionPage)
