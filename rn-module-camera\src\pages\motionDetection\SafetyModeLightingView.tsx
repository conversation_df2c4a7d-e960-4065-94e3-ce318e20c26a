import {Image, Text, TouchableOpacity, View} from "react-native";
import React from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import {SwitchButton, Utils} from "tuya-panel-kit";
import res from "@ledvance/base/src/res/index";
import LuxValuePickerView from "./LuxValuePickerView";
import xlog, {getNumberItemList} from "../../utils/common";
import MinuteSecondsPickView from "./MinuteSecondsPickView";
import {useReactive, useThrottleFn, useUpdateEffect} from "ahooks";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import LDVPicker from "../../components/LDVPicker";
import {
    useAlarmMode,
    useHumanBrightness,
    useHumanBrightnessTime,
    useSecurityLux, useNoHumanBrightness, useNoHumanBrightnessTime, useSensingDistance
} from "../../hooks/DeviceHooks";
import LDVModal from "../../components/LDVModal";
import I18n from "@ledvance/base/src/i18n";
import {SecurityLevel} from "../../utils/SecurityLevel";
import ThemeType from '@ledvance/base/src/config/themeType'

interface SafetyModeLightingViewProps {
    theme?: ThemeType
    securityLevel: string,
}

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function SafetyModeLightingView(props: SafetyModeLightingViewProps) {
    const [securityLux, setSecurityLux] = useSecurityLux();
    const [humanBrightness, setHumanBrightness] = useHumanBrightness();
    const [humanBrightnessTime, setHumanBrightnessTime] = useHumanBrightnessTime();
    const [noHumanBrightness, setNoHumanBrightness] = useNoHumanBrightness();
    const [noHumanBrightnessTime, setNoHumanBrightnessTime] = useNoHumanBrightnessTime();
    const [sensingDistance, setSensingDistance] = useSensingDistance();
    const [alarmMode, setAlarmMode] = useAlarmMode();
    const state = useReactive({
        securityLux: securityLux,
        humanBrightnessTime: humanBrightnessTime,
        humanBrightness: humanBrightness,
        alarmMode: alarmMode,
        noHumanBrightness: noHumanBrightness,
        noHumanLightUpTime: noHumanBrightnessTime,
        noHumanTimeItemList: getNumberItemList(1, 8),
        showLuxTipModal: false,
        showStandbyTipModal: false,
        isCanEdit: props.securityLevel === SecurityLevel.Customized,
        sensingDistance: sensingDistance,
        updateSecurityLuxFlag: Symbol()
    });

    useUpdateEffect(() => {
        state.humanBrightness = humanBrightness;
        state.humanBrightnessTime = humanBrightnessTime;
        state.alarmMode = alarmMode;
        state.isCanEdit = props.securityLevel === SecurityLevel.Customized;
        state.noHumanBrightness = noHumanBrightness;
        state.noHumanLightUpTime = noHumanBrightnessTime;
        state.securityLux = securityLux;
        state.sensingDistance = sensingDistance;
    }, [humanBrightness, humanBrightnessTime, alarmMode, props.securityLevel, noHumanBrightness, noHumanBrightnessTime, securityLux, sensingDistance]);

    useUpdateEffect(() => securityLuxRun, [state.updateSecurityLuxFlag])
    const {run: securityLuxRun} = useThrottleFn(() => {
        xlog(`securityLux=================>${state.securityLux}`)
        setSecurityLux(state.securityLux).then();
    }, {wait: 500});

    useUpdateEffect(() => humanBrightnessTimeRun, [state.humanBrightnessTime])
    const {run: humanBrightnessTimeRun} = useThrottleFn(() => {
        xlog(`humanBrightnessTime=================>${state.humanBrightnessTime}`)
        setHumanBrightnessTime(state.humanBrightnessTime).then()
    }, {wait: 500});
    return (<View>
        <Spacer/>
        <LdvSlider
            title={I18n.getLang('motion_detection_with_safe_mode_safetymode_sensing_text')}
            value={state.sensingDistance}
            min={5}
            max={12}
            subTitleStr={`${state.sensingDistance}m`}
            style={{paddingBottom: cx(20), paddingHorizontal: cx(0)}}
            onValueChange={(value) => {
                state.sensingDistance = value;
            }}
            onSlidingComplete={async (value) => {
                await setSensingDistance(value);
            }}/>
        <Spacer/>
        <Text style={{
            color: props.theme?.global.secondFontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
        }}>{I18n.getLang('motion_detection_subheadline_text')}</Text>
        <Spacer/>
        <View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Text style={{color: props.theme?.global.fontColor, fontSize: cx(14),}}>{I18n.getLang('motion_detection_selectionfield2_topic_text')}</Text>
                <TouchableOpacity onPress={() => {
                    state.showLuxTipModal = true;
                }}>
                    <Image source={{ uri: res.ic_info}} style={{
                        width: cx(16),
                        height: cx(16),
                        marginStart: cx(4),
                    }}/>
                </TouchableOpacity>
            </View>
            <Spacer/>
            <LuxValuePickerView
                value={state.securityLux}
                minValue={10}
                maxValue={500}
                onValueChange={(value) => {
                    xlog(`LuxValuePickerView================>${value}`);
                    if (state.securityLux != value) {
                        state.securityLux = value;
                        state.updateSecurityLuxFlag = Symbol();
                    }
                }}
            />
            <Spacer/>
            <LdvSlider
                title={I18n.getLang('motion_detection_with_safe_mode_brightness_text')}
                value={state.humanBrightness}
                style={{paddingHorizontal: 0}}
                min={50}
                max={100}
                onSlidingComplete={(value) => {
                    setHumanBrightness(value).then();
                    state.humanBrightness = value;
                }}/>
            <Spacer/>
            <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(14),
            }}>{I18n.getLang('motion_detection_switched_off_text')}</Text>
            <MinuteSecondsPickView
                value={state.humanBrightnessTime}
                minSeconds={5}
                maxSeconds={360}
                onSecondsChange={(value) => {
                    state.humanBrightnessTime = value;
                }}
            />
        </View>
        <Spacer/>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{color: props.theme?.global.fontColor, fontSize: cx(14),}}>{I18n.getLang('motion_detection_standby_light_text1')}</Text>
            <TouchableOpacity onPress={() => {
                state.showStandbyTipModal = true;
            }}>
                <Image source={{ uri: res.ic_info}} style={{
                    width: cx(16),
                    height: cx(16),
                    marginStart: cx(4),
                }}/>
            </TouchableOpacity>
            <Spacer style={{flex: 1}}/>
            <SwitchButton
                value={state.alarmMode}
                onValueChange={async (value) => {
                    await setAlarmMode(value);
                    state.alarmMode = value;
                }}
            />
        </View>
        {state.alarmMode && <View>
            <LdvSlider
                title={I18n.getLang('motion_detection_standby_light_brightness_text')}
                value={state.noHumanBrightness}
                style={{paddingHorizontal: 0}}
                min={10}
                max={50}
                onSlidingComplete={(value) => {
                    setNoHumanBrightness(value).then()
                    state.noHumanBrightness = value;
                }}/>
            <Spacer/>
            <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(14),
            }}>{I18n.getLang('motion_detection_with_safe_mode_switch_off_text2')}</Text>
            <Spacer/>
            <LDVPicker
                selectedValue={`${state.noHumanLightUpTime}`}
                label={I18n.getLang('time_unit_h')}
                onValueChange={(value) => {
                    const hour = parseInt(value);
                    xlog(`setNoHumanBrightnessTime=============>${hour}`)
                    setNoHumanBrightnessTime(hour).then();
                    state.noHumanLightUpTime = hour;
                }}
                dataSource={state.noHumanTimeItemList}
            />
        </View>}
        <Spacer/>

        <LDVModal visible={state.showLuxTipModal}
                  title={I18n.getLang('motion_detection_selectionfield2_topic_text')}
                  confirm={I18n.getLang('home_screen_home_dialog_yes_con')}
                  onConfirmPress={() => {
                      state.showLuxTipModal = false;
                  }}
                  onMaskPress={() => {
                      state.showLuxTipModal = false
                  }}
        >
            <Spacer/>
            <Text style={{
                fontSize: cx(15),
                fontWeight: 'bold',
                color: props.theme?.global.fontColor
            }}>{I18n.getLang('lux_value_headline_text')}</Text>
            <Spacer/>
            <Text style={{
                fontSize: cx(14),
                color: props.theme?.global.fontColor,
                minWidth: cx(150)
            }}>{I18n.getLang('lux_value_headline_description')}</Text>
            <Spacer/>
        </LDVModal>

        <LDVModal visible={state.showStandbyTipModal}
                  title={I18n.getLang('motion_detection_standby_light_text1')}
                  confirm={I18n.getLang('home_screen_home_dialog_yes_con')}
                  onConfirmPress={() => {
                      state.showStandbyTipModal = false;
                  }}
        >
            <Spacer/>
            <Text style={{fontSize: cx(14), color: props.theme?.global.fontColor}}>{I18n.getLang('standby_light_information_text')}</Text>
        </LDVModal>
        {!state.isCanEdit && <TouchableOpacity style={{width: '100%', height: '100%',position:'absolute'}} onPress={() => {
        }}/>}
    </View>)
})
