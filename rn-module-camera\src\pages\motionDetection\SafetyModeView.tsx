import {Text, View} from "react-native";
import React, {useEffect} from "react";
import LDVRadioButtonGroup, {LDVRadioButtonItemData} from "../../components/LDVRadioButtonGroup";
import {useReactive} from "ahooks";
import Spacer from "@ledvance/base/src/components/Spacer";
import {SwitchButton, Utils} from "tuya-panel-kit";
import SafetyModeLightingView from "./SafetyModeLightingView";
import {useSecurityLevel, useSecuritySwitch} from "../../hooks/DeviceHooks";
import {LDVRadioItemData} from "../../components/LDVRadioGroup";
import {SecurityLevel} from "../../utils/SecurityLevel";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

const getSecurityLevelName = (level: string) => {
    switch (level) {
        case SecurityLevel.Customized:
            return I18n.getLang('motion_detection_with_safe_mode_safetymode_value4_text');
        case SecurityLevel.High:
            return I18n.getLang('contact_sensor_battery_state1');
        case SecurityLevel.Middle:
            return I18n.getLang('contact_sensor_battery_state2');
        case SecurityLevel.Low:
        default:
            return I18n.getLang('contact_sensor_battery_state3');
    }
}
const getSecurityLevelItemList = (): LDVRadioItemData[] => {
    return Object.entries(SecurityLevel).map(([_, value]) => {
        const flex = value === SecurityLevel.Customized ? 1.5 : 1;
        return {title: getSecurityLevelName(value), value: `${value}`, flex: flex} as LDVRadioButtonItemData
    });
}
export default withTheme(function SafetyModeView(props: { theme?: ThemeType }) {
    const [securitySwitch, setSecuritySwitch] = useSecuritySwitch();
    const [securityLevel, setSecurityLevel] = useSecurityLevel();
    const state = useReactive({
        safeModeSwitch: securitySwitch,
        securityLevel: securityLevel,
    });
    useEffect(() => {
        state.safeModeSwitch = securitySwitch;
        state.securityLevel = securityLevel;
    }, [securitySwitch, securityLevel]);
    return (<View style={{marginHorizontal: cx(24)}}>
        <View style={{flexDirection: 'row'}}>
            <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(16),
                fontWeight: 'bold',
                flex: 1,
            }}>{I18n.getLang('motion_detection_with_safe_mode_subheadline_text')}</Text>
            <SwitchButton
                value={state.safeModeSwitch}
                onValueChange={async (value) => {
                    await setSecuritySwitch(value);
                    state.safeModeSwitch = value;
                }}
            />
        </View>
        <Spacer/>
        {state.safeModeSwitch && <LDVRadioButtonGroup
            data={getSecurityLevelItemList()}
            checkedValue={state.securityLevel}
            onCheckedChange={async (value) => {
                await setSecurityLevel(value.value);
                state.securityLevel = value.value;
            }}
        />}
        {state.safeModeSwitch && <SafetyModeLightingView securityLevel={state.securityLevel}/>}
    </View>)
})
