import {Text, View} from "react-native";
import React from "react";
import ThemeType from "@ledvance/base/src/config/themeType";
import {SwitchButton, Utils} from "tuya-panel-kit";
import I18n from "@ledvance/base/src/i18n/index";
import Spacer from "@ledvance/base/src/components/Spacer";
import {
  isSupportDecibelSensitivity,
  isSupportDetectBabyCryingSwitch,
  useDecibelSensitivity,
  useDecibelSwitch,
  useDetectBabyCryingSwitch
} from "../../hooks/DeviceHooks";
import {useReactive, useUpdateEffect} from "ahooks";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import {SelectPageData, SelectPageParams} from "@ledvance/ui-biz-bundle/src/modules/select/SelectPage";
import {toSelectPage} from "@ledvance/ui-biz-bundle/src/navigation/tools";
import {useNavigation} from '@react-navigation/native'
import {StackNavigationProp} from '@react-navigation/stack'
import {SoundSensitivity} from "../../utils/SoundSensitivity";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

export default withTheme(function SoundDetectionView(props: { theme?: ThemeType }) {
  const navigation = useNavigation<StackNavigationProp<any>>()

  const [decibelSwitch, setDecibelSwitch] = useDecibelSwitch();
  const [detectBabyCryingSwitch, setDetectBabyCryingSwitch] = useDetectBabyCryingSwitch();
  const [decibelSensitivity, setDecibelSensitivity] = useDecibelSensitivity();

  const state = useReactive({
    decibelSwitch: decibelSwitch,
    detectBabyCryingSwitch: detectBabyCryingSwitch,
    decibelSensitivity: getSoundSensitivityName(decibelSensitivity),
    itemList: getSoundSensitivityList(decibelSensitivity),
  });

  useUpdateEffect(() => {
    state.decibelSwitch = decibelSwitch;
    state.detectBabyCryingSwitch = detectBabyCryingSwitch;
    state.decibelSensitivity = getSoundSensitivityName(decibelSensitivity);
    state.itemList = getSoundSensitivityList(decibelSensitivity);
  }, [decibelSwitch, decibelSensitivity, detectBabyCryingSwitch]);

  return (<View style={{marginHorizontal: cx(24)}}>
    <Text style={{
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
    }}>{I18n.getLang('motion_detection_sound_detection')}</Text>
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      marginTop:cx(10)
    }}>
      <Text style={{
        color: props.theme?.global.fontColor,
        fontSize: cx(14),
        flex: 1
      }}>{I18n.getLang('motion_detection_sound_detection')}</Text>
      <SwitchButton
        value={state.decibelSwitch}
        onValueChange={async (value) => {
          await setDecibelSwitch(value);
          state.decibelSwitch = value;
        }}
      />
    </View>
    {isSupportDecibelSensitivity() && state.decibelSwitch && <TextFieldStyleButton
        placeholder={I18n.getLang('motion_detection_select_detection_sensitivity_level')}
        text={state.decibelSensitivity}
        onPress={() => {
          const params: SelectPageParams<string> = {
            title: I18n.getLang('motion_detection_select_detection_sensitivity_level'),
            data: state.itemList,
            onSelect: selectPageData => {
              setDecibelSensitivity(selectPageData.value).then();
              state.decibelSensitivity = getSoundSensitivityName(selectPageData.value)
              state.itemList = getSoundSensitivityList(selectPageData.value);
            }
          }
          toSelectPage(navigation, params)
        }}/>}
    <Spacer/>

    {isSupportDetectBabyCryingSwitch() && state.decibelSwitch && <View style={{
      flexDirection: 'row',
      alignItems: 'center',
    }}>
        <Text style={{
          color: props.theme?.global.fontColor,
          fontSize: cx(14),
          flex: 1
        }}>{I18n.getLang('motion_detection_detect_baby_crying')}</Text>
        <SwitchButton
            value={state.detectBabyCryingSwitch}
            onValueChange={async (value) => {
              await setDetectBabyCryingSwitch(value);
              state.detectBabyCryingSwitch = value;
            }}
        />
    </View>}
  </View>)
})


const getSoundSensitivityName = (value: string): string => {
  switch (value) {
    case SoundSensitivity.High:
      return I18n.getLang('contact_sensor_battery_state1')
    case SoundSensitivity.Low:
    default:
      return I18n.getLang('contact_sensor_battery_state3')
  }
}

const getSoundSensitivityList = (selectedLux: string): SelectPageData<string>[] => {
  return Object.entries(SoundSensitivity).map(([_, value]) => {
    return {text: `${getSoundSensitivityName(value)}`, value: value, selected: selectedLux === value};
  })
};
