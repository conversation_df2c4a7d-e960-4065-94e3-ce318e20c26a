import {Image, Text, TouchableOpacity, View} from "react-native";
import React from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import {SwitchButton, Utils} from "tuya-panel-kit";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import I18n from "@ledvance/base/src/i18n/index";
import {SelectPageData, SelectPageParams} from "@ledvance/ui-biz-bundle/src/modules/select/SelectPage";
import {toSelectPage} from "@ledvance/ui-biz-bundle/src/navigation/tools";
import {useReactive, useThrottleFn, useUpdateEffect} from "ahooks";
import {useNavigation} from '@react-navigation/native'
import {StackNavigationProp} from '@react-navigation/stack'
import res from "@ledvance/base/src/res/index";
import {
  isSupportFlightPIRLux,
  isSupportFlightWarnTime,
  useFlightPirLux,
  useFlightWarnTime,
  usePIRSwitch
} from "../../hooks/DeviceHooks";
import {PIR_SWITCH_OFF, PIRSensitivity} from "../../utils/PIRSensitivity";
import LuxValuePickerView from "./LuxValuePickerView";
import MinuteSecondsPickView from "./MinuteSecondsPickView";
import xlog from "../../utils/common";
import LDVModal from "../../components/LDVModal";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

export default withTheme(function TriggerLightingView(props: { theme?: ThemeType, isSupportLight: boolean }) {
  const navigation = useNavigation<StackNavigationProp<any>>()
  const [pirSwitch, setPIRSwitch] = usePIRSwitch();
  const [flightWarnTime, setFlightWarnTime] = useFlightWarnTime();
  const [flightPirLux, setFlightPirLux] = useFlightPirLux();
  const state = useReactive({
    pirSwitch: pirSwitch !== PIR_SWITCH_OFF,
    lastPIRSwitch: pirSwitch === PIR_SWITCH_OFF ? PIRSensitivity.High : pirSwitch,
    flightPirLux: flightPirLux,
    flightWarnTime: flightWarnTime,
    sensitivity: getPIRSensitivityName(pirSwitch),
    itemList: getPIRSensitivityList(pirSwitch),
    showLuxTipModal: false,
  });

  useUpdateEffect(() => {
    state.pirSwitch = pirSwitch !== PIR_SWITCH_OFF;
    state.flightPirLux = flightPirLux;
    state.flightWarnTime = flightWarnTime;
    state.sensitivity = getPIRSensitivityName(pirSwitch);
    state.itemList = getPIRSensitivityList(pirSwitch);
  }, [pirSwitch, flightPirLux, flightWarnTime]);

  useUpdateEffect(() => flightPirLuxRun, [state.flightPirLux])
  const {run: flightPirLuxRun} = useThrottleFn(() => {
    xlog(`=================>${state.flightPirLux}`)
    setFlightPirLux(state.flightPirLux).then()
  }, {wait: 500});

  useUpdateEffect(() => flightWarnTimeRun, [state.flightWarnTime])
  const {run: flightWarnTimeRun} = useThrottleFn(() => {
    xlog(`=================>${state.flightWarnTime}`)
    setFlightWarnTime(state.flightWarnTime).then()
  }, {wait: 500});

  return (<View style={{marginHorizontal: cx(24),}}>
    <Spacer/>
    <Text style={{
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
    }}>{I18n.getLang(props.isSupportLight ? 'motion_detection_subheadline_text' : 'motion_detection_pir')}</Text>
    {!props.isSupportLight && <Text style={{
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
    }}>{I18n.getLang('camera_motiondetectiondescription')}</Text>}
    <Spacer/>
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
    }}>
      <Text style={{
        color: props.theme?.global.fontColor,
        fontSize: cx(14),
        flex: 1
      }}>{I18n.getLang(props.isSupportLight ? 'motion_detection_trigger_text' : 'camera_motiondetection')}</Text>
      <SwitchButton
        value={state.pirSwitch}
        onValueChange={async (value) => {
          if (value) {
            await setPIRSwitch(state.lastPIRSwitch || PIRSensitivity.High);
          } else {
            await setPIRSwitch(PIR_SWITCH_OFF)
          }
          state.pirSwitch = value;
        }}
      />
    </View>

    {state.pirSwitch && <View>
        <TextFieldStyleButton
            placeholder={I18n.getLang(props.isSupportLight ? 'motion_detection_selectionfield_topic_text' : 'ldv_pir_sensitivity')}
            text={state.sensitivity}
            onPress={() => {
              const params: SelectPageParams<string> = {
                title: I18n.getLang(props.isSupportLight ? 'motion_detection_selectionfield_topic_text' : 'ldv_pir_sensitivity'),
                data: state.itemList,
                onSelect: selectPageData => {
                  setPIRSwitch(selectPageData.value).then();
                  state.lastPIRSwitch = selectPageData.value;
                  state.sensitivity = getPIRSensitivityName(selectPageData.value);
                  state.itemList = getPIRSensitivityList(selectPageData.value);
                }
              }
              toSelectPage(navigation, params)
            }}/>
        <Spacer/>
      {isSupportFlightPIRLux() && <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={{
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
          }}>{I18n.getLang('motion_detection_selectionfield2_topic_text')}</Text>
          <TouchableOpacity onPress={() => {
            state.showLuxTipModal = true;
          }}>
              <Image source={{uri: res.ic_info}} style={{
                width: cx(16),
                height: cx(16),
                marginStart: cx(4),
              }}/>
          </TouchableOpacity>
      </View>}
      {isSupportFlightPIRLux() && <LuxValuePickerView
          value={state.flightPirLux}
          minValue={10}
          maxValue={500}
          onValueChange={(value) => {
            xlog(`LuxValuePickerView================>${value}`);
            state.flightPirLux = value;
          }}
      />}
      {isSupportFlightPIRLux() && <Spacer/>}
      {isSupportFlightWarnTime() && <Text style={{
        color: props.theme?.global.fontColor,
        fontSize: cx(14),
      }}>{I18n.getLang('motion_detection_switched_off_text')}</Text>}
      {isSupportFlightWarnTime() && <MinuteSecondsPickView
          value={state.flightWarnTime}
          minSeconds={5}
          maxSeconds={360}
          onSecondsChange={(value) => {
            state.flightWarnTime = value;
          }}
      />}
    </View>}
    <Spacer/>
    <LDVModal visible={state.showLuxTipModal}
              title={I18n.getLang('motion_detection_selectionfield2_topic_text')}
              confirm={I18n.getLang('home_screen_home_dialog_yes_con')}
              onConfirmPress={() => {
                state.showLuxTipModal = false;
              }}
              onMaskPress={() => {
                state.showLuxTipModal = false
              }}
    >
      <Spacer/>
      <Text style={{
        fontSize: cx(15),
        fontWeight: 'bold',
        color: props.theme?.global.fontColor,
      }}>{I18n.getLang('lux_value_headline_text')}</Text>
      <Spacer/>
      <Text style={{
        fontSize: cx(14),
        color: props.theme?.global.fontColor,
        minWidth: cx(150)
      }}>{I18n.getLang('lux_value_headline_description')}</Text>
      <Spacer/>
    </LDVModal>
  </View>);
})

const getPIRSensitivityName = (value: string): string => {
  switch (value) {
    case PIRSensitivity.High:
      return I18n.getLang('contact_sensor_battery_state1')
    case PIRSensitivity.Middle:
      return I18n.getLang('contact_sensor_battery_state2')
    case PIRSensitivity.Low:
    default:
      return I18n.getLang('contact_sensor_battery_state3')
  }
}

const getPIRSensitivityList = (selectedLux: string): SelectPageData<string>[] => {
  return Object.entries(PIRSensitivity).map(([_, value]) => {
    return {text: `${getPIRSensitivityName(value)}`, value: value, selected: selectedLux === value};
  })
};
