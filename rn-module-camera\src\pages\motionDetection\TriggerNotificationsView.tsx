import {Text, TouchableOpacity, View} from "react-native";
import React from "react";
import Spacer from "@ledvance/base/src/components/Spacer";
import {IconFont, SwitchButton, Utils} from "tuya-panel-kit";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import {SelectPageData, SelectPageParams} from "@ledvance/ui-biz-bundle/src/modules/select/SelectPage";
import {toSelectPage} from "@ledvance/ui-biz-bundle/src/navigation/tools";
import {useNavigation} from '@react-navigation/native'
import {StackNavigationProp} from '@react-navigation/stack'
import {TriggerSensitivity} from "../../utils/TriggerSensitivity";
import {useReactive, useUpdateEffect} from "ahooks";
import {
  isSupportMotionAreaSwitch,
  isSupportMotionRecordingTime,
  isSupportMotionSensitivity,
  isSupportMotionSwitch,
  isSupportMotionTracking,
  isSupportPIRAlarmInterval,
  useAlarmInterval,
  useMotionSensitivity,
  useMotionSwitch,
  useMotionTracking,
  useVideoRecordingDuration
} from "../../hooks/DeviceHooks";
import I18n from "@ledvance/base/src/i18n";
import ActivityAreaSettings from "./ActivityAreaSettings";
import ThemeType from '@ledvance/base/src/config/themeType'
import {PIRAlarmInterval} from "../../utils/PIRAlarmInterval";
import {VideoRecordingDuration} from "../../utils/VideoRecordingDuration";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

export default withTheme(function TriggerNotificationsView(props: { theme?: ThemeType }) {
  const navigation = useNavigation<StackNavigationProp<any>>()
  const [motionSwitch, setMotionSwitch] = useMotionSwitch();
  const [motionSensitivity, setMotionSensitivity] = useMotionSensitivity();
  const [motionTracking, setMotionTracking] = useMotionTracking();
  const [videoRecordingDuration, setVideoRecordingDuration] = useVideoRecordingDuration();
  const [alarmInterval, setAlarmInterval] = useAlarmInterval();

  const state = useReactive({
    motionSwitch: motionSwitch,
    isMotionSwitchOn: !isSupportMotionSwitch() || motionSwitch,
    motionTracking: motionTracking,
    alarmInterval: getAlarmIntervalName(alarmInterval),
    alarmIntervalList: getAlarmIntervalList(alarmInterval),
    sensitivity: getTriggerSensitivityName(motionSensitivity),
    itemList: getTriggerSensitivityList(motionSensitivity),
    videoRecordingDuration: getVideoRecordingDurationName(videoRecordingDuration),
    videoRecordingDurationList: getVideoRecordingDurationList(videoRecordingDuration),
  });

  useUpdateEffect(() => {
    state.motionSwitch = motionSwitch;
    state.isMotionSwitchOn = !isSupportMotionSwitch() || motionSwitch;
    state.motionTracking = motionTracking;
    state.videoRecordingDuration = getVideoRecordingDurationName(videoRecordingDuration);
    state.videoRecordingDurationList = getVideoRecordingDurationList(videoRecordingDuration);
    state.sensitivity = getTriggerSensitivityName(motionSensitivity);
    state.itemList = getTriggerSensitivityList(motionSensitivity);
    state.alarmInterval = getAlarmIntervalName(alarmInterval);
    state.alarmIntervalList = getAlarmIntervalList(alarmInterval);
  }, [motionSwitch, motionSensitivity, motionTracking, videoRecordingDuration, alarmInterval]);

  return (<View style={{marginHorizontal: cx(24),}}>
    <Spacer/>
    <Text style={{
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
    }}>{I18n.getLang('timeschedule_add_schedule_text2')}</Text>
    <Spacer/>
    {isSupportMotionSwitch() && <View style={{
      flexDirection: 'row',
      alignItems: 'center',
    }}>
        <Text style={{
          color: props.theme?.global.fontColor,
          fontSize: cx(14),
          flex: 1
        }}>{I18n.getLang('motion_detection_no_safe_mode_trigger_text')}</Text>
        <SwitchButton
            value={state.motionSwitch}
            onValueChange={async (value) => {
              await setMotionSwitch(value);
              state.motionSwitch = value;
              state.isMotionSwitchOn = !isSupportMotionSwitch() || value;
            }}
        />
    </View>}
    {isSupportMotionSwitch() && <Spacer/>}
    {isSupportMotionSensitivity() && state.isMotionSwitchOn && <TextFieldStyleButton
        placeholder={I18n.getLang('motion_detection_selectionfield_topic_text')}
        text={state.sensitivity}
        onPress={() => {
          const params: SelectPageParams<string> = {
            title: I18n.getLang('motion_detection_selectionfield_topic_text'),
            data: state.itemList,
            onSelect: selectPageData => {
              setMotionSensitivity(selectPageData.value).then();
              state.sensitivity = getTriggerSensitivityName(selectPageData.value)
              state.itemList = getTriggerSensitivityList(selectPageData.value);
            }
          }
          toSelectPage(navigation, params)
        }}/>}
    {isSupportMotionSensitivity() && state.isMotionSwitchOn && <Spacer/>}

    {isSupportMotionAreaSwitch() && state.isMotionSwitchOn && <ActivityAreaSettings/>}

    {isSupportMotionRecordingTime() &&
        <TouchableOpacity onPress={() => {
          const params: SelectPageParams<string> = {
            title: I18n.getLang('motion_detection_video_recording_duration'),
            data: state.videoRecordingDurationList,
            onSelect: selectPageData => {
              setVideoRecordingDuration(selectPageData.value).then();
              state.videoRecordingDuration = getVideoRecordingDurationName(selectPageData.value)
              state.videoRecordingDurationList = getVideoRecordingDurationList(selectPageData.value);
            }
          }
          toSelectPage(navigation, params)
        }}>
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: cx(20)}}>
                <Text style={{
                  color: props.theme?.global.fontColor,
                  fontSize: cx(14),
                  flex: 1
                }}>{I18n.getLang('motion_detection_video_recording_duration')}</Text>
                <Text>{state.videoRecordingDuration}</Text>
                <IconFont name={'arrow'} color={props.theme?.global.secondFontColor} size={cx(11)}/>
            </View>
        </TouchableOpacity>}
    {isSupportMotionRecordingTime() && <Spacer/>}

    {isSupportPIRAlarmInterval() &&
        <TouchableOpacity onPress={() => {
          const params: SelectPageParams<string> = {
            title: I18n.getLang('motion_detection_alarm_interval'),
            data: state.alarmIntervalList,
            onSelect: selectPageData => {
              setAlarmInterval(selectPageData.value).then();
              state.alarmInterval = getAlarmIntervalName(selectPageData.value)
              state.alarmIntervalList = getAlarmIntervalList(selectPageData.value);
            }
          }
          toSelectPage(navigation, params)
        }}>
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: cx(20)}}>
                <Text style={{
                  color: props.theme?.global.fontColor,
                  fontSize: cx(14),
                  flex: 1
                }}>{I18n.getLang('motion_detection_alarm_interval')}</Text>
                <Text>{state.alarmInterval}</Text>
                <IconFont name={'arrow'} color={props.theme?.global.secondFontColor} size={cx(11)}/>
            </View>
        </TouchableOpacity>}
    {isSupportPIRAlarmInterval() && <Spacer/>}

    {isSupportMotionTracking() && <View style={{
      flexDirection: 'row',
      alignItems: 'center',
    }}>
        <Text style={{
          color: props.theme?.global.fontColor,
          fontSize: cx(14),
          flex: 1
        }}>{I18n.getLang('device_menu_camera_thirdbox_text1')}</Text>
        <SwitchButton
            value={state.motionTracking}
            onValueChange={async (value) => {
              await setMotionTracking(value);
              state.motionTracking = value;
            }}
        />
    </View>}
    {isSupportMotionTracking() && <Spacer/>}
  </View>)
})

const getTriggerSensitivityName = (value: string): string => {
  switch (value) {
    case TriggerSensitivity.High:
      return I18n.getLang('contact_sensor_battery_state1')
    case TriggerSensitivity.Middle:
      return I18n.getLang('contact_sensor_battery_state2')
    case TriggerSensitivity.Low:
    default:
      return I18n.getLang('contact_sensor_battery_state3')
  }
}

const getTriggerSensitivityList = (selectedLux: string): SelectPageData<string>[] => {
  return Object.entries(TriggerSensitivity).map(([_, value]) => {
    return {text: `${getTriggerSensitivityName(value)}`, value: value, selected: selectedLux === value};
  })
};

export const getVideoRecordingDurationName = (value: string): string => {
  switch (value) {
    case VideoRecordingDuration.Duration30s:
      return `30${I18n.getLang('socket_settings_switch_off_s')}`;
    case VideoRecordingDuration.Duration20s:
      return `20${I18n.getLang('socket_settings_switch_off_s')}`;
    case VideoRecordingDuration.Duration10s:
    default:
      return `10${I18n.getLang('socket_settings_switch_off_s')}`;
  }
}

const getVideoRecordingDurationList = (selectedLux: string): SelectPageData<string>[] => {
  return Object.entries(VideoRecordingDuration).map(([_, value]) => {
    return {text: `${getVideoRecordingDurationName(value)}`, value: value, selected: selectedLux === value};
  })
};

const getAlarmIntervalName = (value: string): string => {
  switch (value) {
    case PIRAlarmInterval.Duration10s:
      return `20${I18n.getLang('socket_settings_switch_off_s')}`;
    case PIRAlarmInterval.Duration30s:
      return `40${I18n.getLang('socket_settings_switch_off_s')}`;
    case PIRAlarmInterval.Duration60s:
      return `70${I18n.getLang('socket_settings_switch_off_s')}`;
    case PIRAlarmInterval.Duration0s:
    default:
      return `10${I18n.getLang('socket_settings_switch_off_s')}`;
  }
}

const getAlarmIntervalList = (selectedLux: string): SelectPageData<string>[] => {
  return Object.entries(PIRAlarmInterval).map(([_, value]) => {
    return {text: `${getAlarmIntervalName(value)}`, value: value, selected: selectedLux === value};
  })
};
