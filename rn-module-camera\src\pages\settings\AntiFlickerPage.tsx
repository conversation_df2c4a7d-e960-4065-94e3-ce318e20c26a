import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import ThemeType from "@ledvance/base/src/config/themeType";
import {Utils} from "tuya-panel-kit";
import I18n from "@ledvance/base/src/i18n/index";
import LDVRadioGroup, {LDVRadioItemData} from "../../components/LDVRadioGroup";
import {AntiFlicker} from "../../utils/AntiFlicker";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useAntiFlicker} from "../../hooks/DeviceHooks";
import {useReactive} from "ahooks";
import Spacer from "@ledvance/base/src/components/Spacer";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const AntiFlickerPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const [antiFlicker, setAntiFlicker] = useAntiFlicker()
  const state = useReactive({
    antiFlicker: antiFlicker,
    loading: false,
    itemList: getItemList(),
  });

  useEffect(() => {
    state.antiFlicker = antiFlicker;
  }, [antiFlicker]);

  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('camera_settings_anti_flicker_topic')}
    >
      <Spacer height={cx(10)}/>
      <LDVRadioGroup
        style={{marginHorizontal: cx(24)}}
        data={state.itemList}
        checkedValue={state.antiFlicker}
        onCheckedChange={async (item: LDVRadioItemData) => {
          state.loading = true
          await setAntiFlicker(item.value);
          state.antiFlicker = item.value;
          state.loading = false
        }}/>
    </Page>
  )
}
export default withTheme(AntiFlickerPage)

export const getAntiFlickerItem = (value: string): string => {
  switch (value) {
    case AntiFlicker.Hz50:
      return I18n.getLang('camera_settings_anti_flicker_50hz');
    case AntiFlicker.Hz60:
      return I18n.getLang('camera_settings_anti_flicker_60hz');
    case AntiFlicker.Off:
    default:
      return I18n.getLang('watchapp_entitystate_off')
  }
}

const getItemList = (): LDVRadioItemData[] => {
  return Object.entries(AntiFlicker).map(([_, value]) => {
    return {
      title: getAntiFlickerItem(value),
      value: value,
    } as LDVRadioItemData
  });
}