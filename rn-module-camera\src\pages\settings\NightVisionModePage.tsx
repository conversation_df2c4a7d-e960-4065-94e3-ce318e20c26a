import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive, useUpdateEffect} from "ahooks";
import {Utils} from "tuya-panel-kit";
import {Text} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import LDVRadioGroup, {LDVRadioItemData} from "../../components/LDVRadioGroup";
import {useNightVisionMode} from "../../hooks/DeviceHooks";
import I18n from "@ledvance/base/src/i18n";
import {NightVisionMode} from "../../utils/NightVisionMode";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export const getNightVisionModeItem = (value: string): string => {
    switch (value) {
        case NightVisionMode.SmartMode:
            return I18n.getLang('camera_settings_night_vision_mode_firstbox_topic1');
        case NightVisionMode.InfraredMode:
            return I18n.getLang('camera_settings_night_vision_mode_firstbox_topic2');
        case NightVisionMode.IPLMode:
        default:
            return I18n.getLang('camera_settings_night_vision_mode_firstbox_topic3')
    }
}

const getItemList = (): LDVRadioItemData[] => {
    return Object.entries(NightVisionMode).map(([_, value]) => {
        return {
            title: getNightVisionModeItem(value),
            value: value,
        } as LDVRadioItemData
    });
}

const NightVisionModePage = (props: { theme?: ThemeType }) => {
    const dev = useDeviceInfo();
    const [nightVisionMode, setNightVisionMode] = useNightVisionMode()
    const state = useReactive({
        nightVisionMode: nightVisionMode,
        itemList: getItemList(),
    });

    useUpdateEffect(() => {
        state.nightVisionMode = nightVisionMode;
    }, [nightVisionMode]);

    return (
        <Page backText={dev.name}
              headlineText={I18n.getLang('camera_settings_night_vision_mode_topic')}
        >
            <Spacer height={cx(10)}/>
            <Text style={{
                marginHorizontal: cx(24),
                color: props.theme?.global.fontColor,
                fontSize: cx(14)
            }}>{I18n.getLang('camera_settings_night_vision_mode_description')}</Text>
            <Spacer/>
            <LDVRadioGroup
                style={{marginHorizontal: cx(24)}}
                data={state.itemList}
                checkedValue={state.nightVisionMode}
                onCheckedChange={async (item: LDVRadioItemData) => {
                    await setNightVisionMode(item.value);
                    state.nightVisionMode = item.value;
                }}/>
        </Page>
    )
}

export default withTheme(NightVisionModePage)
