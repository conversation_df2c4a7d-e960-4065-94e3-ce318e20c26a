import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import {Utils} from "tuya-panel-kit";
import {Text} from "react-native";
import Spacer from "@ledvance/base/src/components/Spacer";
import LDVRadioGroup, {LDVRadioItemData} from "../../components/LDVRadioGroup";
import {useNightVision} from "../../hooks/DeviceHooks";
import {NightVision} from "../../utils/NightVision";
import xlog from "../../utils/common";
import I18n from "@ledvance/base/src/i18n";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export const getNightVisionItem = (value: string): [string, string] => {
    switch (value) {
        case NightVision.Auto:
            return [I18n.getLang('camera_settings_night_vision_firstbox_topic1'), I18n.getLang('camera_settings_night_vision_firstbox_topic1_description')];
        case NightVision.On:
            return [I18n.getLang('camera_settings_night_vision_firstbox_topic2'), I18n.getLang('camera_settings_night_vision_firstbox_topic2_description')];
        case NightVision.Off:
        default:
            return [I18n.getLang('watchapp_entitystate_off'), I18n.getLang('camera_settings_night_vision_firstbox_topic3_description')]
    }
}

const getItemList = (): LDVRadioItemData[] => {
    return Object.entries(NightVision).map(([_, value]) => {
        const [title, description] = getNightVisionItem(value);
        return {
            title: title,
            value: value,
            description: description,
        } as LDVRadioItemData
    });
}

const NightVisionPage = (props: { theme?: ThemeType }) => {
    const dev = useDeviceInfo();
    const [nightVision, setNightVision] = useNightVision()
    xlog("useNightVision===================>", nightVision)
    const state = useReactive({
        nightVision: nightVision,
        itemList: getItemList(),
    });

    useEffect(() => {
        state.nightVision = nightVision;
    }, [nightVision]);

    return (
        <Page backText={dev.name}
              headlineText={I18n.getLang('camera_settings_night_vision_topic')}
        >
            <Spacer height={cx(10)}/>
            <Text style={{
                marginHorizontal: cx(24),
                color: props.theme?.global.fontColor,
                fontSize: cx(14)
            }}>{I18n.getLang('camera_settings_night_vision_description')}</Text>
            <Spacer/>
            <LDVRadioGroup
                style={{marginHorizontal: cx(24)}}
                data={state.itemList}
                checkedValue={state.nightVision}
                onCheckedChange={async (item: LDVRadioItemData) => {
                    await setNightVision(item.value);
                    state.nightVision = item.value;
                }}/>
        </Page>
    )
}

export default withTheme(NightVisionPage)
