import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import React, {useMemo} from "react";
import {IconFont, Modal, PickerDataProps, Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'
import I18n from "@ledvance/base/src/i18n";
import {Text, TouchableOpacity, View} from "react-native";
import ItemView from "../../components/ItemView";
import {
  isSupportWirelessLowPower,
  isSupportWirelessPowerMode,
  useWirelessElectricity,
  useWirelessLowPower,
  useWirelessPowerMode
} from "../../hooks/DeviceHooks";
import {useReactive, useUpdateEffect} from "ahooks";
import {WirelessPowerMode} from "../../utils/WirelessPowerMode";

const {withTheme} = Utils.ThemeUtils

const cx = Utils.RatioUtils.convertX;

const PowerManagementSettingsPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const [wirelessLowPower, setWirelessLowPower] = useWirelessLowPower();
  const [wirelessPowerMode,] = useWirelessPowerMode();
  const wirelessElectricity = useWirelessElectricity();
  const state = useReactive({
    wirelessElectricity: wirelessElectricity,
    wirelessLowPower: wirelessLowPower,
    loading: false,
    showLowBatteryAlarmThresholdModal: false,
    wirelessPowerMode: getWirelessPowerModeName(wirelessPowerMode),
  });
  useUpdateEffect(() => {
    state.wirelessElectricity = wirelessElectricity;
    state.wirelessLowPower = wirelessLowPower;
    state.wirelessPowerMode = getWirelessPowerModeName(wirelessPowerMode);
  }, [wirelessElectricity, wirelessLowPower, wirelessPowerMode]);
  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('camera_settings_power_management_settings_topic')}
    >
      <View style={{marginHorizontal: cx(24)}}>
        {/*Battery Remaining*/}
        <ItemView
          title={I18n.getLang('power_management_battery_remaining')}
          content={`${state.wirelessElectricity}%`}
          showArrow={false}>

          {/*Power Source*/}
          {isSupportWirelessPowerMode() && <View style={{flexDirection: 'row', alignItems: 'center', padding: cx(16)}}>
              <Text style={{
                color: props.theme?.global.fontColor,
                fontSize: cx(16),
                fontWeight: 'bold',
                flex: 1
              }}>{I18n.getLang('power_management_power_source')}</Text>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                  <Text style={{
                    fontSize: cx(14),
                    color: props.theme?.global.secondFontColor,
                    maxWidth: cx(80)
                  }}>{state.wirelessPowerMode}</Text>
              </View>
          </View>}

          {isSupportWirelessLowPower() && <TouchableOpacity onPress={() => {
            state.showLowBatteryAlarmThresholdModal = true;
          }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: cx(16),
                paddingStart: cx(16),
                paddingEnd: cx(6)
              }}>
                  <Text style={{
                    color: props.theme?.global.fontColor,
                    fontSize: cx(16),
                    fontWeight: 'bold',
                    flex: 1
                  }}>{I18n.getLang('power_management_low_battery_alarm_threshold')}</Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                      <Text style={{
                        fontSize: cx(14),
                        color: props.theme?.global.secondFontColor,
                        maxWidth: cx(80)
                      }}>{`${state.wirelessLowPower}%`}</Text>
                      <IconFont name={'arrow'} color="#444" size={cx(11)}/>
                  </View>
              </View>
          </TouchableOpacity>}
        </ItemView>
      </View>
      <Modal.Picker
        title={I18n.getLang('power_management_low_battery_alarm_threshold')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        visible={state.showLowBatteryAlarmThresholdModal}
        dataSource={getLowBatteryThresholdItemList()}
        value={`${state.wirelessLowPower}`}
        onMaskPress={() => {
          state.showLowBatteryAlarmThresholdModal = false;
        }}
        label={'%'}
        labelOffset={cx(30)}
        onConfirm={value => {
          state.showLowBatteryAlarmThresholdModal = false;
          const battery = parseInt(value);
          setWirelessLowPower(battery).then()
          state.wirelessLowPower = battery
        }}
        onCancel={() => {
          state.showLowBatteryAlarmThresholdModal = false;
        }}
        motionConfig={{hideDuration: 0}}
      />
    </Page>
  )
}

export default withTheme(PowerManagementSettingsPage);

const getWirelessPowerModeName = (value: string): string => {
  switch (value) {
    case WirelessPowerMode.PowerCable:
      return I18n.getLang('power_management_power_source_power_cable');
    case WirelessPowerMode.Battery:
    default:
      return I18n.getLang('power_management_power_source_battery');
  }
}

const getLowBatteryThresholdItemList = (): PickerDataProps[] => {
  return useMemo(() => {
    return Array.from({length: 50 - 10 + 1}, (_, i) => i + 10).map(value => {
      return {label: `${value}`, value: `${value}`};
    });
  }, []);
};
