import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import {Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import LDVRadioGroup, {LDVRadioItemData} from "../../components/LDVRadioGroup";
import {useRecordMode} from "../../hooks/DeviceHooks";
import xlog from "../../utils/common";
import {RecordingMode} from "../../utils/RecordingMode";
import {showDialog} from "@ledvance/base/src/utils/common";
import I18n from "@ledvance/base/src/i18n";

const cx = Utils.RatioUtils.convertX;

export const getRecordingModeItem = (value: string): [string, string] => {
    switch (value) {
        case RecordingMode.MotionTriggered:
            return [I18n.getLang('camera_settings_recording_mode_firstbox_topic1'), I18n.getLang('camera_settings_recording_mode_firstbox_topic1_description')];
        case RecordingMode.Continuous:
        default:
            return [I18n.getLang('camera_settings_recording_mode_firstbox_topic2'), I18n.getLang('camera_settings_recording_mode_firstbox_topic2_description')]
    }
}

const getItemList = (): LDVRadioItemData[] => {
    return Object.entries(RecordingMode).map(([_, value]) => {
        const [title, description] = getRecordingModeItem(value);
        return {
            title: title,
            value: value,
            description: description,
        } as LDVRadioItemData
    });
}

const RecordingModePage = () => {
    const dev = useDeviceInfo();
    const [recordMode, setRecordMode] = useRecordMode()
    xlog("useRecordMode===================>", recordMode)
    const state = useReactive({
        recordMode: recordMode,
        itemList: getItemList(),
    });

    useEffect(() => {
        state.recordMode = recordMode;
    }, [recordMode]);

    return (
        <Page backText={dev.name}
              headlineText={I18n.getLang('device_menu_camera_fourthbox_text2')}
        >
            <Spacer/>
            <LDVRadioGroup
                style={{marginHorizontal: cx(24)}}
                data={state.itemList}
                checkedValue={state.recordMode}
                onCheckedChange={async (item: LDVRadioItemData) => {
                    if (item.value === RecordingMode.MotionTriggered) {
                        showDialog({
                            method: 'confirm',
                            title: I18n.getLang('camera_settings_recording_mode_question_topic'),
                            subTitle: I18n.getLang('camera_settings_recording_mode_question_note'),
                            cancelText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_no_text'),
                            confirmText: I18n.getLang('conflict_dialog_save_item_timeschedule_answer_yes_text'),
                            onConfirm: async (data, {close}) => {
                                close()
                                await setRecordMode(item.value).then();
                                state.recordMode = item.value;
                            }
                        })
                        return
                    }
                    await setRecordMode(item.value);
                    state.recordMode = item.value;
                }}/>
        </Page>
    )
}

export default RecordingModePage;