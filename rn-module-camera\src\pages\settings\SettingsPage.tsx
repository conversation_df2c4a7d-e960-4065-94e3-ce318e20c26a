import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import ItemView from "../../components/ItemView";
import {ScrollView, Text, TouchableOpacity, View} from "react-native";
import {IconFont, Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useNavigation} from "@react-navigation/core";
import {RouterKey} from "../../navigation/Router";
import {
  isSupportAntiDismantle,
  isSupportAntiFlicker,
  isSupportBasicIndicator,
  isSupportDeviceRestart,
  isSupportFlipScreen,
  isSupportHumanoidFocus,
  isSupportNightVision,
  isSupportNightVisionMode,
  isSupportOnvifSwitch, isSupportPresetPoint, isSupportCameraCalibration,
  isSupportRecordMode,
  isSupportSDCardRecordSwitch,
  isSupportSecurityMode,
  isSupportTimeWatermark, isSupportWirelessElectricity, toCameraPresetPointPage, useAntiDismantle,
  useAntiFlicker,
  useBasicIndicator,
  useDeviceRestart,
  useExistSDCard,
  useFlipScreen,
  useHumanoidFocus,
  useNightVision,
  useNightVisionMode,
  useRecordMode,
  useRecordSwitch,
  useSecurityMode,
  useTimeWatermark, useCameraCalibration, isSupportVolume
} from "../../hooks/DeviceHooks";
import {useReactive, useUpdateEffect} from "ahooks";
import {getNightVisionItem} from "./NightVisionPage";
import {getRecordingModeItem} from "./RecordingModePage";
import {getNightVisionModeItem} from "./NightVisionModePage";
import ThemeType from '@ledvance/base/src/config/themeType'
import {showDialog} from "@ledvance/base/src/utils/common";
import {NativeApi} from "@ledvance/base/src/api/native";
import {getAntiFlickerItem} from "./AntiFlickerPage";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const SettingsPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const navigation = useNavigation();
  const [nightVision] = useNightVision();
  const [nightVisionMode] = useNightVisionMode();
  const [recordMode] = useRecordMode();
  const [securityMode, setSecurityMode] = useSecurityMode();
  const existSDCard = useExistSDCard();
  const [flipScreen, setFlipScreen] = useFlipScreen();
  const [timeWatermark, setTimeWatermark] = useTimeWatermark();
  const [basicIndicator, setBasicIndicator] = useBasicIndicator();
  const [recordSwitch, setRecordSwitch] = useRecordSwitch();
  const [humanoidFocus, setHumanoidFocus] = useHumanoidFocus();
  const [antiDismantle, setAntiDismantle] = useAntiDismantle();
  const [antiFlicker,] = useAntiFlicker();
  const [, setDeviceRestart] = useDeviceRestart();
  const [, setCameraCalibration] = useCameraCalibration();
  const state = useReactive({
    nightVisionMode: getNightVisionModeItem(nightVisionMode),
    nightVision: getNightVisionItem(nightVision)[0],
    recordMode: getRecordingModeItem(recordMode)[0],
    flipScreen: flipScreen,
    timeWatermark: timeWatermark,
    securityMode: securityMode,
    existSDCard: existSDCard,
    basicIndicator: basicIndicator,
    recordSwitch: recordSwitch,
    humanoidFocus: humanoidFocus,
    antiDismantle: antiDismantle,
    antiFlicker: getAntiFlickerItem(antiFlicker),
    loading: false,
  });

  useUpdateEffect(() => {
    state.nightVisionMode = getNightVisionModeItem(nightVisionMode);
    state.nightVision = getNightVisionItem(nightVision)[0];
    state.recordMode = getRecordingModeItem(recordMode)[0];
    state.flipScreen = flipScreen;
    state.securityMode = securityMode;
    state.existSDCard = existSDCard;
    state.timeWatermark = timeWatermark;
    state.basicIndicator = basicIndicator;
    state.recordSwitch = recordSwitch;
    state.humanoidFocus = humanoidFocus;
    state.antiDismantle = antiDismantle;
    state.antiFlicker = getAntiFlickerItem(antiFlicker);
  }, [nightVision, nightVisionMode, recordMode, flipScreen, securityMode, existSDCard, timeWatermark, basicIndicator, recordSwitch, humanoidFocus, antiFlicker, antiDismantle]);
  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('contact_sensor_specific_settings')}>
      <ScrollView>
        <Spacer/>
        <View style={{marginHorizontal: cx(24)}}>

          {isSupportBasicIndicator() && <ItemView
              title={I18n.getLang('camera_status_indicator')}
              switchValue={state.basicIndicator}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setBasicIndicator(value);
                state.basicIndicator = value;
                state.loading = false;
              }}
          />}
          {isSupportBasicIndicator() && <Spacer/>}

          {isSupportSecurityMode() && <ItemView
              title={I18n.getLang('camera_human_private_mode')}
              description={I18n.getLang('privatemode_description')}
              switchValue={state.securityMode}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setSecurityMode(value);
                state.securityMode = value;
                state.loading = false;
              }}
          />}
          {isSupportSecurityMode() && <Spacer/>}

          {isSupportFlipScreen() && <ItemView
              title={I18n.getLang('device_menu_camera_secondbox_text3')}
              switchValue={state.flipScreen}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setFlipScreen(value);
                state.flipScreen = value;
                state.loading = false;
              }}/>}

          {isSupportFlipScreen() && <Spacer/>}

          {isSupportTimeWatermark() && <ItemView
              title={I18n.getLang('device_menu_camera_secondbox_text4')}
              switchValue={state.timeWatermark}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setTimeWatermark(value);
                state.timeWatermark = value;
                state.loading = false;
              }}/>}

          {isSupportTimeWatermark() && <Spacer/>}

          {isSupportHumanoidFocus() && <ItemView
              title={I18n.getLang('camera_settings_humanoid_focus_topic')}
              switchValue={state.humanoidFocus}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setHumanoidFocus(value);
                state.humanoidFocus = value;
                state.loading = false;
              }}/>}

          {isSupportHumanoidFocus() && <Spacer/>}

          {isSupportAntiDismantle() && <ItemView
              title={I18n.getLang('camera_settings_anti_dismantle_topic')}
              switchValue={state.antiDismantle}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setAntiDismantle(value);
                state.antiDismantle = value;
                state.loading = false;
              }}/>}

          {isSupportAntiDismantle() && <Spacer/>}

          {isSupportWirelessElectricity() && <ItemView
              title={I18n.getLang('camera_settings_power_management_settings_topic')}
              content={' '}
              onItemPress={() => {
                navigation.navigate(RouterKey.power_management_settings)
              }}
          />}
          {isSupportWirelessElectricity() && <Spacer/>}

          {isSupportPresetPoint() && <ItemView
              title={I18n.getLang('camera_preset_point')}
              content={' '}
              onItemPress={() => {
                toCameraPresetPointPage(dev.devId)
              }}
          />}
          {isSupportPresetPoint() && <Spacer/>}

          {isSupportAntiFlicker() && <ItemView
              title={I18n.getLang('camera_settings_anti_flicker_topic')}
              content={state.antiFlicker}
              onItemPress={() => {
                navigation.navigate(RouterKey.anti_flicker)
              }}
          />}
          {isSupportAntiFlicker() && <Spacer/>}

          {isSupportNightVision() && <ItemView
              title={I18n.getLang('camera_settings_night_vision_topic')}
              content={state.nightVision}
              onItemPress={() => {
                navigation.navigate(RouterKey.night_vision)
              }}
          />}
          {isSupportNightVision() && <Spacer/>}

          {isSupportNightVisionMode() && <ItemView
              title={I18n.getLang('camera_settings_night_vision_mode_topic')}
              content={state.nightVisionMode}
              onItemPress={() => {
                navigation.navigate(RouterKey.night_vision_mode)
              }}
          />}
          {isSupportNightVisionMode() && <Spacer/>}

          {state.existSDCard && isSupportSDCardRecordSwitch() && <ItemView
              title={I18n.getLang('camera_local_recording')}
              switchValue={state.recordSwitch}
              onSwitchChange={async (value) => {
                state.loading = true;
                await setRecordSwitch(value)
                state.recordSwitch = value;
                state.loading = false;
              }}
          >
            {state.recordSwitch && isSupportRecordMode() && <TouchableOpacity onPress={() => {
              navigation.navigate(RouterKey.recording_mode)
            }}>
                <View style={{flexDirection: 'row', alignItems: 'center', padding: cx(16)}}>
                    <Text style={{
                      color: props.theme?.global.fontColor,
                      fontSize: cx(16),
                      fontWeight: 'bold',
                      flex: 1
                    }}>{I18n.getLang('device_menu_camera_fourthbox_text2')}</Text>
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                        <Text style={{
                          fontSize: cx(14),
                          color: props.theme?.global.secondFontColor,
                          maxWidth: cx(80)
                        }}>{state.recordMode}</Text>
                        <IconFont name={'arrow'} color="#444" size={cx(11)}/>
                    </View>
                </View>
            </TouchableOpacity>}
          </ItemView>}
          {state.existSDCard && isSupportSDCardRecordSwitch() && <Spacer/>}

          {state.existSDCard && <ItemView
              title={I18n.getLang('camera_settings_sd_storage_topic')}
              content={' '}
              onItemPress={() => {
                navigation.navigate(RouterKey.sd_card_config)
              }}
          />}
          {state.existSDCard && <Spacer/>}

          {isSupportOnvifSwitch() && <ItemView
              title={I18n.getLang('camera_settings_onvif_topic')}
              content={' '}
              onItemPress={() => {
                navigation.navigate(RouterKey.onvif)
              }}
          />}
          {isSupportOnvifSwitch() && <Spacer/>}

          {isSupportCameraCalibration() && <ItemView
              title={I18n.getLang('camera_calibration')}
              content={' '}
              onItemPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('camera_calibration_desc'),
                  confirmText: I18n.getLang(`auto_scan_system_wifi_confirm`),
                  cancelText: I18n.getLang(`auto_scan_system_cancel`),
                  onConfirm: async (_, {close}) => {
                    close();
                    state.loading = true;
                    await setCameraCalibration(true);
                    state.loading = false;
                  }
                });
              }}
          />}
          {isSupportCameraCalibration() && <Spacer/>}

          {isSupportVolume() && <ItemView
              title={I18n.getLang('dotit_volume')}
              content={' '}
              onItemPress={() => {
                navigation.navigate(RouterKey.volume)
              }}
          />}
          {isSupportVolume() && <Spacer/>}

          {isSupportDeviceRestart() && <ItemView
              title={I18n.getLang('camera_settings_device_restart')}
              content={' '}
              onItemPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('camera_settings_device_restart'),
                  subTitle: I18n.getLang(`camera_restart_device_dialog_content`),
                  confirmText: I18n.getLang(`auto_scan_system_wifi_confirm`),
                  cancelText: I18n.getLang(`auto_scan_system_cancel`),
                  onConfirm: async (_, {close}) => {
                    close();
                    state.loading = true;
                    await setDeviceRestart(true);
                    state.loading = false;
                    NativeApi.back()
                  }
                });
              }}
          />}
          {isSupportDeviceRestart() && <Spacer/>}
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(SettingsPage)
