import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'
import {isSupportVolume} from "../../hooks/DeviceHooks";
import CameraVolumeView from "../home/<USER>";
import I18n from "@ledvance/base/src/i18n";

const {withTheme} = Utils.ThemeUtils

const VolumePage = (_: { theme?: ThemeType }) => {
  return (
    <Page backText={I18n.getLang('contact_sensor_specific_settings')}
          headlineText={I18n.getLang('dotit_volume')}
    >
      {isSupportVolume() && <CameraVolumeView/>}

    </Page>
  )
}

export default withTheme(VolumePage);