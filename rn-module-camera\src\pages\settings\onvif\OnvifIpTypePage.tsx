import Page from "@ledvance/base/src/components/Page";
import React from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {OnvifIpType} from "../../../utils/OnvifIpType";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import Spacer from "@ledvance/base/src/components/Spacer";
import LDVRadioGroup, {LDVRadioItemData} from "../../../components/LDVRadioGroup";
import {Utils} from "tuya-panel-kit";
import ThemeType from "@ledvance/base/src/config/themeType";
import {useReactive, useUpdateEffect} from "ahooks";
import {useOnvifIpTypeConfig} from "../../../hooks/DeviceHooks";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const OnvifIpTypePage = (_: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const [onvifIpTypeConfig, setOnvifIpTypeConfig] = useOnvifIpTypeConfig();
  const state = useReactive({
    onvifIpTypeConfig: onvifIpTypeConfig,
    itemList: getItemList(),
  });

  useUpdateEffect(() => {
    state.onvifIpTypeConfig = onvifIpTypeConfig;
  }, [onvifIpTypeConfig]);

  return (
    <Page backText={dev.name}
          headlineText={I18n.getLang('camera_settings_onvif_ip_type_topic')}
    >
      <Spacer height={cx(10)}/>
      <LDVRadioGroup
        style={{marginHorizontal: cx(24)}}
        data={state.itemList}
        checkedValue={state.onvifIpTypeConfig}
        onCheckedChange={async (item: LDVRadioItemData) => {
          await setOnvifIpTypeConfig(item.value);
          state.onvifIpTypeConfig = item.value;
        }}/>
    </Page>
  )
}

export default withTheme(OnvifIpTypePage)
const getItemList = (): LDVRadioItemData[] => {
  return Object.entries(OnvifIpType).map(([_, value]) => {
    return {
      title: getOnvifIpTypeItem(value),
      value: value,
    } as LDVRadioItemData
  });
}
export const getOnvifIpTypeItem = (value: string): string => {
  switch (value) {
    case OnvifIpType.DynamicIp:
      return I18n.getLang('camera_settings_onvif_ip_type_dynamic');
    case OnvifIpType.StaticIp:
    default:
      return I18n.getLang('camera_settings_onvif_ip_type_static')
  }
}