import Page from "@ledvance/base/src/components/Page";
import React from "react";
import ThemeType from "@ledvance/base/src/config/themeType";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {Utils} from "tuya-panel-kit";
import {
  isSupportOnvifIpAddress,
  isSupportOnvifIpTypeConfig,
  isSupportOnvifResetPassword,
  isSupportOnvifSwitch,
  useIsNeedSetOnvifPWD,
  useOnvifIpAddress,
  useOnvifIpTypeConfig,
  useOnvifSwitch
} from "../../../hooks/DeviceHooks";
import ItemView from "../../../components/ItemView";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useReactive, useUpdateEffect} from "ahooks";
import {View} from "react-native";
import {getOnvifIpTypeItem} from "./OnvifIpTypePage";
import {useNavigation} from "@react-navigation/core";
import {RouterKey} from "../../../navigation/Router";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils
const OnvifPage = (_: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const navigation = useNavigation();
  const [onvifSwitch, setOnvifSwitch] = useOnvifSwitch();
  const isForceSetPwd = useIsNeedSetOnvifPWD();
  const [onvifIpTypeConfig,] = useOnvifIpTypeConfig();
  const ipAddress = useOnvifIpAddress();
  const state = useReactive({
    onvifSwitch: onvifSwitch,
    onvifIpTypeConfig: getOnvifIpTypeItem(onvifIpTypeConfig),
    ipAddress: ipAddress,
    isForceSetPwd: isForceSetPwd,
    loading: false,
  });

  useUpdateEffect(() => {
    state.onvifSwitch = onvifSwitch;
    state.onvifIpTypeConfig = getOnvifIpTypeItem(onvifIpTypeConfig);
    state.ipAddress = ipAddress;
    state.isForceSetPwd = isForceSetPwd;
  }, [onvifSwitch, onvifIpTypeConfig, ipAddress, isForceSetPwd])

  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('camera_settings_onvif_topic')}>
      <View style={{marginHorizontal: cx(24)}}>
        <Spacer/>
        {isSupportOnvifSwitch() && <ItemView
            title={I18n.getLang('camera_settings_onvif_switch_topic')}
            switchValue={state.onvifSwitch}
            onSwitchChange={async (value) => {
              if (value && state.isForceSetPwd) {
                navigation.navigate(RouterKey.onvif_reset_password, {isForceSetPwd: true});
                return
              }
              state.loading = true
              await setOnvifSwitch(value);
              state.onvifSwitch = value;
              state.loading = false
            }}/>}

        {isSupportOnvifSwitch() && <Spacer/>}

        {isSupportOnvifIpTypeConfig() && <ItemView
            title={I18n.getLang('camera_settings_onvif_ip_type_topic')}
            content={state.onvifIpTypeConfig}
            onItemPress={() => {
              navigation.navigate(RouterKey.onvif_mode);
            }}
        />}
        {isSupportOnvifIpTypeConfig() && <Spacer/>}

        {isSupportOnvifIpAddress() && <ItemView
            title={I18n.getLang('camera_settings_onvif_ip_topic')}
            showArrow={false}
            content={state.ipAddress}
        />}

        {isSupportOnvifIpAddress() && <Spacer/>}

        <ItemView
          title={I18n.getLang('camera_user')}
          showArrow={false}
          content={'admin'}
        />
        <Spacer />

        {isSupportOnvifResetPassword() && <ItemView
            title={I18n.getLang('setting_change_title')}
            content={' '}
            onItemPress={() => {
              navigation.navigate(RouterKey.onvif_reset_password, {isForceSetPwd: state.isForceSetPwd});
            }}
        />}

        {isSupportOnvifResetPassword() && <Spacer/>}
      </View>
    </Page>
  )
}

export default withTheme(OnvifPage)
