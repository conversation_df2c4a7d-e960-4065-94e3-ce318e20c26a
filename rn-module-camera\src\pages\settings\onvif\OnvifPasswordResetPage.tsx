import Page from "@ledvance/base/src/components/Page";
import React from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {Utils} from "tuya-panel-kit";
import {useParams} from "@ledvance/base/src/hooks/Hooks";
import ThemeType from "@ledvance/base/src/config/themeType";
import {Text, View} from "react-native";
import DeleteButton from "@ledvance/base/src/components/DeleteButton";
import Spacer from "@ledvance/base/src/components/Spacer";
import InfoText from "@ledvance/base/src/components/InfoText";
import res from "@ledvance/base/src/res";
import {useReactive, useUpdateEffect} from "ahooks";
import {useOnvifChangePwd, useOnvifSwitch} from "../../../hooks/DeviceHooks";
import {useNavigation} from '@react-navigation/native'
import {showDialog} from "@ledvance/base/src/utils/common";
import PasswordTextField from "../../../components/PasswordTextField";

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils

const OnvifPasswordResetPage = (props: { theme?: ThemeType }) => {
  const dev = useDeviceInfo();
  const navigation = useNavigation()
  const [onvifChangePwd, setOnvifChangePwd] = useOnvifChangePwd();
  const [, setOnvifSwitch] = useOnvifSwitch();
  const {isForceSetPwd} = useParams<{ isForceSetPwd: boolean }>()
  const state = useReactive({
    currentPwd: '',
    newPwd: '',
    disabledButton: true,
    loading: false,
  });
  const isValidPassword = (str: string) => {
    return str.length >= 8 && str.length <= 32;
  };

  useUpdateEffect(() => {
    state.disabledButton = isForceSetPwd ? !isValidPassword(state.newPwd) : (!isValidPassword(state.currentPwd) || !isValidPassword(state.newPwd));
  }, [state.currentPwd, state.newPwd, isForceSetPwd]);

  useUpdateEffect(() => {
    const res = JSON.parse(onvifChangePwd)
    if (res?.res == 'failed') {
      state.loading = false
      showDialog({
        method: 'alert',
        title: I18n.getLang('pos_mode_switching_fail_tips'),
        confirmText:I18n.getLang('ceiling_fan_direction_info_button_label'),
        onConfirm: (_, {close}) => {
          close()
        }
      })
    } else if (res?.res == 'ok') {
      if (isForceSetPwd){
        setOnvifSwitch(true).then()
      }
      navigation.goBack()
    }
  }, [onvifChangePwd])

  return (
    <Page backText={dev.name}
          loading={state.loading}
          headlineText={I18n.getLang('setting_change_title')}>

      <View style={{marginHorizontal: cx(24)}}>
        <Spacer/>
        {!isForceSetPwd && <View>
            <Text style={{
              color: props.theme?.global.fontColor,
              fontSize: cx(16),
              fontWeight: 'bold',
            }}>{I18n.getLang('setting_cur_passwd')}</Text>
            <PasswordTextField
                value={state.currentPwd}
                onChangeText={text => {
                  state.currentPwd = text;
                }}
                maxLength={33}
                showError={state.currentPwd.length > 32}
                errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}/>
        </View>}
        <Text style={{
          color: props.theme?.global.fontColor,
          fontSize: cx(16),
          fontWeight: 'bold',
        }}>{I18n.getLang('setting_set_passwd')}</Text>
        <PasswordTextField
          value={state.newPwd}
          onChangeText={text => {
            state.newPwd = text;
          }}
          maxLength={33}
          showError={state.newPwd.length > 32}
          errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}
        />

        <InfoText
          icon={res.ic_info}
          text={I18n.getLang('camera_settings_onvif_set_password_tips')}
          style={{width: 'auto', alignItems: 'center'}}
          textStyle={{flex: undefined}}/>
        <Spacer/>
        <DeleteButton
          disabled={state.disabledButton}
          style={{backgroundColor: state.disabledButton ? props.theme?.button.disabled : props.theme?.button.primary}}
          text={I18n.getLang('auto_scan_system_wifi_confirm')}
          onPress={() => {
            state.loading = true
            setOnvifChangePwd(state.newPwd, state.currentPwd).then();
          }}
        />
      </View>
    </Page>
  )
}
export default withTheme(OnvifPasswordResetPage)
