import CameraManager from "@tuya/tuya-panel-ipc-sdk/src/ty-ipc-native/nativeApi";

export class CameraDirectionControl {
    static startPtzUp = () => {
        CameraManager.startPtzUp();
    }
    static startPtzDown = () => {
        CameraManager.startPtzDown();
    }
    static startPtzLeft = () => {
        CameraManager.startPtzLeft();
    }
    static startPtzRight = () => {
        CameraManager.startPtzRight();
    }
    static stopPtz = () => {
        CameraManager.stopPtz();
    }
}