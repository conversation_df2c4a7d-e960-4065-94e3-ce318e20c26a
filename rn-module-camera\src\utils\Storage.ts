import xlog from "./common";
import {NativeModules} from "react-native";

const devicePanel = NativeModules.LDVDevicePanelManager;

export class Storage {
    private static get<T>(key: string, defaultValue: T) {
        return new Promise<T>(async (resolve) => {
            let value: string | null = null;
            try {
                value = await devicePanel.getString(key, `${defaultValue}`);
            } catch (e) {
                xlog("Storage==========>get", key, e);
            }
            xlog("Storage==========>get", key, value, defaultValue);
            resolve(value === null ? defaultValue : this.parseValue(value, defaultValue))
        });
    }

    private static parseValue<T>(value: string, defaultValue: T): T {
        try {
            return <T>JSON.parse(value);
        } catch (e) {
            return defaultValue;
        }
    }

    private static set<T>(key: string, value: T) {
        xlog("Storage==========>set", key, value);
        devicePanel.putString(key, `${value}`)
    }

    static getBoolean(key: string, defaultValue: boolean = false) {
        return this.get<boolean>(key, defaultValue)
    }

    static setBoolean(key: string, value: boolean) {
        this.set(key, `${value}`);
    }

    static getNumber(key: string, defaultValue: number = 0) {
        return this.get<number>(key, defaultValue);
    }

    static setNumber(key: string, value: number) {
        this.set(key, `${value}`);
    }

    static getString(key: string, defaultValue: string = "") {
        return this.get<string>(key, defaultValue);
    }

    static setString(key: string, value: string) {
        this.set(key, value);
    }
}