import {Dimensions, Platform} from "react-native";
import {PickerDataProps, Utils} from "tuya-panel-kit";
import {NativeApi} from "@ledvance/base/src/api/native";
import I18n from "@ledvance/base/src/i18n";

const {statusBarHeight} = Utils.RatioUtils;

const {width: winWidth, height: winHeight} = Dimensions.get('screen');

// 全屏播放器宽度
export const fullPlayerWidth =
    Platform.OS === 'ios' ? Math.ceil(winHeight) : Math.ceil(winHeight + statusBarHeight);
// 全屏播放器高度
export const fullPlayerHeight = Math.ceil(winWidth);
export const realFullPlayerWidth = Math.ceil(16 * fullPlayerHeight / 9)


export default function xlog(message?: any, ...optionalParams: any[]) {
    const paramsString = stringifyArray(optionalParams);
    console.log(`${message} ${paramsString}`);
    NativeApi.log(`${message} ${paramsString}`)
}

function stringifyArray(array: any[]): string {
    return array.map(item => {
        if (item === null || item === undefined) {
            return ''; // 或者你可以根据需求返回其他默认值
        } else if (typeof item === 'object') {
            return JSON.stringify(item); // 如果元素是对象，则转换为 JSON 字符串
        } else {
            return String(item); // 其他情况下，使用 String() 方法转换为字符串
        }
    }).join(' '); // 使用逗号和空格连接数组中的字符串
}

export const getNumberItemList = (minValue: number = 0, maxValue: number): PickerDataProps[] => {
    return Array.from({length: maxValue === minValue ? 1 : (maxValue - minValue) + 1}, (_, i) => i + minValue).map(value => {
        return {label: `${value}`, value: `${value}`}
    });
}


export const videoLoadText = {
    /* 返回状态进行定义
       status:  0: 设备离线 1: 隐私模式 2: 正在连接P2P通道 3: 通道构建失败 4: 正在获取视频流 5: 获取视频流失败 6: 正常播放
    */
    0: I18n.getLang('software_update_found_failed_update_error_5012'), // 设备离线
    1: I18n.getLang('camera_private_mode_sleep'), // 隐私模式
    2: I18n.getLang('camera_video_get_stream_ing'), // 正在连接P2P通道
    3: I18n.getLang('camera_video_get_stream_failure'), // 通道构建失败
    4: I18n.getLang('camera_video_get_stream_ing'), // 正在获取视频流
    5: I18n.getLang('camera_video_get_stream_failure'), // 获取视频流失败
    6: I18n.getLang('camera_video_get_stream_ing'), // 正常播放
    8: I18n.getLang('camera_video_channel_pause'), // 暂停播放 视频暂停中 点击开启
};

