# React Navigation TS 对外模板

[English](./README.md) | 简体中文

如需获取更多信息，请浏览 [涂鸦官方文档](https://docs.tuya.com)

对于中国大陆以外的用户，请删除 `.npmrc` 文件。

## 手动下载

```bash
$ curl https://codeload.github.com/tuya/tuya-panel-demo/tar.gz/master | tar -xz --strip=2 tuya-panel-demo-master/examples/basic-ts-navigation
$ mv basic tuya-panel-basic-ts-navigation-example
$ cd tuya-panel-basic-ts-navigation-example
```

## 介绍

React Navigation TS 模板是基于 react-navigation 三方库实现的涂鸦基础模板，模板中展示了 react-navigation 的部分动画效果，解决了对于大量列表数据对页面产生的卡顿现象。

您可以通过涂鸦 App 扫描以下二维码进行预览。

![reactNavigation](https://images.tuyacn.com/rms-static/5a4e6770-7b2b-11eb-b60c-35c3dc2e2583-1614671169383.png?tyName=react_navigation_ts.png)

## 快速运行

```bash
$ npm install && npm run start
# 或者
$ yarn && yarn start
```

## 许可证

Copyright © 2020
