import {
  useDp,
  useDeviceId,
  useTimeSchedule,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import { NativeApi } from '@ledvance/base/src/api/native';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { useUpdateEffect, useReactive } from 'ahooks';
import { useCallback, useEffect, useMemo } from 'react';
import { SupportUtils, nToHS } from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import I18n, { I18nKey } from '@ledvance/base/src/i18n';
import { RouterKey } from '../navigation/Router';
import {MoodInfo, MoodPageParams } from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface';
import {
  AdvancedData,
  AdvancedStatus,
  getAdvancedStatusColor,
} from '@ledvance/base/src/components/AdvanceCard';
import { BiorhythmPageParams } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmPage';
import { SleepWakeUpPageRouteParams } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpPage';
import { createParams } from '@ledvance/base/src/hooks/Hooks';
import {
  DeviceStateType,
  DeviceType,
  FanLightData,
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { dp2Obj, obj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse';
import { cloneDeep } from 'lodash';
import { useSleepMode, useWakeUp } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpActions'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import { useBiorhythm } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions';
import { timeFormat, useCountdowns } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'
import { ApplyForItem } from '@ledvance/base/src/utils/interface';

export const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  temp_value: { key: 'temp_value', code: '23' },
  scene_data: { key: 'scene_data', code: '25' },
  countdown: { key: 'countdown', code: '26' },
  control_data: { key: 'control_data', code: '28' },
  switch_fan: { key: 'switch_fan', code: '51' },
  fan_speed: { key: 'fan_speed', code: '53' },
  countdown_fan: { key: 'countdown_left_fan', code: '55' },
  rhythm_mode: { key: 'rhythm_mode', code: '101' },
  sleep_mode: { key: 'sleep_mode', code: '102' },
  wakeup_mode: { key: 'wakeup_mode', code: '103' },
};

const lightApplyFor: ApplyForItem[] = [
  {
    type: 'light',
    name: I18n.getLang('Onoff_button_socket'),
    key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
    dp: dpKC.switch_led.code,
    enable: true,
  },
];

const fanLightApplyFor: ApplyForItem[] = [
  {
    type: 'light',
    name: I18n.getLang('Onoff_button_socket'),
    key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
    dp: dpKC.switch_led.code,
    enable: true,
  },
  {
    type: 'fan',
    key: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
    dp: dpKC.switch_fan.code,
    enable: true
  }
];


export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_led'));
}

export function useSwitchFan(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_fan'));
}

export function useFanSpeed(): [number, (value: number) => Promise<Result<any>>] {
  return useDp<number, any>(getGlobalParamsDp('fan_speed'));
}

export enum WorkMode {
  Plan = 'Plan',
  Control = 'control',
  Scene = 'scene',
  Rhythm = 'rhythm',
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  return useDp<WorkMode, any>(getGlobalParamsDp('work_mode'));
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4);
  const s = str.substring(4, 6);
  const v = str.substring(6, 8);
  const brightness = str.substring(8, 10);
  const temperature = str.substring(10, 12);
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16),
    v: parseInt(v, 16),
    brightness: parseInt(brightness, 16),
    temperature: parseInt(temperature, 16),
  };
};

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80));
};

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(
    dpKC.bright_value.code
  );
  const setBrightFn = (v: number) => {
    return setBright(v * 10);
  };
  return [Math.round(bright / 10), setBrightFn];
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue]: [number, (v: number) => Promise<Result<any>>] = useDp(
    dpKC.temp_value.code
  );
  const setTempValueFn = (v: number) => {
    return setTempValue(v * 10);
  };
  return [Math.round(tempValue / 10), setTempValueFn];
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code);
}

export function useCountDownsFan(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown_fan.code);
}

export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.bright_value.key);
}

export function isSupportTemperature(): boolean {
  return SupportUtils.isSupportDp(dpKC.temp_value.key);
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key);
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp(dpKC.scene_data.key);
}

export function isSupportRhythm() {
  return SupportUtils.isSupportDp(dpKC.rhythm_mode.key);
}

export function isSupportSleepWakeUp() {
  return (
    SupportUtils.isSupportDp(dpKC.sleep_mode.key) || SupportUtils.isSupportDp(dpKC.wakeup_mode.key)
  );
}

export function useAdvancedData(isSuspend: boolean): AdvancedData[] {
  const res: AdvancedData[] = [];
  const deviceId = useDeviceId();
  const [workMode] = useWorkMode();
  const [switchLed] = useSwitch();
  const [switchFan] = useSwitchFan()
  const [rhythmMode] = useBiorhythm(getGlobalParamsDp('rhythm_mode'), true)
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [sleepList] = useSleepMode(dpKC.sleep_mode.code, true);
  const [wakeUpList] = useWakeUp(dpKC.wakeup_mode.code, true);
  const state = useReactive({
    rhythmModeStatus: AdvancedStatus.Disable,
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable,
  });
  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useEffect(() => {
    if (rhythmMode.enable) {
      state.rhythmModeStatus = isSuspend ? AdvancedStatus.Suspend : AdvancedStatus.Enable
    } else {
      state.rhythmModeStatus = AdvancedStatus.Disable
    }
  }, [isSuspend, rhythmMode.enable])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  useEffect(() => {
    const sleepWakeUp = [...sleepList, ...wakeUpList];
    state.sleepWakeUpStatus = sleepWakeUp.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [sleepList, wakeUpList]);

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.FanLight,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
          fanSpeed: 1
        },
      },
      isManual: !dps.hasOwnProperty(dpKC.scene_data.code),
      mood: undefined,
    };

    if (dps.hasOwnProperty(dpKC.bright_value.code)) {
      deviceState.deviceData.deviceData.brightness = Math.round(dps[dpKC.bright_value.code] / 10);
      deviceState.deviceData.deviceData.isColorMode = false;
    }
    if (dps.hasOwnProperty(dpKC.temp_value.code)) {
      deviceState.deviceData.deviceData.temperature = Math.round(dps[dpKC.temp_value.code] / 10);
      deviceState.deviceData.deviceData.isColorMode = false;
    }
    if (dps.hasOwnProperty(dpKC.scene_data.code)) {
      const mood = dp2Obj(dps[dpKC.scene_data.code], true);
      deviceState.mood = cloneDeep(mood);
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        if ((mood as MoodInfo).mainLamp && (mood as MoodInfo).mainLamp?.nodes?.length) {
          manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
          manualDps[getGlobalParamsDp('scene_data')] = obj2Dp((mood as MoodInfo), true)
          manualDps[getGlobalParamsDp('switch_led')] = true
          manualDps[getGlobalParamsDp('switch_fan')] = (mood as MoodInfo).mainLamp.fanEnable
          if ((mood as MoodInfo).mainLamp.fanEnable) {
            manualDps[getGlobalParamsDp('fan_speed')] = (mood as MoodInfo).mainLamp.fanSpeed
          }
        }
      } else {
        if (deviceData.type === DeviceType.FanLight) {
          const fanDeviceData = deviceData.deviceData as FanLightData
          applyForList.forEach(apply => {
            manualDps[apply.dp] = apply.enable;
          });
          if (manualDps[dpKC.switch_led.code]) {
            if (isSupportBrightness()) {
              manualDps[dpKC.bright_value.code] = fanDeviceData.brightness * 10;
            }
            if (isSupportTemperature()) {
              manualDps[dpKC.temp_value.code] = fanDeviceData.temperature * 10;
            }
            manualDps[dpKC.work_mode.code] = WorkMode.Control;
          }
          if (manualDps[dpKC.switch_fan.code]) {
            manualDps[dpKC.fan_speed.code] = fanDeviceData.fanSpeed
          }
        }
      }
      return manualDps;
    },
    []
  );

  if (isSupportMood()) {
    const params = createParams<MoodPageParams>({
      isSupportColor: false,
      isSupportSceneStatus: true,
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      switchLedDp: getGlobalParamsDp('switch_led'),
      mainDp: getGlobalParamsDp('scene_data'),
      mainWorkMode: getGlobalParamsDp('work_mode'),
      mainSwitch: getGlobalParamsDp('switch_led'),
      isFanLight: true,
    });
    res.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(
        workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: dpKC.scene_data,
      router: {
        key: RouterKey.ui_biz_mood,
        params,
      },
    });
  }

  if (isSupportRhythm()) {
    const params = createParams<BiorhythmPageParams>({
      biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        randomTimeDpCode: getGlobalParamsDp('random_timing'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
    })
    res.push({
      title: I18n.getLang('add_new_trigger_time_system_back_text'),
      statusColor: getAdvancedStatusColor(state.rhythmModeStatus),
      subtitles: state.rhythmModeStatus === AdvancedStatus.Suspend
        ? [I18n.getLang('light_sources_feature_4_text_name')]
        : [],
      dp: dpKC.rhythm_mode,
      router: {
        key: RouterKey.bi_biz_biological,
        params
      },
    });
  }

  res.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: fanLightApplyFor,
        isSupportMood: isSupportMood(),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: isSupportBrightness(),
        isSupportTemperature: isSupportTemperature(),
        isFanLight: true
      } as TimeSchedulePageParams,
    },
  });

  if (isSupportTimer()) {
    const params = createParams({
      dps: [
        {
          label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
          value: 'lighting',
          dpId: dpKC.countdown.code,
          enableDp: dpKC.switch_led.code,
          cloudKey: 'lightingInfo',
          stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
          stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
        },
        {
          label: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
          value: 'fan',
          dpId: dpKC.countdown_fan.code,
          enableDp: dpKC.switch_fan.code,
          cloudKey: 'fanInfo',
          stringOn: 'timer_ceiling_fan_switched_on_text',
          stringOff: 'timer_ceiling_fan_switched_off_text'
        },
      ],
    })
    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() =>{
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let enable = false
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown'):
            enable = switchLed
            break
          case getGlobalParamsDp('countdown_left_fan'):
            enable = switchFan
            break
        }
        const L = 'ceiling_fan_feature_2_light_text_min_'
        const F = 'ceiling_fan_feature_2_fan_text_min_'
        const isLight = timer.dpId === getGlobalParamsDp('countdown')
        const key: I18nKey = `${isLight ? L : F}${enable ? 'off': 'on'}`
        return I18n.formatValue(key, timeFormat(timer.countdown[0], true))
      })
    }, [switchLed, switchFan, JSON.stringify(tasks)])
    res.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  if (isSupportSleepWakeUp()) {
    const params = createParams<SleepWakeUpPageRouteParams>({
      wakeUpDpCode: dpKC.wakeup_mode.code,
      sleepDpCode: dpKC.sleep_mode.code,
      conflictDps: {
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
      },
      isSupportColor: false,
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      applyForList: lightApplyFor,
    });
    res.push({
      title: I18n.getLang('add_sleepschedule_one_source_system_back_text'),
      statusColor: getAdvancedStatusColor(state.sleepWakeUpStatus),
      dp: dpKC.sleep_mode,
      router: {
        key: RouterKey.ui_biz_sleep_wakeUp_new,
        params,
      },
    });
  }

  return res;
}
