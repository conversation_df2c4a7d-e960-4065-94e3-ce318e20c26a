import { spliceByStep } from "@ledvance/base/src/utils/common"
import { nToHS } from "@tuya/tuya-panel-lamp-sdk/lib/utils"

export interface MixLightBean {
  whiteLightSwitch: boolean,
  colorLightSwitch: boolean,
  mixRgbcwEnabled: boolean,
  h: number,    // 色相 0-360
  s: number,    // 饱和度 0-100
  v: number,    // 明度 0-100
  brightness: number,    // 亮度 0-100
  temperature: number,    // 色温 0-100
}

export function mixObj2Dp(obj: MixLightBean): string {
  const powerSwitchDpValueArray = ['0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0']
  if (obj.whiteLightSwitch) {
      powerSwitchDpValueArray[15] = '1'
  }
  if (obj.colorLightSwitch) {
      powerSwitchDpValueArray[14] = '1'
  }
  if (obj.mixRgbcwEnabled) {
      powerSwitchDpValueArray[13] = '1'
  }

  
  const powerSwitchDpValueBinary = powerSwitchDpValueArray.join('')
  const powerSwitchDpValue = parseInt(powerSwitchDpValueBinary, 2).toString(16).padStart(4, '0')
  const hueDpValue = nToHS(obj.h, 4)
  const satDpValue = nToHS(obj.s * 10, 4)
  const lightnessDpValue = nToHS(obj.v * 10, 4)
  const brightnessDpValue = nToHS(obj.brightness * 10, 4)
  const colorTempDpValue = nToHS(obj.temperature * 10, 4)
  return powerSwitchDpValue + hueDpValue + satDpValue + lightnessDpValue + brightnessDpValue + colorTempDpValue
}

export function mixDp2Obj(dp: string): MixLightBean {
  if (!dp) {
      return {
          whiteLightSwitch: true,
          colorLightSwitch: true,
          mixRgbcwEnabled: true,
          h: 360,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 100,
      }
  }
  const dpValueArray = spliceByStep(dp, 4)
  const powerSwitch = parseInt(dpValueArray[0], 16).toString(2).padStart(16, '0')
  return {
      whiteLightSwitch: powerSwitch[15] === '1',
      colorLightSwitch: powerSwitch[14] === '1',
      mixRgbcwEnabled: powerSwitch[13] === '1',
      h: Math.round(parseInt(dpValueArray[1], 16)),   // 色相 0-360
      s: Math.round(parseInt(dpValueArray[2], 16) / 10),    // 饱和度 0-100
      v: Math.round(parseInt(dpValueArray[3], 16) / 10),    // 明度 0-100
      brightness: Math.round(parseInt(dpValueArray[4], 16) / 10),    // 亮度 0-100
      temperature: Math.round(parseInt(dpValueArray[5], 16) / 10),    // 色温 0-100
  }
}