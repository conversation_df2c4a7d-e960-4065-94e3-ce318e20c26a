import React, {useCallback, useEffect} from 'react';
import { ScrollView, View } from 'react-native';
import {useReactive, useThrottleFn, useUpdateEffect} from 'ahooks';
import Page from '@ledvance/base/src/components/Page';
import {
  useDeviceInfo,
  useFamilyName,
  useFanMaxSpeed,
  useGestureControl,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import { NativeApi, sendAppEvent } from '@ledvance/base/src/api/native';
import {
  WorkMode,
  isSupportBrightness,
  isSupportTemperature,
  useAdvancedData,
  useBrightness,
  useCountDowns,
  useCountDownsFan,
  useFanSpeed,
  useSwitch,
  useSwitchFan,
  useTemperature,
  useWorkMode,
} from '../../hooks/FeatureHooks';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import Card from '@ledvance/base/src/components/Card';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import I18n from '@ledvance/base/src/i18n';
import { cctToColor } from '@ledvance/base/src/utils/cctUtils';
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView';
import Spacer from '@ledvance/base/src/components/Spacer';
import FanAdjustView from '@ledvance/base/src/components/FanAdjustView';
import { Utils } from 'tuya-panel-kit';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import { useRhythmSuspend } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions';
import dayjs from 'dayjs';
import { useIsFocused } from '@react-navigation/core'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks';

const cx = Utils.RatioUtils.convertX;

const HomePage = () => {
  const devInfo = useDeviceInfo();
  const familyName = useFamilyName();
  const [countDown, setCountDown] = useCountDowns();
  const [countDownFan, setCountDownFan] = useCountDownsFan();
  const [tempValue, setTempValue] = useTemperature();
  const [brightness, setBrightness] = useBrightness();
  const [switchLed, setSwitchLed] = useSwitch();
  const [workMode, setWorkMode] = useWorkMode();
  const [switchFan, setSwitchFan] = useSwitchFan();
  const [fanSpeed, setFanSpeed] = useFanSpeed();
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureBrightness] = useGestureControl('brightness')
  const [isSuspend, getSuspendTime, setSuspendTime] = useRhythmSuspend(getGlobalParamsDp('rhythm_mode'))
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator()
  const advanceData = useAdvancedData(isSuspend);
  const state = useReactive({
    tempValue,
    brightness,
    fanSpeed,
    controlFlag: Symbol(),
    loading: false,
    suspendFlag: Symbol()
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('temp_value'))
    if (!shouldBlock) {
      state.tempValue = tempValue;
    }
  }, [tempValue, onDpResponse])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('brightness_control'))
    if (!shouldBlock) {
      state.brightness = brightness;
    }
  }, [brightness, onDpResponse])

  useUpdateEffect(() => {
    state.fanSpeed = fanSpeed;
  }, [fanSpeed]);

  const { run: runCct } = useThrottleFn(async () => {
    await setTempValue(state.tempValue)
    state.suspendFlag = Symbol()
    if (workMode !== WorkMode.Control) {
      await setWorkMode(WorkMode.Control)
    }
  }, { wait: 500 })

  const { run: runBrightness } = useThrottleFn(async () => {
    if (switchLed) {
      await setBrightness(state.brightness)
      state.suspendFlag = Symbol()
      if (workMode !== WorkMode.Control) {
        await setWorkMode(WorkMode.Control)
      }
    }
  }, { wait: 500 })

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    state.brightness = gestureBrightness
    runBrightness().then()
  }, [isFocused, gestureBrightness])

  const closeCountDown = useCallback(() => {
    if (countDown) {
      setCountDown(0).then();
    }
    if (countDownFan) {
      setCountDownFan(0).then();
    }
  }, [countDown, countDownFan]);

  const getBlockColor = useCallback(() => {
    return cctToColor(state.tempValue);
  }, [state.tempValue]);

  useUpdateEffect(() =>{
    setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
  }, [state.suspendFlag])

  useUpdateEffect(() =>{
    getSuspendTime()
  }, [workMode, tempValue, brightness, switchLed, switchFan, fanSpeed])

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer height={cx(16)}/>
          <Card style={{marginHorizontal: cx(24)}}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
              color={getBlockColor()}
              colorAlpha={1}
              enable={switchLed}
              setEnable={async (v: boolean) => {
                await setSwitchLed(v);
                state.suspendFlag = Symbol()
                closeCountDown();
              }}
            />
            {switchLed && (
              <>
                <ColorTempAdjustView
                  colorTemp={state.tempValue}
                  brightness={state.brightness}
                  isSupportTemperature={isSupportTemperature()}
                  isSupportBrightness={isSupportBrightness()}
                  onCCTChange={async cct => {
                    state.tempValue = cct;
                    sendDpWithTimestamps(getGlobalParamsDp('temp_value'), () => runCct()).then()
                  }}
                  onCCTChangeComplete={async cct => {
                    state.tempValue = cct
                    sendDpWithTimestamps(getGlobalParamsDp('temp_value'), () => runCct()).then()
                  }}
                  onBrightnessChange={async bright => {
                    state.brightness = bright;
                    sendDpWithTimestamps(getGlobalParamsDp('brightness_control'), () => runBrightness()).then()
                  }}
                  onBrightnessChangeComplete={async bright => {
                    state.brightness = bright
                    sendDpWithTimestamps(getGlobalParamsDp('brightness_control'), () => runBrightness()).then()
                  }}
                />
                <Spacer />
              </>
            )}
          </Card>
          <Spacer />
          <FanAdjustView
            style={{marginHorizontal: cx(24)}}
            fanEnable={switchFan}
            fanSpeed={state.fanSpeed}
            maxFanSpeed={useFanMaxSpeed()}
            isSupportDirection={false}
            isSupportDisinfect={false}
            isSupportMode={false}
            onFanSwitch={async fanEnable => {
              await setSwitchFan(fanEnable);
              state.suspendFlag = Symbol()
              closeCountDown();
              if (workMode !== WorkMode.Control) {
                await setWorkMode(WorkMode.Control);
              }
            }}
            onFanSpeedChangeComplete={async fanSpeed => {
              await setFanSpeed(fanSpeed);
              state.suspendFlag = Symbol()
              if (workMode !== WorkMode.Control) {
                await setWorkMode(WorkMode.Control);
              }
            }}
          />
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  );
};

export default HomePage;
