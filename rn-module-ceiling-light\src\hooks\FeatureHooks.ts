import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDeviceId, useDp, useTimeSchedule, useDps, useFlagMode } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { NativeApi, getTuyaCloudData } from '@ledvance/base/src/api/native'
import { Result } from '@ledvance/base/src/models/modules/Result'
import { ColorUtils, SupportUtils, avgSplit, nToHS, parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import I18n from '@ledvance/base/src/i18n/index'
import { RouterKey } from '../navigation/Router'
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard'
import { MoodPageParams } from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface'
import { getHSVByHex, getHexByHSV } from '@ledvance/base/src/utils'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { cloneDeep, flatMap, range } from 'lodash'
import { useCreation, useReactive, useThrottleFn, useUpdateEffect } from 'ahooks'
import { ColorTool, Combination, WhiteTool, drawToolFormat, drawToolParse, getDefaultDrawTool, ControlData } from './ProtocolConvert'
import { Node } from '@ledvance/base/src/components/DrawToolView'
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import { MusicPageRouterParams } from '@ledvance/ui-biz-bundle/src/modules/music/MusicPage'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import { lampApi } from '@tuya/tuya-panel-api'
import { obj2Dp, stripObj2Dp, dp2Obj, stripDp2Obj } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import { Buffer } from 'buffer'
import { FlagPageProps } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage'
import { getFlagMode, saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import res from '@ledvance/base/src/res'
import { Formatter } from "@tuya/tuya-panel-lamp-sdk";
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { createParams } from '@ledvance/base/src/hooks/Hooks'
import { ApplyForItem } from '@ledvance/base/src/utils/interface'
import { DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'

const { ControlDataFormatter } = Formatter
const control = new ControlDataFormatter()

export const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  temp_value: { key: 'temp_value', code: '23' },
  colour_data: { key: 'colour_data', code: '24' },
  scene_data: { key: 'scene_data', code: '25' },
  countdown: { key: 'countdown', code: '26' },
  music_data: { key: 'music_data', code: '27' },
  control_data: { key: 'control_data', code: '28' },
  colour_switch: { key: 'colour_switch', code: '54' },
  rgbic_work_mode: { key: 'rgbic_work_mode', code: '55' },
  dreamlight_scene_mode: { key: 'dreamlight_scene_mode', code: '56' },
  paint_colour_data: { key: 'paint_colour_data', code: '61' },
  white_switch: { key: 'white_switch', code: '63' },
}

// ceiling light Auxiliary 灯珠数量
export const ceilingLightPixel = 12

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.switch_led.code)
}
// 彩光 all     [00,01,00,06,00,00,f6,03,20,03,e8]
// 彩光 single  [00,01,00,06,01,00,01,02,c1,03,e8,81,01]
// 彩光 single1 [00,01,00,06,01,00,01,02,c1,03,e8,86,01,02,03,04,05,06]
// 彩光 clear   [00,01,00,06,02,00,00,00,00,00,00,81,01]
// 白光 all     [00,00,00,06,00,03,e8,03,b4]
export enum WorkMode {
  White = 'white',
  Colour = 'colour',
  Scene = 'scene',
  Music = 'music'
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(dpKC.work_mode.code)
  const [workMode, setWorkMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWorkMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWorkMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

export function useRgbcWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(dpKC.rgbic_work_mode.code)
  const [workMode, setWorkMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWorkMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWorkMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

type HSV = {
  h: number
  s: number
  v: number
}

export function putControlData(): (isColorMode: boolean, value: ControlData) => Promise<any> {
  const [, setControlData] = useDp(dpKC.control_data.code)
  return async (isColorMode: boolean, value: ControlData) => {
    const v = control.format({
      mode: 1,
      hue: isColorMode ? value.h : 0,
      saturation: isColorMode ? value.s * 10 : 0,
      value: isColorMode ? value.brightness * 10 : 0,
      brightness: isColorMode ? 0 : value.brightness * 10,
      temperature: isColorMode ? 0 : value.cct * 10,
    })
    await setControlData(v)
  }
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4)
  const s = str.substring(4, 8)
  const v = str.substring(8, 12)
  const brightness = str.substring(12, 16)
  const temperature = str.substring(16, 20)
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16) / 10,
    v: parseInt(v, 16) / 10,
    brightness: parseInt(brightness, 16) / 10,
    temperature: parseInt(temperature, 16) / 10
  }
}

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80))
}

export const getHexByTemp = (temp: number, bright?: number) => {
  const rgb = cctToColor(Math.round(temp))
  return `#${getBrightOpacity(bright || 100)}${ColorUtils.rgb2hex(rgb[0], rgb[1], rgb[2]).slice(1)}`
}

export function useColorData(): [HSV, (h: number, s: number, v: number) => Promise<Result<any>>] {
  const [color, setColor] = useDp(dpKC.colour_data.code)
  const hsv = useMemo(() => {
    if (!color) return {
      h: 360,
      s: 100,
      v: 100
    }
    const hsvData = getHSVByHex(color)
    return {
      h: hsvData.h,
      s: Math.round(hsvData.s / 10),
      v: Math.round(hsvData.v / 10)
    }
  }, [color])

  const setColorFn = (h: number, s: number, v: number) => {
    return setColor(getHexByHSV({
      h,
      s: s * 10,
      v: v * 10
    }))
  }
  return [hsv, setColorFn]
}

export function usePaintDp() {
  return dpKC.paint_colour_data
}

type ColorType = {
  h: number
  s: number
  v: number
  b?: number
  t?: number
}
type PaintColorType = [any, (data: ColorTool | WhiteTool) => Promise<Result<any>>, Node[], (color: ColorType | string[], idxList: number[]) => void]
const whiteHex = '#FEAC5B'
const colorHex = '#FF0000'
export function usePaintColorData(): PaintColorType {
  const [dps, setPaintData]: [string, (hex: Record<string, any>) => Promise<Result<any>>] = useDps()
  const paintDp = usePaintDp()
  const paintData = dps[paintDp.code]
  const devId = useDeviceId()
  const [workMode, setWorkMode] = useRgbcWorkMode()
  const state = useReactive({
    cloudData: [] as any[],
    cloudHex: '',
    sendingDp: [] as string[],
  })

  const hsv = useCreation(() => {
    const paint = drawToolParse(paintData)
    const isWhite = isSupportBrightness() || isSupportTemperature()
    if (!isWhite && paint.adjustCode === 0) {
      return {
        ...getDefaultDrawTool(),
        s: 1
      }
    } else {
      return paint
    }
  }, [paintData])

  useEffect(() => {
    getTuyaData().then()
  }, [])

  useUpdateEffect(() => {
    if (state.sendingDp.includes(paintData)) {
      state.sendingDp = state.sendingDp.filter(dp => dp === paintData)
      return
    }
    getTuyaCloud.run()

  }, [paintData])

  const groupAndPadString = (str: string) =>{
    // 每组4个字符
    const groupSize = 20;
    // 分割字符串为4字符一组
    const groups: string[] = str.match(new RegExp(`.{1,${groupSize}}`, 'g')) || [];
    
    // 如果组数少于5，补齐
    while (groups.length < 12) {
      const lastGroup = groups[groups.length - 1] || '';
      groups.push(lastGroup);
    }
    
    return groups.join('');
  }

  const getTuyaData = async () => {
    const paint = drawToolParse(paintData)
    if (paint.daubType === 0) {
      if (paint.adjustCode === 1) {
        const colorPaint = paint as ColorTool
        const hex = ColorUtils.hsv2hex(colorPaint?.h, colorPaint?.s / 10, 100).slice(1)
        state.cloudData = createColorFn(`#${getBrightOpacity(colorPaint?.v / 10)}${hex}`, ceilingLightPixel)
        state.cloudHex = `${nToHS(colorPaint.h, 4)}${nToHS(colorPaint.s)}${nToHS(colorPaint.v)}00000000`.repeat(ceilingLightPixel)
      }
      if (paint.adjustCode === 3) {
        const combPaint = paint as Combination
        const colors = flatMap(range(ceilingLightPixel), (index) => ({
          color: combPaint.colors[index % combPaint.colors.length]
        }))
        state.cloudData = colors
        state.cloudHex = colors.map(c => {
          const v = ColorUtils.hex2hsv(c.color)
          return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
        }).join('')
      }
      return
    } else {
      const res = await getTuyaCloudData(devId)
      if (res && res['lights_0']) {
        const v = parseJSON(res['lights_0'])
        const isString = typeof v === 'string'
        const nodeHex = isString ? v : v?.data
        if (nodeHex) {
          const hex = groupAndPadString(nodeHex)
          state.cloudData = getColorFn(hex)
          state.cloudHex = hex
        } else {
          state.cloudData = createColorFn(colorHex, ceilingLightPixel)
          state.cloudHex = `${nToHS(360, 4)}${nToHS(1000, 4)}${nToHS(1000, 4)}00000000`.repeat(ceilingLightPixel)
        }
      } else {
        state.cloudData = createColorFn(colorHex, ceilingLightPixel)
        state.cloudHex = `${nToHS(360, 4)}${nToHS(1000, 4)}${nToHS(1000, 4)}00000000`.repeat(ceilingLightPixel)
      }
    }
  }

  const saveCloudData = async (data: string) => {
    const res = await lampApi.generalApi.saveCloudConfig('lights_0', data)
    return res
  }

  const { run } = useThrottleFn(saveCloudData, { wait: 300, leading: true })
  const getTuyaCloud = useThrottleFn(getTuyaData, { wait: 400, leading: true })

  const getColorFn = (hex: string, n: number = 20) => {
    const colorDataList = avgSplit(hex, n).map(hex => {
      return getColorData(hex)
    })
    return colorDataList.map(color => {
      if (color.v !== 0) {
        return {
          color: `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
        }
      } else {
        return {
          color: Object.values(color).every(item => item === 0) ? `#20${whiteHex.slice(1)}` : getHexByTemp(color.temperature, color.brightness)
        }
      }
    })
  }

  const createColorFn = useCallback((color: string, length: number) => {
    return Array.from({ length }, () => ({ color }))
  }, [])


  const setDrawToolFn = async (data: ColorTool | WhiteTool | Combination) => {
    await run(state.cloudHex)
    const hex = drawToolFormat({
      ...data,
      num: ceilingLightPixel
    })
    const { adjustCode } = data
    state.sendingDp = [...state.sendingDp, hex]
    const extraDp = {
      [paintDp.code]: hex
    }
    if (workMode === WorkMode.Music) {
      MusicManager.close()
    }

    if ((adjustCode !== 0 && workMode !== WorkMode.Colour) || (adjustCode === 0 && workMode !== WorkMode.White)) {
      await setWorkMode(adjustCode === 0 ? WorkMode.White : WorkMode.Colour)
      // extraDp[dpKC.rgbic_work_mode.code] = adjustCode === 0 ? WorkMode.White : WorkMode.Colour
    }

    return setPaintData(extraDp)
  }

  const setColorFn = useCallback(async (color: ColorType | string[], idxList: number[]) => {
    if (Array.isArray(color)) {
      const colors = flatMap(range(ceilingLightPixel), (index) => ({
        color: color[index % color.length]
      }))
      state.cloudData = colors
      state.cloudHex = colors.map(c => {
        const v = ColorUtils.hex2hsv(c.color)
        return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
      }).join('')
    } else {
      const newNodeList = cloneDeep(state.cloudData).map((item, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            item.color = `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
          } else {
            item.color = `#20${whiteHex.slice(1)}`
          }
        }
        return item
      })
      const newHex = avgSplit(state.cloudHex, 20).map((hex, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            return `${nToHS(color.h, 4)}${nToHS(color.s * 10, 4)}${nToHS(color.v * 10, 4)}00000000`
          } else {
            return '0'.repeat(20)
          }
        }
        return hex
      }).join('')
      state.cloudData = newNodeList
      state.cloudHex = newHex
    }
  }, [ceilingLightPixel, state.cloudData, state.cloudHex])
  return [hsv, setDrawToolFn, state.cloudData, setColorFn]
}

export function useColourSwitch(): [boolean, (v: boolean) => Promise<Result<any>>] {
  return useDp(dpKC.colour_switch.code)
}

export function useWhiteSwitch(): [boolean, (v: boolean) => Promise<Result<any>>] {
  return useDp(dpKC.white_switch.code)
}

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(dpKC.bright_value.code)
  const setBrightFn = (v: number) => {
    return setBright(v * 10)
  }
  return [Math.round(bright / 10), setBrightFn]
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [temp, setTemp]: [number, (v: number) => Promise<Result<any>>] = useDp(dpKC.temp_value.code)
  const setTempFn = (v: number) => {
    return setTemp(v * 10)
  }
  return [Math.round(temp / 10), setTempFn]
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code)
}

export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.bright_value.key)
}

export function isSupportTemperature(): boolean {
  return SupportUtils.isSupportDp(dpKC.temp_value.key)
}

export function isSupportColor(): boolean {
  return SupportUtils.isSupportDp(dpKC.colour_data.key)
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp(dpKC.dreamlight_scene_mode.key)
}

export function isSupportMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.music_data.key)
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key)
}


export function useAdvancedData(): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const deviceId = useDeviceId()
  const [workMode] = useWorkMode()
  const [rgbicWorkMode] = useRgbcWorkMode()
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const [flagMode, setFlagMode] = useFlagMode()
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })

      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          const flagData = parseJSON(res.data)
          setFlagMode(flagData)
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    if (rgbicWorkMode !== WorkMode.Colour && flagMode?.flagMode) {
      setFlagMode({
        flagId: undefined,
        flagMode: false,
      })
      saveFlagMode(deviceId, JSON.stringify({
        flagId: undefined,
        flagMode: false
      })).then()
    }
  }, [rgbicWorkMode])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  const mixApplyFor: ApplyForItem[] = [
    {
      type: 'mainLight',
      key: I18n.getLang('light_sources_tile_main_lighting_headline'),
      dp: getGlobalParamsDp('white_switch'),
      enable: true,
    },
    {
      type: 'secondaryLight',
      key: I18n.getLang('light_sources_tile_sec_lighting_headline'),
      dp: getGlobalParamsDp('colour_switch'),
      enable: true,
    },
  ]

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.CeilingLight,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: true,
          colorLightSwitch: true,
          whiteLightSwitch: true,
          mixRgbcwEnabled: true,
          activeKey: 1,
          colorDiskActiveKey: 0,
          colors: []
        }
      },
      isManual: !(dps.hasOwnProperty(getGlobalParamsDp('scene_data')) || dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))),
      mood: undefined
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('white_switch'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.whiteLightSwitch = dps[getGlobalParamsDp('white_switch')]
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('colour_switch'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.colorLightSwitch = dps[getGlobalParamsDp('colour_switch')]
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('bright_value'))) {
      deviceState.deviceData.deviceData.brightness = Math.round(dps[getGlobalParamsDp('bright_value')] / 10)
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('temp_value'))) {
      deviceState.deviceData.deviceData.temperature = Math.round(dps[getGlobalParamsDp('temp_value')] / 10)
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('paint_colour_data'))) {
      const paint_color = Buffer.from(dps[getGlobalParamsDp('paint_colour_data')], 'base64').toString('hex')
      const paintData: any = drawToolParse(paint_color)
      deviceState.deviceData.deviceData = {
        ...deviceState.deviceData.deviceData,
        ...paintData,
        h: paintData.hasOwnProperty('h') ? paintData.h : 0,
        s: paintData.hasOwnProperty('s') ? Math.trunc(paintData.s / 10) : 100,
        v: paintData.hasOwnProperty('v') ? Math.trunc(paintData.v / 10) : 100,
        colors: paintData?.colors || [],
        activeKey: paintData.adjustCode ?? 1,
        colorDiskActiveKey: paintData.colorDiskActiveKey ?? 0
      }
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('scene_data'))) {
      const sceneMood = dp2Obj(dps[getGlobalParamsDp('scene_data')])
      deviceState.mood = {
        ...sceneMood,
        mainLamp: {
          ...sceneMood.mainLamp,
          id: sceneMood.id
        }
      }
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))) {
      const rgbicMood = stripDp2Obj(Buffer.from(dps[getGlobalParamsDp('dreamlight_scene_mode')], 'base64').toString('hex'))
      // @ts-ignore
      deviceState.mood = {
        ...deviceState.mood,
        secondaryLamp: {
          ...rgbicMood.mainLamp,
          id: rgbicMood.id
        }
      }
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('switch_led')] = true
        if (mood.mainLamp && mood.mainLamp?.nodes?.length) {
          manualDps[getGlobalParamsDp('white_switch')] = true
          manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
          manualDps[getGlobalParamsDp('scene_data')] = obj2Dp({ ...mood, id: mood.mainLamp.id! })
        }
        if (mood.secondaryLamp && mood.secondaryLamp?.nodes?.length) {
          manualDps[getGlobalParamsDp('colour_switch')] = true
          manualDps[getGlobalParamsDp('rgbic_work_mode')] = WorkMode.Scene
          manualDps[getGlobalParamsDp('dreamlight_scene_mode')] = Buffer.from(stripObj2Dp({ ...mood, mainLamp: mood.secondaryLamp, id: mood.secondaryLamp.id! }), 'hex').toString('base64')
        }
      } else {
        const device = deviceData.deviceData as any
        const colorLightSwitch = applyForList[1].enable
        const whiteLightSwitch = applyForList[0].enable
        manualDps[getGlobalParamsDp('white_switch')] = whiteLightSwitch
        manualDps[getGlobalParamsDp('colour_switch')] = colorLightSwitch
        manualDps[getGlobalParamsDp('switch_led')] = true
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.White
        manualDps[getGlobalParamsDp('rgbic_work_mode')] = WorkMode.Colour
        if (whiteLightSwitch) {
          manualDps[getGlobalParamsDp('bright_value')] = device.brightness * 10
          manualDps[getGlobalParamsDp('temp_value')] = device.temperature * 10
        }
        if (colorLightSwitch) {
          const paint_color = drawToolFormat({
            ...device,
            num: ceilingLightPixel,
            adjustCode: device.activeKey ?? 1
          })
          manualDps[getGlobalParamsDp('paint_colour_data')] = Buffer.from(paint_color, 'hex').toString('base64')
        }
      }
      return manualDps;
    },
    []
  );

  if (isSupportColor()) {
    advanceData.push({
      title: I18n.getLang('Feature_devicepanel_flags'),
      statusColor: getAdvancedStatusColor(flagMode?.flagMode && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      icons: res.flag_icon,
      router: {
        key: RouterKey.ui_biz_flag_page,
        params: {
          isSupportColor: isSupportColor(),
          isSupportBrightness: isSupportBrightness(),
          isSupportTemperature: isSupportTemperature(),
          isCeilingLight: true,
          workModeCode: dpKC.work_mode.code,
          switchLedCode: dpKC.switch_led.code,
          whiteSwitchCode: dpKC.white_switch.code,
          rgbcSwitchLedCode: dpKC.colour_switch.code,
          rgbcWorkModeCode: dpKC.rgbic_work_mode.code,
          sceneDataCode: dpKC.scene_data.code,
          brightValueCode: dpKC.bright_value.code,
          temperatureCode: dpKC.temp_value.code,
          drawToolLight: {
            drawToolCode: dpKC.paint_colour_data.code,
            drawToolObj2dp: (colors) => {
              const def = {
                version: 0,
                num: ceilingLightPixel,
                daubType: 1,
                effect: 0,
                adjustCode: 1,
              }
              const distribute = distributeColorsEvenly(colors, ceilingLightPixel)
              const hexArray = colors.map((c, idx) => {
                return drawToolFormat({
                  ...def,
                  h: c.h,
                  s: c.s,
                  v: c.v,
                  selected: distribute[idx] as any
                })
              })
              return hexArray
            },
            drawToolDp2Obj: () => {
              return []
            }
          }
        } as FlagPageProps
      }
    })
  }

  if (isSupportMood()) {
    const params = createParams<MoodPageParams>({
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      switchLedDp: getGlobalParamsDp('switch_led'),
      mainDp: getGlobalParamsDp('scene_data'),
      mainWorkMode: getGlobalParamsDp('work_mode'),
      mainSwitch: getGlobalParamsDp('white_switch'),
      secondaryDp: getGlobalParamsDp('dreamlight_scene_mode'),
      secondaryWorkMode: getGlobalParamsDp('rgbic_work_mode'),
      secondarySwitch: getGlobalParamsDp('colour_switch'),
      isCeilingLight: true
    });

    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor((workMode === WorkMode.Scene || rgbicWorkMode === WorkMode.Scene) && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.dreamlight_scene_mode,
      router: {
        key: RouterKey.ui_biz_mood,
        params
      },
    })
  }

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        isSupportBrightness: isSupportBrightness(),
        isSupportColor: isSupportColor(),
        isSupportTemperature: isSupportTemperature(),
        isSupportMood: isSupportMood(),
        isCeilingLight: true,
        applyForDisabled: true,
        applyForList: cloneDeep(mixApplyFor),
        manualDataDp2Obj,
        manualDataObj2Dp,
      } as TimeSchedulePageParams,
    },
  })

  if (isSupportMusic()) {
    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(rgbicWorkMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.music_data,
      router: {
        key: RouterKey.ui_biz_music,
        params: {
          switch_led: dpKC.switch_led.code,
          work_mode: dpKC.rgbic_work_mode.code,
          music_data: dpKC.music_data.code,
          isMixRGBWLamp: false,
          isCeilingLight: true,
          colour_switch: dpKC.colour_switch.code
        } as MusicPageRouterParams,
      },
    })
  }

  if (isSupportTimer()) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: dpKC.countdown.code,
              enableDp: dpKC.switch_led.code,
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }

  return advanceData
}

function distributeColorsEvenly(colors: any[], num: number) {
  const colorLen = colors.length
  const colorDistribution = colors.map(() => Math.floor(num / colorLen))
  const overDistribution = num >= colorLen ? num % colorLen : 0
  const finalDistribution = colorDistribution.map((dist, idx) => {
    if (overDistribution !== 0) {
      // 判断余数是否为奇数
      const isEven = overDistribution % 2 === 0
      const midIdx = Math.floor(colorLen / 2)
      let startIdx = midIdx
      let endIdx = midIdx
      const m = Math.floor(overDistribution / 2)
      if (overDistribution > 1) {
        startIdx = midIdx - m
        endIdx = midIdx + m - (isEven ? 1 : 0)
      }
      if (idx >= startIdx && idx <= endIdx) {
        return dist + 1
      }
      return dist
    }
    if (idx <= num - 1 && dist === 0) {
      return 1
    }
    return dist
  }).filter(v => v !== 0)
  const getReduceIndex = (numList: number[], idx: number) => {
    return numList.reduce((pre, cur, index) => {
      if (idx > index) {
        return pre + cur
      }
      return pre
    }, 0)
  }

  const distributeIndex = finalDistribution.map((n, idx) => {
    const index = getReduceIndex(finalDistribution, idx)
    return range(index, index + n)
  })
  return distributeIndex
}
