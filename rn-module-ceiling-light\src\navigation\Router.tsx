import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import MoodPageRouters from '@ledvance/ui-biz-bundle/src/newModules/mood/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import MusicPageRouters from '@ledvance/ui-biz-bundle/src/modules/music/Router'
import FlagPageRouters from '@ledvance/ui-biz-bundle/src/modules/flags/Router'
import SelectPagePageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'

export const RouterKey = {
  main: 'main',
  ...ui_biz_routerKey,
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...TimeSchedulePageRouters,
  ...MoodPageRouters,
  ...TimerPageRouters,
  ...MusicPageRouters,
  ...FlagPageRouters,
  ...SelectPagePageRouters
]
