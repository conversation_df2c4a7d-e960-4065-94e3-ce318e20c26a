import React, {useCallback, useEffect} from 'react';
import { ScrollView, View } from 'react-native';
import Page from '@ledvance/base/src/components/Page';
import {
  useDeviceInfo,
  useFamilyName,
  useDeviceId,
  useFlagMode,
  useDps,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import { NativeApi } from '@ledvance/base/src/api/native';
import { Utils } from 'tuya-panel-kit';
import {
  WorkMode,
  isSupportBrightness,
  isSupportColor,
  isSupportTemperature,
  useAdvancedData,
  useBrightness,
  useColorData,
  usePaintColorData,
  useTemperature,
  useWorkMode,
  ceilingLightPixel,
  useWhiteSwitch,
  useColourSwitch,
  useSwitch,
  dpKC,
  putControlData,
} from '../../hooks/FeatureHooks';
import { useCreation, useReactive, useThrottleFn, useUpdateEffect } from 'ahooks';
import Spacer from '@ledvance/base/src/components/Spacer';
import DrawToolView from '@ledvance/base/src/components/DrawToolView';
import { range } from 'lodash';
import LampSwitchCard from 'components/LampSwitchCard';
import I18n from '@ledvance/base/src/i18n';
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils';
import { cctToColor } from '@ledvance/base/src/utils/cctUtils';
import { ColorList } from '@ledvance/ui-biz-bundle/src/modules/timeSchedule/components/ColorList';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView';
import Card from '@ledvance/base/src/components/Card';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import { saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions';
import ThemeType from '@ledvance/base/src/config/themeType'
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils
type AdjustType = 1 | 2 | 3;

const HomePage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo();
  const devId = useDeviceId();
  const familyName = useFamilyName();
  const [brightness, setBrightness] = useBrightness();
  const [temperature, setTemperature] = useTemperature();
  const [hsv] = useColorData();
  const [switchLed, setSwitchLed] = useSwitch();
  const [whiteSwitch] = useWhiteSwitch();
  const [colourSwitch] = useColourSwitch();
  const [, setDps] = useDps();
  const [painData, setPainData, ledColorList, setColorFn] = usePaintColorData();
  const [workMode, setWorkMode] = useWorkMode();
  const [flagMode, setFlagMode] = useFlagMode();
  const setControlData = putControlData();
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const advanceData = useAdvancedData();

  const tabList = useCreation(() => {
    // const isSupportWhite = isSupportBrightness() || isSupportTemperature()
    const tabs = [
      { key: 1, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_color_text') },
      // { key: 0, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_white_text') },
      {
        key: 3,
        title: I18n.getLang(
          'add_new_dynamic_mood_strip_lights_schedule_switch_tab_combination_text'
        ),
      },
    ];
    if (!isSupportColor()) {
      return tabs.filter(tab => tab.key === 0);
    }
    return tabs;
  }, []);

  const state = useReactive({
    ...hsv,
    brightness,
    temperature,
    loading: false,
    adjustType: 0,
    touchIdx: undefined as undefined | number,
    touchIdxList: '',
    activeKey: painData.adjustCode === 2 ? 1 : painData.adjustCode, // 0 white, 1 color 3 combination
    colorDiskActiveKey: painData.colorDiskActiveKey,
    colorDisk: [] as string[],
    flag: Symbol(),
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useUpdateEffect(() => {
    state.h = hsv.h;
    state.s = hsv.s;
    state.v = hsv.v;
  }, [hsv]);

  useUpdateEffect(() => {
    state.activeKey = painData.adjustCode === 2 ? 1 : painData.adjustCode;
    state.colorDiskActiveKey = painData.colorDiskActiveKey;
    state.touchIdxList = '';
    state.touchIdx = undefined;
  }, [painData]);

  useUpdateEffect(() => {
    state.brightness = brightness;
  }, [brightness]);

  useUpdateEffect(() => {
    state.temperature = temperature;
  }, [temperature]);

  useUpdateEffect(() => {
    if (workMode === WorkMode.Music || workMode === WorkMode.Scene) {
      state.adjustType = 0;
    }
  }, [workMode]);

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = state.activeKey === 1
    if (!isFocused || !switchLed || !colourSwitch || !isColorMode || state.adjustType !== 0 || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    setColorFn({ h: state.h, s: state.s, v: state.v }, range(ceilingLightPixel))
    runColour()
  }, [isFocused, switchLed, colourSwitch, state.activeKey, state.adjustType, gestureHue])

  useEffect(() => {
    if (!isFocused || !switchLed || gestureBrightness === undefined) {
      return
    }
    const isColorMode = state.activeKey === 1
    if (isColorMode && colourSwitch && state.adjustType === 0) {
      state.v = gestureBrightness
      setColorFn({ h: state.h, s: state.s, v: gestureBrightness }, range(ceilingLightPixel))
      runColour()
    }
    if (whiteSwitch) {
      state.brightness = gestureBrightness
      runBrightness().then()
    }
  }, [isFocused, switchLed, whiteSwitch, colourSwitch, state.activeKey, state.adjustType, gestureBrightness])

  const sendCommand = (idxList?: string) => {
    updateFlagMode();
    const subscript = idxList || state.touchIdxList;
    const params: any =
      state.adjustType !== 2
        ? state.activeKey === 3
          ? {
              colors: state.colorDisk,
            }
          : {
              h: state.h,
              s: state.s,
              v: state.v,
              selected: subscript ? JSON.parse(subscript) : [],
            }
        : {
            selected: subscript ? JSON.parse(subscript) : [],
          };
    setPainData({
      ...params,
      daubType: state.adjustType,
      adjustCode: state.activeKey,
    }).then();
  };

  useUpdateEffect(() => {
    sendCommand();
  }, [state.flag]);

  const putControlAction = () => {
    const isColorMode = false;
    const v = {
      h: state.h,
      s: state.s,
      brightness: state.brightness,
      cct: state.temperature,
    };
    setControlData(isColorMode, v).then();
  };

  const { run } = useThrottleFn(putControlAction, { wait: 500 });
  const { run: runBrightness } = useThrottleFn(async () => {
    if (switchLed && whiteSwitch) {
      updateFlagMode()
      setWorkMode(WorkMode.White)
      setBrightness(state.brightness)
    }
  }, { wait: 500 })
  const { run: runColour } = useThrottleFn(() => {
    if (switchLed && colourSwitch) {
      sendCommand()
    }
  }, { wait: 500 })

  const getBlockColor = useCallback(() => {
    if (state.activeKey === 1) {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100));
      return hsv2Hex(state.h, s, 100);
    }
    if (state.activeKey === 0) {
      return cctToColor(state.temperature);
    }
    return props.theme?.card.background;
  }, [state.activeKey, state.h, state.s, state.v, state.brightness, state.temperature]);

  const updateFlagMode = () => {
    if (flagMode?.flagMode) {
      saveFlagMode(
        devId,
        JSON.stringify({
          flagMode: false,
          flagId: undefined,
        })
      ).then();
      setFlagMode({
        flagId: undefined,
        flagMode: false,
      });
    }
  };

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          <Card style={{ marginHorizontal: cx(24) }}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_main_lighting_headline')}
              color={cctToColor(state.temperature, state.brightness)}
              colorAlpha={1}
              enable={whiteSwitch && switchLed}
              setEnable={async v => {
                state.loading = true;
                const beforeDps = {};
                const afterDps = {};
                if (v) {
                  if (!switchLed) {
                    afterDps[dpKC.switch_led.code] = true;
                    beforeDps[dpKC.colour_switch.code] = false;
                  }
                  beforeDps[dpKC.white_switch.code] = v;
                } else {
                  if (!colourSwitch) {
                    afterDps[dpKC.switch_led.code] = false;
                  } else {
                    beforeDps[dpKC.white_switch.code] = false;
                  }
                }
                setDps(beforeDps);
                setDps(afterDps);
                state.loading = false;
              }}
            />
            {whiteSwitch && switchLed && (
              <>
                <ColorTempAdjustView
                  isSupportBrightness={isSupportBrightness()}
                  isSupportTemperature={isSupportTemperature()}
                  brightness={state.brightness}
                  colorTemp={state.temperature}
                  onBrightnessChange={brightness => {
                    state.brightness = brightness;
                    run();
                  }}
                  onBrightnessChangeComplete={async brightness => {
                    state.brightness = brightness;
                    updateFlagMode();
                    setWorkMode(WorkMode.White)
                    setBrightness(brightness);
                  }}
                  onCCTChange={cct => {
                    state.temperature = cct;
                    run();
                  }}
                  onCCTChangeComplete={async cct => {
                    state.temperature = cct;
                    updateFlagMode();
                    setWorkMode(WorkMode.White)
                    setTemperature(cct);
                  }}
                />
                <Spacer height={cx(16)} />
              </>
            )}
          </Card>
          <Spacer />
          <DrawToolView
            title={I18n.getLang('light_sources_tile_sec_lighting_headline')}
            adjustType={(state.adjustType + 1) as AdjustType}
            stripStyle={'ONLY_LINE'}
            setAdjustType={t => {
              if (state.adjustType !== t - 1) {
                state.adjustType = t - 1;
                state.touchIdx = undefined;
              }
            }}
            hidLampAdjustView={true}
            hideColorize={state.activeKey !== 1}
            hideDisableLight={state.activeKey !== 1}
            nodes={ledColorList}
            fixCount={5}
            nodeTouch={idx => {
              if (idx !== state.touchIdx && state.adjustType !== 0) {
                state.touchIdx = idx;
                const color =
                  state.adjustType === 2
                    ? {}
                    : { h: state.h, s: state.s, v: state.v };

                // @ts-ignore
                setColorFn(color, [idx]);
              }
            }}
            fingerUp={idxList => {
              if (idxList !== state.touchIdxList && state.adjustType !== 0) {
                state.touchIdxList = idxList;
                sendCommand(idxList);
              }
            }}
            switchLed={colourSwitch && switchLed}
            showEnable={true}
            setEnable={async (v: boolean) => {
              state.loading = true;
              const beforeDps = {};
              const afterDps = {};
              if (v) {
                if (!switchLed) {
                  afterDps[dpKC.switch_led.code] = true;
                  beforeDps[dpKC.white_switch.code] = false;
                }
                beforeDps[dpKC.colour_switch.code] = v;
              } else {
                if (!whiteSwitch) {
                  afterDps[dpKC.switch_led.code] = false;
                } else {
                  beforeDps[dpKC.colour_switch.code] = false;
                }
              }
              setDps(beforeDps);
              setDps(afterDps);
              state.loading = false;
            }}
            isColorMode={state.activeKey === 1}
            setIsColorMode={() => {}}
            blockColor={getBlockColor()}
            h={state.h}
            s={state.s}
            v={state.v}
            onHSVChange={() => {}}
            onHSVChangeComplete={() => {}}
            temperature={state.temperature}
            brightness={state.brightness}
            onCCTChange={() => {}}
            onCCTChangeComplete={() => {}}
            onBrightnessChange={() => {}}
            onBrightnessChangeComplete={() => {}}
            hideLedNum={true}
            setLedNum={() => {}}
          >
            <LampSwitchCard
              lampTabs={tabList}
              onColorDiskChange={(color, idx) => {
                state.colorDisk = color;
                setColorFn(color, []);
                state.colorDiskActiveKey = idx;
                state.flag = Symbol();
              }}
              colorDiskActiveKey={state.colorDiskActiveKey}
              activeKey={state.activeKey}
              onActiveKeyChange={v => {
                state.adjustType = 0;
                state.activeKey = Number(v);
                state.colorDiskActiveKey = state.colorDiskActiveKey ?? 0;
                state.colorDisk = ColorList[state.colorDiskActiveKey];
                const color =
                  Number(v) === 3
                    ? state.colorDisk
                    : Number(v) === 0
                    ? { b: state.brightness, t: state.temperature }
                    : { h: state.h, s: state.s, v: state.v };
                const idx = Number(v) === 3 ? [] : range(ceilingLightPixel);
                setColorFn(color, idx);
                state.flag = Symbol();
              }}
              isSupportTemperature={false}
              isSupportBrightness={false}
              h={state.h}
              s={state.s}
              v={state.v}
              onHSVChange={() => {}}
              onHSVChangeComplete={async (h, s, v) => {
                state.h = h;
                state.s = s;
                state.v = v;
                state.touchIdx = undefined;
                if (state.adjustType === 0) {
                  setColorFn({ h: state.h, s: state.s, v: state.v }, range(ceilingLightPixel));
                  state.flag = Symbol();
                }
              }}
              colorTemp={state.temperature}
              brightness={state.brightness}
              onCCTChange={() => {}}
              onCCTChangeComplete={() => {}}
              onBrightnessChange={() => {}}
              onBrightnessChangeComplete={() => {}}
            />
          </DrawToolView>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  );
};

export default withTheme(HomePage)
