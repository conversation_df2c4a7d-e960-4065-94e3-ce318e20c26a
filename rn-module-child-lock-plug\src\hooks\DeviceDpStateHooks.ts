import { useDeviceId, useDp, useTimeSchedule } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { Result } from '@ledvance/base/src/models/modules/Result'
import I18n from '@ledvance/base/src/i18n'
import { RouterKey } from '../navigation/Router'
import { useReactive, useUpdateEffect } from 'ahooks'
import { NativeApi } from '@ledvance/base/src/api/native'
import { useCallback, useEffect, useMemo } from 'react'
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { useRandomTime } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions'
import { PowerBehaviorPageParams, usePowerBehavior } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions'
import { ApplyForItem } from '@ledvance/base/src/utils/interface'
import { DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage'
import { createParams } from '@ledvance/base/src/hooks/Hooks'
import { RandomTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage'
import { SupportUtils } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import { cloneDeep } from 'lodash'
import { FixedTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimePage'
import { useFixedTime } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimeActions'
import { timeFormatToRealTime } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'
import { ChildLockPageParams, useChildLock } from '@ledvance/ui-biz-bundle/src/newModules/childLock/ChildLockPage'
import { SwitchInchingPageParams, useSwitchInching } from '@ledvance/ui-biz-bundle/src/newModules/swithInching/SwithInchingAction'
import { SwitchHistoryPageRouteParams } from '@ledvance/ui-biz-bundle/src/modules/history/HistoryPage'

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_1'))
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('countdown_1'))
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp('countdown_1')
}

export function isSupportFixedTime(): boolean {
  return SupportUtils.isSupportDp('cycle_time')
}

export function isSupportRandomTime(): boolean {
  return SupportUtils.isSupportDp('random_time')
}

export function isSupportPowerBehavior(): boolean {
  return SupportUtils.isSupportDp('relay_status')
}

export function isSupportChildLock(): boolean {
  return SupportUtils.isSupportDp('child_lock')
}

export function isSupportSwitching(): boolean {
  return SupportUtils.isSupportDp('switch_inching')
}


export function useAdvancedData(): AdvancedData[] {
  const res: AdvancedData[] = []
  const deviceId = useDeviceId();
  const [powerBehaviors] = usePowerBehavior(['relay_status'])
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [randomTimeList] = useRandomTime(getGlobalParamsDp('random_time'), true, true);
  const [fixedTimeList] = useFixedTime(getGlobalParamsDp('cycle_time'), true, true);
  const [childLock] = useChildLock(getGlobalParamsDp('child_lock'))
  const [switchInching] = useSwitchInching(getGlobalParamsDp('switch_inching'))
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const state = useReactive({
    timeScheduleStatus: AdvancedStatus.Disable,
    randomTimeStatus: AdvancedStatus.Disable,
    fixedTimeStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  useEffect(() => {
    state.randomTimeStatus = randomTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(randomTimeList)]);

  useEffect(() => {
    state.fixedTimeStatus = fixedTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(fixedTimeList)]);



  const plugApplyFor: ApplyForItem[] = [
    {
      type: 'socket',
      key: I18n.getLang('manual_search_button_socket'),
      name: I18n.getLang('Onoff_button_socket'),
      dp: getGlobalParamsDp('switch_1'),
      enable: true,
    },
  ];

  const manualDataDp2Obj = useCallback(() => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
        },
      },
      isManual: true,
      mood: undefined,
    };
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (_, applyForList: ApplyForItem[]) => {
      const manualDps = {};
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
      })
      return manualDps;
    },
    []
  );

  res.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(plugApplyFor),
        applyForDisabled: true,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: false,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    }
  })

  if (isSupportTimer()) {
    const params = useMemo(() => createParams({
      dps: [
        {
          label: I18n.getLang('manual_search_button_socket'),
          cloudKey: 'socketInfo',
          dpId: getGlobalParamsDp('countdown_1'),
          enableDp: getGlobalParamsDp('switch_1'),
          stringOn: 'timer_nightplug_active_timer_field_description_on_text',
          stringOff: 'timer_nightplug_active_timer_field_description_off_text',
        },
      ],
    }), [])

    res.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'timer_nightplug_active_timer_field_description_off_text' : 'timer_nightplug_active_timer_field_description_on_text', timeFormatToRealTime(countdown))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'countdown_1', code: getGlobalParamsDp('countdown_1') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      }
    })
  }

  if (isSupportFixedTime()) {
    const params = createParams<FixedTimePageParams>({
      fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_time'),
        switchIngCode: getGlobalParamsDp('switch_inching')
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
    });

    res.push({
      title: I18n.getLang('fixedTimeCycle_socket_headline'),
      statusColor: getAdvancedStatusColor(state.fixedTimeStatus),
      dp: { key: 'cycle_time', code: getGlobalParamsDp('cycle_time') },
      router: {
        key: RouterKey.ui_biz_fixed_time_new,
        params
      }
    })
  }

  if (isSupportRandomTime()) {
    const params = createParams<RandomTimePageParams>({
      randomTimeDpCode: getGlobalParamsDp('random_time'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
        switchIngCode: getGlobalParamsDp('switch_inching')
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
    });

    res.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      statusColor: getAdvancedStatusColor(state.randomTimeStatus),
      dp: { key: 'random_time', code: getGlobalParamsDp('random_time') },
      router: {
        key: RouterKey.ui_biz_random_time_new,
        params
      }
    })
  }

  if (isSupportPowerBehavior()) {
    const params = createParams<PowerBehaviorPageParams>({
      powerBehaviorKeys: ['relay_status']
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_relay_status'),
      statusColor: getAdvancedStatusColor(powerBehaviors.some(item => !!item) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'relay_status', code: getGlobalParamsDp('relay_status') },
      router: {
        key: RouterKey.ui_biz_power_behavior_plug,
        params
      },
    })
  }

  if (isSupportChildLock()){
    const params = createParams<ChildLockPageParams>({
      childLockCode: getGlobalParamsDp('child_lock')
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_child_lock'),
      statusColor: getAdvancedStatusColor(childLock ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'child_lock', code: getGlobalParamsDp('child_lock') },
      router: {
        key: RouterKey.ui_biz_child_lock,
        params
      },
    })
  }

  if (isSupportSwitching()){
    const params = createParams<SwitchInchingPageParams>({
      switchIngCode: getGlobalParamsDp('switch_inching'),
      countdownCode: getGlobalParamsDp('countdown_1'),
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_time'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_time')
      }
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_switch_inching'),
      statusColor: getAdvancedStatusColor(switchInching.some(item => item.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'switch_inching', code: getGlobalParamsDp('switch_inching') },
      router: {
        key: RouterKey.ui_biz_switch_inching,
        params
      },
    })
  }

  const historyParams = createParams<SwitchHistoryPageRouteParams>({
    dpIds: [getGlobalParamsDp('switch_1')],
    getActionsText: (dpData:any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
  })

  res.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return res
}
