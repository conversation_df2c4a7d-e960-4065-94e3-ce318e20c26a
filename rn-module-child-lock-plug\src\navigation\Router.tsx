import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import RandomTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/randomTime/Router'
import FixedTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import { PlugPowerOnBehaviorPageRouters } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/Router'
import ChildLockPageRouter from '@ledvance/ui-biz-bundle/src/newModules/childLock/Router'
import SwitchInchingPageRouters from '@ledvance/ui-biz-bundle/src/newModules/swithInching/Router'
export const RouterKey = {
    main: 'main',
    ...ui_biz_routerKey,
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: undefined,
        },
    },
    ...TimerPageRouters,
    ...TimeSchedulePageRouters,
    ...RandomTimePageRouters,
    ...FixedTimePageRouters,
    ...PlugPowerOnBehaviorPageRouters,
    ...HistoryPageRouters,
    ...ChildLockPageRouter,
    ...SwitchInchingPageRouters
]