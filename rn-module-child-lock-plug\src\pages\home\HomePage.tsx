import React from 'react'
import { ScrollView, StyleSheet, Text, View } from 'react-native'
import res from '@ledvance/base/src/res'
import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import Page from '@ledvance/base/src/components/Page'
import { NativeApi } from '@ledvance/base/src/api/native'
import Card from '@ledvance/base/src/components/Card'
import { SwitchButton, Utils } from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import {
  useSwitch,
  useAdvancedData,
} from '../../hooks/DeviceDpStateHooks'
import { useReactive } from 'ahooks'
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const HomePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const [switch1, setSwitch1] = useSwitch()
  const advanceData = useAdvancedData()
  const state = useReactive({
    loading: false,
  })

  const styles = StyleSheet.create({
    content: {
      flex: 1,
    },
    switchCard: {
      paddingVertical: cx(16),
      marginHorizontal: cx(24),
    },
    switchCardContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    switchCardTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
  })

  return (
    <Page
      backText={familyName}
      headlineText={deviceInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(deviceInfo.devId)
      }}
      loading={state.loading}>
      <ScrollView nestedScrollEnabled={true}>
        <View style={styles.content}>
          <Spacer />
          <Card style={styles.switchCard} containerStyle={styles.switchCardContent}>
            <Spacer width={cx(16)} />
            <Text style={styles.switchCardTitle}>{I18n.getLang('Onoff_button_socket')}</Text>
            <Spacer width={0} height={0} style={{ flex: 1 }} />
            <SwitchButton
              value={switch1}
              onValueChange={setSwitch1} />
            <Spacer width={cx(16)} />
          </Card>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)

