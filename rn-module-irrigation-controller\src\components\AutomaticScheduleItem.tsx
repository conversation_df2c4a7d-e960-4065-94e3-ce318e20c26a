import {StyleSheet, Text, View, ViewStyle} from "react-native"
import React from "react"
import Card from "@ledvance/base/src/components/Card"
import ThemeType from '@ledvance/base/src/config/themeType'
import {SwitchButton, Utils} from "tuya-panel-kit"
import {formatHourAndMinute, loopText} from "@ledvance/base/src/utils/common"
import {IrrigationMode, IrrigationPlan, IrrigationType} from "../pages/automaticSchedule/AutomaticScheduleAction";
import I18n from "@ledvance/base/src/i18n";

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

interface ScheduleItemProps {
  theme?: ThemeType
  name: string,
  item: IrrigationPlan
  style?: ViewStyle
  is24Hour?: boolean
  onEnableChange: (enable: boolean) => void
  onPress: (item: any) => void
}

const AutomaticScheduleItem = (props: ScheduleItemProps) => {
  const { name, item, style, is24Hour, onEnableChange, onPress } = props


  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      borderRadius: cx(8),
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    infoContainer: {
      flex: 1,
      marginTop: cx(16),
      marginBottom: cx(16),
      flexDirection: 'column',
      marginLeft: cx(16),
    },
    time: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
    },
    loop: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginTop: cx(8),
    },
    name: {
      color: props.theme?.global.fontColor,
      marginBottom: cx(5),
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    switchContainer: {
      marginRight: cx(16),
      marginTop: cx(16),
    },
  });
  return (
    <Card
      style={styles.card}
      containerStyle={[styles.container, style]}
      onPress={() => {
        onPress(item);
      }}
    >
      <View style={styles.infoContainer}>
        <Text style={styles.name}>{name}</Text>
        <Text style={styles.time}>{item.type === IrrigationType.Irrigation ? I18n.getLang('irrigation') : I18n.getLang('spray')}</Text>
        <Text style={styles.time}>{I18n.getLang('thermostat_starttime')}: {formatHourAndMinute(item.startHour, item.startMinute, is24Hour)}</Text>
        {
          item.mode === IrrigationMode.Time ? <Text style={styles.time}>{I18n.getLang('irrigation_duration')}: {formatHourAndMinute(item.durationHour, item.durationMinute)}</Text>
            : <Text style={styles.time}>{I18n.getLang('irrigation_quantity')}: {item.waterVolume}L</Text>
        }
        <Text style={styles.loop}>
          {loopText(item.weeks, formatHourAndMinute(item.startHour, item.startMinute))}
        </Text>
      </View>
      <View style={styles.switchContainer}>
        <SwitchButton
          value={item.enable}
          thumbStyle={{ elevation: 0 }}
          onValueChange={() => {
            onEnableChange(!item.enable);
          }}
        />
      </View>
    </Card>
  )
}

export default withTheme(AutomaticScheduleItem) as React.ComponentType<Omit<ScheduleItemProps, 'theme'>>
