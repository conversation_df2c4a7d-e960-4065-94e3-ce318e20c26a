import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from '@ledvance/base/src/components/AdvanceCard'
import {useDp} from "@ledvance/base/src/models/modules/NativePropsSlice"
import {getGlobalParamsDp} from '@ledvance/base/src/utils/common'
import dayjs from "dayjs"
import CustomParseFormat from 'dayjs/plugin/customParseFormat'
import {RouterKey} from 'navigation/Router'
import {useTimerList} from "../pages/automaticSchedule/AutomaticScheduleAction";
import {useMemo} from "react";
import I18n from '@ledvance/base/src/i18n'

dayjs.extend(CustomParseFormat)

export const DpKeys = {
  temp: 'temp',
  humidity: 'humidity',
  flow: 'flow',
  battery: 'battery_capacity',
  batteryPercentage: 'battery_percentage',
  workStatus: 'work_status',
  manualTimer: 'manual_timer',
  manualSwitch: 'manual_switch',
  timerDelay: 'timer_delay',
  tempFormat: 'temp_format',
  timeFormat: 'time_format',
  nextTime: 'next_time',
  timer1: 'timer1',
  timer2: 'timer2',
  timer3: 'timer3',
  humiditySensor: 'add_humidity_sensor',
  humiditySensorStatus: 'moisure_sensor_status',
  flowCount: 'flow_count',
  tempCount: 'temp_count',
  humidityCount: 'humidity_count',
  janWater: 'jan_water',
  febWater: 'feb_water',
  marWater: 'mar_water',
  aprWater: 'apr_water',
  mayWater: 'may_water',
  junWater: 'jun_water',
  julWater: 'jul_water',
  augWater: 'aug_water',
  sepWater: 'sep_water',
  octWater: 'oct_water',
  novWater: 'nov_water',
  decWater: 'dec_water'
}

export const enum BatteryStatus {
  Empty = '0',
  Low = '1',
  Normal = '2'
}

export const enum WorkStatus {
  Idle = '0',
  Manual = '1',
  Automatic = '2',
  RainDelay = '3'
}

export const enum TimerDelay {
  Zero = '0',
  OnDay = '24',
  TwoDays = '48',
  ThreeDays = '72'
}

export function useTemp() {
  const [dpValue] = useDp<number, any>(getGlobalParamsDp(DpKeys.temp))
  return dpValue
}

export function useHumidity() {
  const [dpValue] = useDp<number, any>(getGlobalParamsDp(DpKeys.humidity))
  return dpValue
}

export function useFlow() {
  const [dpValue] = useDp<number, any>(getGlobalParamsDp(DpKeys.flow))
  return dpValue / 10
}

export function useWorkStatus() {
  return useDp<WorkStatus, any>(getGlobalParamsDp(DpKeys.workStatus))
}

export function useBatteryStatus() {
  const [dpValue] = useDp<BatteryStatus, any>(getGlobalParamsDp(DpKeys.battery))
  return dpValue
}

export function useBatteryPercentage() {
  const [dpValue] = useDp<number, any>(getGlobalParamsDp(DpKeys.batteryPercentage))
  return dpValue || 0
}

export function useTimerDelay() {
  return useDp<TimerDelay, any>(getGlobalParamsDp(DpKeys.timerDelay));
}

export function useTimeFormat() {
  return useDp<'12' | '24', any>(getGlobalParamsDp(DpKeys.timeFormat));
}

export function useNextTime() {
  const [nextTime] = useDp<string, any>(getGlobalParamsDp(DpKeys.nextTime));
  return [nextTime]
}

export function useManualTimer() {
  return useDp<number, any>(getGlobalParamsDp(DpKeys.manualTimer))
}

export function useManualSwitch() {
  return useDp<boolean, any>(getGlobalParamsDp(DpKeys.manualSwitch));
}

export function useHumiditySensor() {
  return useDp<boolean, any>(getGlobalParamsDp(DpKeys.humiditySensor))
}

export function useHumiditySensorStatus() {
  return useDp<boolean, any>(getGlobalParamsDp(DpKeys.humiditySensorStatus))[0]
}

export function parseNextTime(nextTime: string, timeFormat: string): string {
  if (!nextTime || nextTime === '0000000000') {
    return '-'
  }
  const year = parseInt(nextTime.substring(0, 2), 16).toString().padStart(2, '0')
  const month = parseInt(nextTime.substring(2, 4), 16).toString().padStart(2, '0')
  const day = parseInt(nextTime.substring(4, 6), 16).toString().padStart(2, '0')
  const hour = parseInt(nextTime.substring(6, 8), 16).toString().padStart(2, '0')
  const minute = parseInt(nextTime.substring(8, 10), 16).toString().padStart(2, '0')
  const dateStr = `${year}/${month}/${day} ${hour}:${minute}`
  const format = `DD.MM.YYYY ${timeFormat === '12' ? 'hh:mmA' : 'HH:mm'}`
  return dayjs(dateStr, 'YY/MM/DD HH:mm').format(format)
}

export function useAdvanceData(): AdvancedData[] {
  const [timer1, timer2, timer3] = useTimerList()
  const [manualSwitch] = useManualSwitch()
  const [humiditySensor] = useHumiditySensor()
  const advanceData = [] as AdvancedData[]

  const timerEnableStatus = useMemo(() => {
    return (timer1.enable || timer2.enable || timer3.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timer1.enable, timer2.enable, timer3.enable])

  advanceData.push({
    title: I18n.getLang('manual_mode'),
    statusColor: getAdvancedStatusColor(manualSwitch ? AdvancedStatus.Disable : AdvancedStatus.Enable),
    dp: { key: '', code: '' },
    router: {
      key: RouterKey.manual_mode,
      params: {},
    }
  })

  advanceData.push({
    title: I18n.getLang('thermostat_automode'),
    statusColor: getAdvancedStatusColor(timerEnableStatus),
    dp: { key: '', code: '' },
    router: {
      key: RouterKey.automatic_schedule,
      params: {},
    }
  })

  advanceData.push({
    title: I18n.getLang('sensor'),
    statusColor: getAdvancedStatusColor(humiditySensor ? AdvancedStatus.Enable : AdvancedStatus.Disable),
    dp: { key: '', code: '' },
    router: {
      key: RouterKey.sensor,
      params: {},
    }
  })

  advanceData.push({
    title: I18n.getLang('contact_sensor_specific_settings'),
    dp: { key: '', code: '' },
    router: {
      key: RouterKey.settings,
      params: {},
    }
  })

  return advanceData;
}
