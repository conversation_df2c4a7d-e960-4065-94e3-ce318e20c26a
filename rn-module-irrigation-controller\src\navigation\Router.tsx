import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import AutomaticSchedulePage from "../pages/automaticSchedule/AutomaticSchedulePage"
import AutomaticScheduleDetailPage from "../pages/automaticSchedule/AutomaticScheduleDetailPage";
import ManualModePage from "../pages/manualMode/ManualModePage";
import SensorPage from "../pages/sensor/SensorPage";
import WaterConsumptionPage from "../pages/waterConsumption/WaterConsumptionPage";
import SensorChart from "../pages/sensor/SensorChart";
import WaterChart from "../pages/waterConsumption/WaterChart";
import SettingsPage from "../pages/settings/SettingsPage";

export const RouterKey = {
    main: 'main',
    automatic_schedule: 'automatic_schedule',
    automatic_schedule_detail: 'automatic_schedule_detail',
    manual_mode: 'manual_mode',
    sensor: 'sensor',
    water_consumption: 'water_consumption',
    water_chart: 'water_chart',
    sensor_chart: 'sensor_chart',
    settings: 'settings',
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.automatic_schedule,
        component: AutomaticSchedulePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.automatic_schedule_detail,
        component: AutomaticScheduleDetailPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.manual_mode,
        component: ManualModePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.sensor,
        component: SensorPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.water_consumption,
        component: WaterConsumptionPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.water_chart,
        component: WaterChart,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.sensor_chart,
        component: SensorChart,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.settings,
        component: SettingsPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
]
