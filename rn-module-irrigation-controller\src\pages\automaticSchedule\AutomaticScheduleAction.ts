import {useDp} from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Result } from "@ledvance/base/src/models/modules/Result";
import {getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import { DpKeys } from "features/FeatureHooks";
import {useCallback} from "react";

export enum IrrigationType {
  Irrigation,
  Spray
}

export enum IrrigationMode {
  Quantity,
  Time
}

export interface IrrigationPlan {
  index: number
  id: number
  enable: boolean
  startHour: number
  startMinute: number
  durationHour: number
  durationMinute: number
  weeks: number[]
  drippingTime: number
  intervalMinute: number
  intervalSecond: number
  waterVolume: number
  type: IrrigationType
  mode: IrrigationMode
}

export function useTimeFormat() {
  return useDp<'12' | '24', any>(getGlobalParamsDp(DpKeys.timeFormat));
}

export function useTimerList(): [IrrigationPlan, IrrigationPlan, IrrigationPlan, (plan: IrrigationPlan) => Promise<Result<any>>] {
  const [timer1Dp, setTimer1] = useDp<string, any>(getGlobalParamsDp(DpKeys.timer1))
  const [timer2Dp, setTimer2] = useDp<string, any>(getGlobalParamsDp(DpKeys.timer2))
  const [timer3Dp, setTimer3] = useDp<string, any>(getGlobalParamsDp(DpKeys.timer3))
  const setFuncList = [setTimer1, setTimer2, setTimer3]
  const config1 = decodeIrrigation(timer1Dp, 0)
  const config2 = decodeIrrigation(timer2Dp, 1)
  const config3 = decodeIrrigation(timer3Dp, 2)
  const setConfig = useCallback((plan: IrrigationPlan) => {
    const value = encodeIrrigation(plan)
    return setFuncList[plan.index](value)
  }, [])
  return [config1, config2, config3, setConfig]
}

export function decodeIrrigation(hexStr: string, index: number): IrrigationPlan {
  if (!hexStr) {
    return getDefaultIrrigation(index)
  }
  const buffer = new Uint8Array(11);
  for (let i = 0; i < 11; i++) {
    buffer[i] = parseInt(hexStr.substring(i * 2, i * 2 + 2), 16)
  }
  const id = buffer[0]
  const startHour = buffer[1]
  const startMinute = buffer[2]
  const durationHour = buffer[3]
  const durationMinute = buffer[4]
  const enable = (buffer[5] & 0x01) === 1
  const week = buffer[5]
  const weeks: number[] = [
    (week & 0b00000001) >> 0,
    (week & 0b00000010) >> 1,
    (week & 0b00000100) >> 2,
    (week & 0b00001000) >> 3,
    (week & 0b00010000) >> 4,
    (week & 0b00100000) >> 5,
    (week & 0b01000000) >> 6,
    (week & 0b10000000) >> 7,
  ].reverse()
  const drippingTime = buffer[6]
  const type = drippingTime === 0 ? IrrigationType.Irrigation : IrrigationType.Spray
  const interval = (buffer[7] << 8) | buffer[8]
  const intervalMinute = Math.floor(interval / 60)
  const intervalSecond = interval % 60
  const waterVolume = ((buffer[9] << 8) | buffer[10]) / 10
  const mode = waterVolume === 0 ? IrrigationMode.Time : IrrigationMode.Quantity
  return {
    index,
    id,
    enable,
    startHour,
    startMinute,
    durationHour,
    durationMinute,
    weeks,
    drippingTime,
    intervalMinute,
    intervalSecond,
    waterVolume,
    type,
    mode
  }
}


export function encodeIrrigation(plan: IrrigationPlan): string {
  const buffer = new Uint8Array(11);

  buffer[0] = plan.id
  buffer[1] = plan.startHour
  buffer[2] = plan.startMinute
  buffer[3] = plan.durationHour
  buffer[4] = plan.durationMinute

  let weekByte: number = plan.enable ? 0x01 : 0x00
  const reversedWeeks = [...plan.weeks].reverse()
  reversedWeeks.forEach((enabled: number, index: number) => {
    if (enabled === 1 && index > 0) {
      weekByte |= (1 << index)
    }
  })
  buffer[5] = weekByte
  buffer[6] = plan.drippingTime
  const interval = plan.intervalMinute * 60 + plan.intervalSecond
  buffer[7] = (interval >> 8) & 0xFF
  buffer[8] = interval & 0xFF

  const waterVolume = plan.waterVolume * 10
  buffer[9] = (waterVolume >> 8) & 0xFF
  buffer[10] = waterVolume & 0xFF

  return Array.from(buffer)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('')
}

function getDefaultIrrigation(index: number): IrrigationPlan {
  return {
    index,
    id: 0,
    enable: false,
    startHour: 0,
    startMinute: 0,
    durationHour: 0,
    durationMinute: 0,
    weeks: [0, 0, 0, 0, 0, 0, 0, 0],
    drippingTime: 0,
    intervalMinute: 0,
    intervalSecond: 0,
    waterVolume: 0,
    type: IrrigationType.Irrigation,
    mode: IrrigationMode.Quantity
  }
}
