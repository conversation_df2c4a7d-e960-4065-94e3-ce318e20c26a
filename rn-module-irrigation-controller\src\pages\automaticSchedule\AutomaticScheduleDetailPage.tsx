import React, {useMemo} from "react";
import Page from "@ledvance/base/src/components/Page";
import {Dialog, Popup, Utils} from "tuya-panel-kit";
import {useReactive} from "ahooks";
import I18n from "@ledvance/base/src/i18n/index";
import res from "@ledvance/base/src/res/index";
import {ScrollView, StyleSheet, View} from "react-native";
import LdvWeekView from "@ledvance/base/src/components/weekSelect";
import Spacer from "@ledvance/base/src/components/Spacer";
import ThemeType from '@ledvance/base/src/config/themeType'
import {useNavigation, useRoute} from '@react-navigation/native'
import {IrrigationMode, IrrigationPlan, IrrigationType, useTimeFormat} from "./AutomaticScheduleAction";
import {cloneDeep, isEqual} from "lodash";
import Segmented from "@ledvance/base/src/components/Segmented";
import Cell from "@ledvance/base/src/components/Cell";
import {formatHourAndMinute, isPrecisionValid} from "@ledvance/base/src/utils/common";
import dayjs from "dayjs";

const {convertX: cx} = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface ScheduleDetailParam {
  name: string
  item: IrrigationPlan
  checkItemValid: (item: IrrigationPlan) => boolean
  onScheduleChange: (item: IrrigationPlan) => Promise<void>
}
const dripTimeDataSource = Array.from({length: 60}, (_, index) => ({
  label: `${index}`,
  value: `${index}`
})).filter(it => Number(it.value) >= 5)

const firstDataSource = Array.from({length: 24}, (_, index) => ({
  label: `${index}`.padStart(2, "0"),
  value: `${index}`
}))

const secondDataSource = Array.from({length: 60}, (_, index) => ({
  label: `${index}`.padStart(2, "0"),
  value: `${index}`
}))

const AutomaticScheduleDetailPage = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation()
  const params = useRoute().params as ScheduleDetailParam
  const [timeFormat] = useTimeFormat()
  const state = useReactive({
    loading: false,
    item: cloneDeep({...params.item, drippingTime: params.item.drippingTime || 5, intervalSecond: params.item.intervalSecond || 5}),
    is24Hour: timeFormat === '24'
  })

  const irrigationTypeOptions = [{
    label: I18n.getLang('irrigation'),
    value: IrrigationType.Irrigation
  }, {
    label: I18n.getLang('spray'),
    value: IrrigationType.Spray
  }]

  const irrigationModeOptions = [{
    label: state.item.type === IrrigationType.Irrigation ? I18n.getLang('irrigation_by_quantity') : I18n.getLang('spray_by_quantity'),
    value: IrrigationMode.Quantity
  }, {
    label: state.item.type === IrrigationType.Irrigation ? I18n.getLang('irrigation_on_time') : I18n.getLang('spray_on_time'),
    value: IrrigationMode.Time
  }]

  const showConfirm = useMemo(() => {
    return !isEqual(state.item, params.item)
  }, [JSON.stringify(state.item), JSON.stringify(params.item)])


  const canSave = useMemo(() => {
    return showConfirm && params.checkItemValid(state.item)
  }, [JSON.stringify(state.item), showConfirm])

  const onSave = async () => {
    if (!canSave) {
      return
    }
    const item = cloneDeep(state.item)
    item.enable = true
    if (item.type === IrrigationType.Irrigation) {
      item.drippingTime = 0
      item.intervalMinute = 0
      item.intervalSecond = 0
      if (item.mode === IrrigationMode.Quantity) {
        item.durationHour = 0
        item.durationMinute = 0
      } else {
        item.waterVolume = 0
      }
    } else {
      if (item.mode === IrrigationMode.Quantity) {
        item.durationHour = 0
        item.durationMinute = 0
      } else {
        item.waterVolume = 0
      }
    }
    state.loading = true
    await params.onScheduleChange(item)
    state.loading = false
  }

  const selectTime = (title: string, hour: number, minute: number, maxHour: number = 23, setter: (first: number, second: number) => void, use12Hours: boolean = false) => {
    Popup.datePicker({
      title: title,
      mode: 'time',
      defaultDate: dayjs().set('hour', hour).set('minute', minute).set('second', 0).toDate(),
      use12Hours: use12Hours,
      maxDate: dayjs().set('hour', maxHour).set('minute', 59).set('second', 0).toDate(),
      cancelText: I18n.getLang('auto_scan_system_cancel'),
      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
      onConfirm: (date, { close }) => {
        const dateInfo = dayjs(date)
        setter(dateInfo.hour(), dateInfo.minute())
        close()
      }
    })
  }

  const inputValue = (title: string, value: number, setter: (value: number) => void, min: number = 0, max: number = 1000, precision: number = 1) => {
    Dialog.prompt({
      title: title,
      placeholder: title,
      value: value.toString(),
      cancelText: I18n.getLang('auto_scan_system_cancel'),
      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
      inputWrapperStyle: {backgroundColor: props.theme?.textInput.background, borderRadius: cx(10)},
      autoFocus: true,
      keyboardType: 'numeric',
      onChangeText: text => {
        if (!Number.isNaN(Number(text)) && isPrecisionValid(text, precision)) {
          if (Number(text) < min) {
            return `${min}`
          } else if (Number(text) > max) {
            return `${max}`
          }
          return text
        }
      },
      onConfirm: (data, { close }) => {
        setter(Number(data))
        close()
      }
    })
  }

  const selectValues = ({title, first, second, maxFirst = 23, minSecond = 0, label = ['h', 'min']}: {
    title: string,
    first: number,
    second: number,
    maxFirst?: number,
    minSecond?: number,
    label?: [string, string]
  }, setter: (first: number, second: number) => void) => {
    const dataSource = [
      firstDataSource.filter(it => Number(it.value) <= maxFirst),
      secondDataSource.filter(it => Number(it.value) >= minSecond)
    ]
    Popup.picker({
      title: title,
      value: [`${first}`, `${second}`],
      dataSource: dataSource,
      singlePicker: false,
      label: label,
      cancelText: I18n.getLang('auto_scan_system_cancel'),
      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
      onConfirm: (values: string[], _index, {close}) => {
        setter(Number(values[0]), Number(values[1]))
        close()
      }
    })
  }

  const selectValue = (title: string, value: number, setter: (value: number) => void) => {
    Popup.picker({
      title: title,
      value: `${value}`,
      dataSource: dripTimeDataSource,
      label: ['s'],
      cancelText: I18n.getLang('auto_scan_system_cancel'),
      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
      onConfirm: (value: string, _index, { close }) => {
        setter(Number(value))
        close()
      }
    })
  }

  const styles = StyleSheet.create({
    cardContainer: {
      marginHorizontal: cx(24)
    },
    content: {
      flexDirection: 'column',
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      padding: cx(8),
    }
  })

  return (
    <Page
      backText={params.name}
      onBackClick={!showConfirm ? navigation.goBack : undefined}
      rightButtonIcon={canSave ? res.ic_check : res.ic_uncheck}
      rightButtonDisabled={state.loading}
      loading={state.loading}
      showBackDialog={showConfirm}
      headlineText={I18n.getLang('thermostat_editauto')}
      backDialogTitle={I18n.getLang('cancel_dialog_leave_unsaved_titel')}
      backDialogContent={I18n.getLang('cancel_dialog_delete_item_fixedtimecycle_description')}
      rightButtonIconClick={onSave}
    >
      <ScrollView nestedScrollEnabled={true}>
        <LdvWeekView
          value={state.item.weeks}
          style={styles.cardContainer}
          onSelect={(index: number) => {
            const rawIndex = index - 1
            state.item.weeks[rawIndex] = state.item.weeks[rawIndex] === 1 ? 0 : 1
          }}/>
        <Spacer/>
        <Segmented
          style={styles.cardContainer}
          options={irrigationTypeOptions} value={state.item.type}
          onChange={(value) => {
            state.item.type = value as IrrigationType
          }}
        />
        <Spacer/>
        <Segmented
          style={styles.cardContainer}
          options={irrigationModeOptions}
          value={state.item.mode}
          onChange={(value) => {
            state.item.mode = value as IrrigationMode
          }}
        />
        <Spacer />
        <View style={styles.content}>
          <Cell title={I18n.getLang('thermostat_starttime')} value={formatHourAndMinute(state.item.startHour, state.item.startMinute, state.is24Hour)} onPress={() => {
            selectTime(I18n.getLang('thermostat_starttime'), state.item.startHour, state.item.startMinute, 23,(hour: number, minute: number) => {
              state.item.startHour = hour
              state.item.startMinute = minute
            }, !state.is24Hour)
          }} />
          <Spacer height={cx(8)} />
          {
            state.item.type === IrrigationType.Irrigation ? (
              state.item.mode === IrrigationMode.Quantity ? <Cell title={I18n.getLang('irrigation_by_quantity')} value={`${state.item.waterVolume}L`} onPress={() => {
                  inputValue(I18n.getLang('irrigation_by_quantity'), state.item.waterVolume, (value: number) => {
                    state.item.waterVolume = value
                  })
                }} />
                : <Cell title={I18n.getLang('irrigation_duration')} value={formatHourAndMinute(state.item.durationHour, state.item.durationMinute)} onPress={() => {
                  selectValues({title: I18n.getLang('irrigation_duration'), first: state.item.durationHour, second: state.item.durationMinute, maxFirst: 11}, (hour: number, minute: number) => {
                    state.item.durationHour = hour
                    state.item.durationMinute = minute
                  })
                }} />
            ) : (
              <>
                {state.item.mode === IrrigationMode.Quantity ?
                  <Cell title={I18n.getLang('spray_by_quantity')} value={`${state.item.waterVolume}L`} onPress={() => {
                    inputValue(I18n.getLang('spray_by_quantity'), state.item.waterVolume, (value: number) => {
                      state.item.waterVolume = value
                    })
                  }}/>
                  : <Cell title={I18n.getLang('misting_period')} value={formatHourAndMinute(state.item.durationHour, state.item.durationMinute)} onPress={() => {
                    selectValues({ title: I18n.getLang('misting_period'), first: state.item.durationHour, second: state.item.durationMinute, maxFirst: 11}, (hour: number, minute: number) => {
                      state.item.durationHour = hour
                      state.item.durationMinute = minute
                    })
                  }}/>}
                <Spacer height={cx(8)}/>
                <Cell title={I18n.getLang('drip_irrigation_time')} value={`${state.item.drippingTime}s`} onPress={() => {
                  selectValue(I18n.getLang('drip_irrigation_time'), state.item.drippingTime, (value: number) => {
                    state.item.drippingTime = value
                  })
                }}/>
                <Spacer height={cx(8)}/>
                <Cell title={I18n.getLang('intermittent_time')} value={formatHourAndMinute(state.item.intervalMinute, state.item.intervalSecond)} onPress={() => {
                  selectValues({title: I18n.getLang('intermittent_time'), first: state.item.intervalMinute, second: state.item.intervalSecond, maxFirst: 15, minSecond: 5, label: ['min', 's']}, (minute: number, second: number) => {
                    state.item.intervalMinute = minute
                    state.item.intervalSecond = second
                  })
                }}/>
              </>
            )
          }
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(AutomaticScheduleDetailPage)
