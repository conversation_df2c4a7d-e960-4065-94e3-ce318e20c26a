import React, {useMemo} from "react";
import {Utils} from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useNavigation} from '@react-navigation/core';
import AutomaticScheduleItem from "../../components/AutomaticScheduleItem";
import {IrrigationMode, IrrigationPlan, IrrigationType, useTimeFormat, useTimerList} from "./AutomaticScheduleAction";
import Spacer from "@ledvance/base/src/components/Spacer";
import {RouterKey} from "../../navigation/Router";
import {useReactive} from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import {showDialog} from "@ledvance/base/src/utils/common";

const {withTheme} = Utils.ThemeUtils;

const AutomaticSchedulePage = () => {
  const deviceInfo = useDeviceInfo()
  const navigation = useNavigation()
  const [timeFormat] = useTimeFormat()
  const [timer1, timer2, timer3, setTimer] = useTimerList()

  const state = useReactive({
    loading: false
  })

  const is24Hour = useMemo(() => {
    return timeFormat === '24'
  }, [timeFormat])

  const checkItemValid = (item: IrrigationPlan) => {
    if (item.type === IrrigationType.Irrigation) {
      if (item.mode === IrrigationMode.Quantity) {
        return item.waterVolume > 0
      } else {
        return (item.durationHour > 0 || item.durationMinute > 0)
      }
    } else {
      const commonRes = item.drippingTime > 0 && (item.intervalMinute > 0 || item.intervalSecond > 0)
      if (item.mode === IrrigationMode.Quantity) {
        return item.waterVolume > 0 && commonRes
      } else {
        return (item.durationHour > 0 || item.durationMinute > 0) && commonRes
      }
    }
  }

  const onEditSchedule = (name: string, item: IrrigationPlan) => {
    navigation.navigate(RouterKey.automatic_schedule_detail, {
      name,
      item,
      checkItemValid,
      onScheduleChange: async (item: IrrigationPlan) => {
        const res = await setTimer(item)
        if (res.success) {
          navigation.goBack()
        }
      }
    })
  }

  const onChangeSwitch = async (item: IrrigationPlan, enable: boolean) => {
    if (enable && !checkItemValid(item)) {
      showDialog({
        method: 'alert',
        title: I18n.getLang('irrigation_schedule_param_invalid'),
        onConfirm: (_, {close}) => {
          close()
        }
      })
      return
    }
    state.loading = true
    await setTimer({...item, enable: enable})
    state.loading = false
  }

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('thermostat_automode')}
      loading={state.loading}
    >
      <AutomaticScheduleItem
        name={I18n.getLang('first_irrigation')}
        item={timer1}
        is24Hour={is24Hour}
        onEnableChange={async (enable) => {
          await onChangeSwitch(timer1, enable)
        }}
        onPress={() => {
          onEditSchedule(I18n.getLang('first_irrigation'), timer1)
        }}/>
      <Spacer/>
      <AutomaticScheduleItem
        name={I18n.getLang('second_irrigation')}
        item={timer2}
        is24Hour={is24Hour}
        onEnableChange={async (enable) => {
          await onChangeSwitch(timer2, enable)
        }}
        onPress={() => {
          onEditSchedule(I18n.getLang('second_irrigation'), timer2)
        }}/>
      <Spacer/>
      <AutomaticScheduleItem
        name={I18n.getLang('third_irrigation')}
        item={timer3}
        is24Hour={is24Hour}
        onEnableChange={async (enable) => {
          await onChangeSwitch(timer3, enable)
        }}
        onPress={() => {
          onEditSchedule(I18n.getLang('third_irrigation'), timer3)
        }}/>
    </Page>
  )
}

export default withTheme(AutomaticSchedulePage)
