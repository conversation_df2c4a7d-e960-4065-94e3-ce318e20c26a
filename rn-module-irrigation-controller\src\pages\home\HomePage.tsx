import {useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, {useMemo} from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import {Utils} from 'tuya-panel-kit'
import {Image, ScrollView, StyleSheet, Text, TouchableOpacity, View} from "react-native";
import {
  parseNextTime,
  TimerDelay,
  useAdvanceData, useBatteryPercentage,
  useFlow,
  useHumidity,
  useNextTime,
  useTemp,
  useTimeFormat,
  useTimerDelay,
  useWorkStatus,
  WorkStatus,
} from "../../features/FeatureHooks";
import {NativeApi} from "@ledvance/base/src/api/native";
import BatteryPercentageView from "@ledvance/base/src/components/BatteryPercentageView";
import Card from "@ledvance/base/src/components/Card";
import {useInterval, useReactive} from "ahooks";
import Spacer from "@ledvance/base/src/components/Spacer";
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import {useNavigation} from '@react-navigation/core'
import {RouterKey} from "../../navigation/Router";
import I18n from "@ledvance/base/src/i18n";

const cx = Utils.RatioUtils.convertX
const {withTheme} = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const HomePage = (props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const navigation = useNavigation()
  const batteryPercentage = useBatteryPercentage()
  const temp = useTemp()
  const humidity = useHumidity()
  const flow = useFlow()
  const [workStatus] = useWorkStatus()
  const [timerDelay, setTimerDelay] = useTimerDelay()
  const [timeFormat] = useTimeFormat()
  const [nextTime] = useNextTime()
  const advancedData = useAdvanceData()

  const state = useReactive({
    showWater: true
  })

  useInterval(() => {
    state.showWater = !state.showWater
  }, 10000)

  const workStatusName = useMemo(() => {
    switch (workStatus) {
      case WorkStatus.Idle:
        return I18n.getLang('working_status_idle')
      case WorkStatus.Manual:
        return I18n.getLang('working_status_manual')
      case WorkStatus.Automatic:
        return I18n.getLang('working_status_automatic')
      case WorkStatus.RainDelay:
        return I18n.getLang('working_status_delay')
    }
  }, [workStatus])

  const getTimerDelayIcon = (val: TimerDelay) => {
    switch (val) {
      case TimerDelay.OnDay:
        return res.delay_24h
      case TimerDelay.TwoDays:
        return res.delay_48h
      case TimerDelay.ThreeDays:
        return res.delay_72h
    }
  }

  const styles = StyleSheet.create({
    title: {
      marginStart: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    content: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    dataContainer: {
      alignItems: 'center',
    },
    dataImage: {
      width: cx(200),
      height: cx(200),
      justifyContent: 'center',
      alignItems: 'center',
    },
    textContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center'
    },
    valueText: {
      color: props.theme?.global.brand,
      fontSize: cx(20),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    labelText: {
      color: props.theme?.global.fontColor,
      marginTop: cx(10),
      fontSize: cx(12),
      textAlign: 'center',
      maxWidth: cx(150),
    },
    statusText: {
      marginVertical: cx(10),
      color: props.theme?.global.fontColor,
    }
  })

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}>
      <ScrollView>
        <BatteryPercentageView value={batteryPercentage} />
        <Spacer height={cx(10)} />
        <TouchableOpacity onPress={() => {
          navigation.navigate(RouterKey.water_consumption)
        }}>
          <Card style={{marginHorizontal: cx(24), marginTop: cx(10)}}>
            <Text style={styles.title}>{I18n.getLang('irrigation_controller')}</Text>
            <View style={styles.content}>
              <View style={styles.dataContainer}>
                <Image source={{ uri: res.home_data_bg}} style={styles.dataImage}/>
                {
                  state.showWater ? <View style={styles.textContainer}>
                    <Text style={styles.valueText}>{flow}L</Text>
                    <Text style={styles.labelText}>{I18n.getLang('last_water_usage')}</Text>
                  </View> : <View style={styles.textContainer}>
                    <Text style={styles.valueText}>{humidity}%</Text>
                    <Text style={styles.valueText}>{temp}℃</Text>
                    <Text style={styles.labelText}>{I18n.getLang('current_temp_humidity')}</Text>
                  </View>
                }
              </View>
              <Text style={styles.statusText}>{I18n.getLang('working_status')}: {workStatusName}</Text>
              <Text style={styles.statusText}>{I18n.getLang('next_irrigation')}: {parseNextTime(nextTime, timeFormat)}</Text>
            </View>
          </Card>
        </TouchableOpacity>
        <Spacer height={cx(10)}/>
        <Card style={{marginHorizontal: cx(24), marginTop: cx(10)}}>
          <Text style={styles.title}>{I18n.getLang('working_status_delay')}</Text>
          <View
            style={{flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', marginBottom: cx(20)}}>
            {
              [TimerDelay.OnDay, TimerDelay.TwoDays, TimerDelay.ThreeDays].map(delay => {
                return <TouchableOpacity key={delay} onPress={() => {
                  setTimerDelay(timerDelay === delay ? TimerDelay.Zero : delay).then()
                }}>
                  <Image source={{ uri: getTimerDelayIcon(delay)}} style={{
                    width: cx(50),
                    height: cx(50),
                    tintColor: timerDelay === delay ? props.theme?.icon.primary : props.theme?.icon.normal
                  }}/>
                </TouchableOpacity>
              })
            }
          </View>
        </Card>
        <Spacer height={cx(10)}/>
        <AdvanceList advanceData={advancedData}/>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
