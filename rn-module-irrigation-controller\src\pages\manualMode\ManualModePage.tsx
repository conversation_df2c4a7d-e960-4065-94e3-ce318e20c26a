import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import React, { useEffect } from "react";
import {Image, StyleSheet, Text, TouchableOpacity, View} from "react-native";
import {Progress, Utils} from "tuya-panel-kit";
import ThemeType from "@ledvance/base/src/config/themeType";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useManualSwitch, useManualTimer} from "../../features/FeatureHooks";
import res from "@ledvance/base/src/res";
import I18n from "@ledvance/base/src/i18n";

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

const ManualModePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo()
  const [manualTimer, setManualTimer] = useManualTimer()
  const [manualSwitch, setManualSwitch] = useManualSwitch()

  const state = useReactive({
    loading: false,
    value: manualTimer
  })

  useEffect(() => {
    state.value = manualTimer
  }, [manualTimer])

  const styles = StyleSheet.create({
    container: {
      marginHorizontal: cx(24),
      justifyContent: 'center',
      alignItems: 'center',
    },
    innerContainer: {
      position: 'absolute',
      flexDirection: 'row',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      justifyContent: 'center',
      alignItems: 'center',
    },
    outerContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
      width: cx(250),
      height: cx(250)
    },
    60: {
      position: 'absolute',
      top: cx(-15),
      left: cx(110)
    },
    15: {
      position: 'absolute',
      top: cx(110),
      right: cx(-15)
    },
    30: {
      position: 'absolute',
      bottom: cx(-15),
      left: cx(110)
    },
    45: {
      position: 'absolute',
      top: cx(110),
      left: cx(-15)
    },
    check: {
      backgroundColor: props.theme?.button.primary,
      borderRadius: cx(15),
      width: cx(30),
      height: cx(30),
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkText: {
      color: props.theme?.button.fontColor
    },
    uncheck: {
      borderRadius: cx(15),
      width: cx(30),
      height: cx(30),
      justifyContent: 'center',
      alignItems: 'center',
    },
    text: {
      color: props.theme?.global.fontColor,
    },
    textValue: {
      color: props.theme?.global.brand,
      fontSize: cx(20),
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    button: {
      width: cx(150),
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: cx(5),
      backgroundColor: props.theme?.button.primary
    },
    disabled: {
      width: cx(150),
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: cx(5),
      backgroundColor: props.theme?.button.disabled
    }
  })
  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('manual_mode')}
      loading={state.loading}
    >
      <View style={styles.container}>
        <Spacer />
        <View style={styles.outerContainer}>
          {
            [60, 15, 30, 45].map(value => (
              <TouchableOpacity
                key={`${value}`}
                style={[styles[value], state.value === value ? styles.check : styles.uncheck]}
                onPress={async () => {
                  if (manualSwitch) {
                    state.value = value
                    await setManualTimer(value)
                  }
                }}>
                <Text style={state.value === value ? styles.checkText : styles.text}>{value}</Text>
              </TouchableOpacity>
            ))
          }
          <Progress.Space
            style={{width: cx(200), height: cx(200)}}
            foreColor={'#f60'}
            scaleNumber={60}
            strokeWidth={2}
            startDegree={270}
            andDegree={360}
            min={0}
            max={60}
            stepValue={1}
            disabled={!manualSwitch}
            value={state.value}
            onValueChange={(value: number) => state.value = value}
            onSlidingComplete={async (value: number) => await setManualTimer(value)}
            renderCenterView={
              <View style={styles.innerContainer}>
                <Text style={styles.text}><Text style={styles.textValue}>{state.value}</Text> min</Text>
              </View>
            }
          />
        </View>
        <Spacer height={cx(50)}/>
        <TouchableOpacity style={[state.value === 0 ? styles.disabled : styles.button]} onPress={() => {
          if (state.value !== 0) {
            setManualSwitch(!manualSwitch).then()
          }
        }}>
          <Image source={{ uri: manualSwitch ? res.start : res.stop}} style={{width: cx(40), height: cx(40)}} />
        </TouchableOpacity>
      </View>
    </Page>
  )
}

export default withTheme(ManualModePage)
