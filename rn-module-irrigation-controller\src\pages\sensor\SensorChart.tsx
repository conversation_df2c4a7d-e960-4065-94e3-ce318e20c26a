import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import ECharts from '@ledvance/react-native-echarts-pro';
import ThemeType from "@ledvance/base/src/config/themeType";
import {Utils} from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import res from "@ledvance/base/src/res";
import dayjs from "dayjs";
import {exportCsvFile, getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import I18n from "@ledvance/base/src/i18n";
import Spacer from "@ledvance/base/src/components/Spacer";
import InfoText from "@ledvance/base/src/components/InfoText";
import {useDp} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {DpKeys} from "../../features/FeatureHooks";

const {withTheme} = Utils.ThemeUtils
const cx = Utils.RatioUtils.convertX;

interface BarChartProps {
  theme?: ThemeType
}

interface ChartProps {
  setNewOption: (option: any, optionSetting?: any) => void
  getInstance: (functionName: string, params?: any) => Promise<any>
}

function parseCountValue(countValue?: string) {
  if (!countValue) {
    return []
  }
  const values: number[] = []
  for (let i = 0; i < 10; i++) {
    const dayStart: number = i * 24
    const dayValue: string = countValue.slice(dayStart, dayStart + 24)

    const hours: number[] = []
    for (let j = 0; j < 12; j++) {
      const hourStart: number = j * 2
      const hourValue: string = dayValue.slice(hourStart, hourStart + 2)
      hours.push(parseInt(hourValue, 16))
    }
    const value: number = Math.max(...hours)
    values.push(value)
  }

  return values
}
export function useTempCount() {
  const [dpValue] = useDp<string, any>(getGlobalParamsDp(DpKeys.tempCount))
  return parseCountValue(dpValue)
}

export function useHumidityCount() {
  const [dpValue] = useDp<string, any>(getGlobalParamsDp(DpKeys.humidityCount))
  return parseCountValue(dpValue)
}

const SensorChart = (props: BarChartProps) => {
  const echarts = useRef<ChartProps>()
  const tempValues = useTempCount()
  const humidityValues = useHumidityCount()
  const [option, setOption] = useState({})
  const [labels, setLabels] = useState<string[]>([])

  const hasValues = useMemo(() => {
    return tempValues.length > 0 || humidityValues.length > 0
  }, [tempValues, humidityValues])

  useEffect(() => {
    const today = dayjs()
    const labels: string[] = []
    setLabels(labels)
    for (let i = 0; i < 10; i++) {
      const label = today.add(-1 * i, 'day').format('DD/MM')
      labels.push(label)
    }
    const option = {
      tooltip: {
        show: true,
        triggerOn: 'click',
        trigger: 'axis',
      },
      legend: {
        show: true,
        data: [I18n.getLang('legend_temp'), I18n.getLang('legend_humidity')],
        textStyle: {
          color: props.theme?.global.fontColor,
        }
      },
      grid: {
        width: '85%',
      },
      xAxis: {
        data: labels,
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: props.theme?.global.secondFontColor,
          interval: 0,
        }
      },
      yAxis: [{
        type: 'value',
        min: -20,
        max: 100,
        nameTextStyle: {
          fontSize: 14,
          align:'left',
          color: props.theme?.global.secondFontColor,
        }
      }],
      series: [
        {
          name: I18n.getLang('legend_temp'),
          type: 'line',
          data: tempValues,
          itemStyle: {
            emphasis: {
              color: '#FFC2A9', // Color when bar is clicked
            },
            color: '#FFC2A9',
            borderRadius: 2,
          },
          barMaxWidth: 10,
          select: {
            itemStyle: {
              borderColor: '#FFC2A9',
            }
          },
          selectedMode: 'single',
        },
        {
          name: I18n.getLang('legend_humidity'),
          type: 'line',
          data: humidityValues,
          smooth: true,
          showSymbol: false,
          color: '#F49431',
          selectedMode: "single",
        }
      ],
      dataZoom: {
        start: 0,
        type: "inside",
        maxValueSpan: 5,
        zoomLock: true,
      },
      customMapData: {}
    }
    setOption(option)
  }, [])

  const styles = StyleSheet.create({
    listEmptyView: {
      alignItems: 'center',
    },
    listEmptyImage: {
      width: cx(200),
      height: cx(200),
    },
    listEmptyText: {
      flex: 0
    },
    downloadIcon: {
      width: cx(24),
      height: cx(24),
      tintColor: props.theme?.global.brand,
      position: 'absolute',
      right: 0,
      top: cx(10)
    }
  })

  return (
    <Page
      backText={I18n.getLang('sensor')}
      headlineText={I18n.getLang('sensor')}
      headlineIcon={hasValues ? res.download_icon : undefined}
      onHeadlineIconClick={() => {
        if (!hasValues) {
          return
        }
        const headers = [I18n.getLang('date'), I18n.getLang('legend_temp'), I18n.getLang('legend_humidity')]
        const values = labels.map((item, index) => [item, tempValues[index], humidityValues[index]])
        exportCsvFile(headers, values, 'sensor')
      }}
    >
      {
        !hasValues ? (
            <View style={styles.listEmptyView}>
              <Spacer height={cx(26)}/>
              <Image
                style={styles.listEmptyImage}
                source={{uri: res.ldv_timer_empty}}/>
              <Spacer height={cx(14)}/>
              <InfoText
                style={{width: 'auto', alignItems: 'center'}}
                text={I18n.getLang('energyconsumption_emptydata')}
                icon={res.ic_info}
                textStyle={{flex: undefined}}
              />
            </View>
          ) :
          <ECharts
              option={option}
              ref={echarts}
              height={cx(400)}
              enableParseStringFunction={true}
          />
      }
    </Page>
  )
}

export default withTheme(SensorChart)
