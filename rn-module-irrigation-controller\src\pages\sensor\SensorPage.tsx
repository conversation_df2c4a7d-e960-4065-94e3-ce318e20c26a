import React, {useCallback, useMemo} from 'react'
import {Utils} from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import Spacer from "@ledvance/base/src/components/Spacer";
import {StyleSheet, Text, TouchableOpacity, View} from "react-native";
import ThemeType from '@ledvance/base/src/config/themeType'
import {DpKeys, useHumidity, useHumiditySensor, useHumiditySensorStatus, useTemp} from "../../features/FeatureHooks";
import I18n from "@ledvance/base/src/i18n";
import Card from "@ledvance/base/src/components/Card";
import {RouterKey} from "../../navigation/Router";
import {useNavigation} from '@react-navigation/core';
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import {NativeApi} from "@ledvance/base/src/api/native";
import {getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import { RoutineParam } from '@ledvance/base/src/utils/interface';

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

const SensorPage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const navigation = useNavigation()
  const [humiditySensor, setHumiditySensor] = useHumiditySensor()
  const humiditySensorStatus = useHumiditySensorStatus()
  const temp = useTemp()
  const humidity = useHumidity()
  const state = useReactive({
    loading: false
  })

  const routineParamMap: { [key: string]: RoutineParam } = useMemo(() => {
    return {
      one: {
        pageType: 'Create',
        conditionType: 'All',
        conditions: [{
          type: "DeviceStatusChange",
          targetId: getGlobalParamsDp(DpKeys.humidity),
          operator: "Less",
          value: "20"
        }, {
          type: "DeviceStatusChange",
          targetId: getGlobalParamsDp(DpKeys.temp),
          operator: "Greater",
          value: "1"
        }],
        tasks: [{
          type: 'Device',
          targetId: getGlobalParamsDp(DpKeys.manualSwitch),
          value: "false"
        }]
      },
      two: {
        pageType: 'Create',
        conditionType: 'All',
        conditions: [{
          type: "DeviceStatusChange",
          targetId: getGlobalParamsDp(DpKeys.humidity),
          operator: "Greater",
          value: "60"
        }, {
          type: "DeviceStatusChange",
          targetId: getGlobalParamsDp(DpKeys.temp),
          operator: "Greater",
          value: "1"
        }],
        tasks: [{
          type: 'Device',
          targetId: getGlobalParamsDp(DpKeys.manualSwitch),
          value: "true"
        }]
      },
      three: {
        pageType: 'Create',
      }
    }
  }, [])

  const goRoutine = useCallback((param: RoutineParam) => {
    console.log('param', param)
    NativeApi.toRoutinesPage({
      backTitle: devInfo.name,
      deviceId: devInfo.devId,
      routineParam: param
    })
  }, [])

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      marginTop: cx(10)
    },
    status: {
      marginEnd: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    sensorTitle: {
      marginStart: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    sensor: {
      flexDirection: 'row',
      marginStart: cx(16),
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    sensorItem: {
      alignItems: 'center',
    },
    sensorValue: {
      color: props.theme?.global.brand,
      fontSize: cx(20),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    sensorLabel: {
      color: props.theme?.global.secondFontColor
    },
    routineTitle: {
      marginStart: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    routineItem: {
      marginHorizontal: cx(10),
      marginBottom: cx(10),
      padding: cx(10)
    },
    routineText: {
      color: props.theme?.global.fontColor,
    }
  })

  return (<Page
    backText={devInfo.name}
    headlineText={I18n.getLang('sensor')}
    loading={state.loading}>
    <Card style={styles.card}>
      <LdvSwitch
        title={I18n.getLang('activate_sensor')}
        colorAlpha={1}
        enable={humiditySensor}
        setEnable={async (v: boolean) => {
          if (state.loading) {
            return
          }
          state.loading = true
          await setHumiditySensor(v)
          state.loading = false
        }}
      />
    </Card>
    {
      humiditySensor &&
        <>
            <Card style={styles.card}>
              <TouchableOpacity onPress={() => navigation.navigate(RouterKey.sensor_chart)}>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                        <Text style={styles.sensorTitle}>{I18n.getLang('sensor')}</Text>
                        <Text
                            style={styles.status}>{humiditySensorStatus ? I18n.getLang('online', 'Online') : I18n.getLang('offline', 'Offline')}</Text>
                    </View>
                    <View style={styles.sensor}>
                        <View style={styles.sensorItem}>
                            <Text style={styles.sensorValue}>{temp}℃</Text>
                            <Text style={styles.sensorLabel}>{I18n.getLang('temperature')}</Text>
                        </View>
                        <View style={styles.sensorItem}>
                            <Text style={styles.sensorValue}>{humidity}%</Text>
                            <Text style={styles.sensorLabel}>{I18n.getLang('humidity')}</Text>
                        </View>
                    </View>
                    <Spacer/>
                </TouchableOpacity>
            </Card>
            <Spacer/>
            <Card style={styles.card}>
                <Text style={styles.routineTitle}>{I18n.getLang('soilsensor_R')}</Text>
                <TouchableOpacity onPress={() => goRoutine(routineParamMap['one'])}>
                    <Card style={styles.routineItem}>
                        <Text style={styles.routineText}>{I18n.getLang('soilsensor_R1')}</Text>
                    </Card>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => goRoutine(routineParamMap['two'])}>
                    <Card style={styles.routineItem}>
                        <Text style={styles.routineText}>{I18n.getLang('soilsensor_R2')}</Text>
                    </Card>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => goRoutine(routineParamMap['three'])}>
                    <Card style={styles.routineItem}>
                        <Text style={styles.routineText}>{I18n.getLang('body_create_your_own_routine')}</Text>
                    </Card>
                </TouchableOpacity>
            </Card>
        </>
    }
  </Page>)
}

export default withTheme(SensorPage)
