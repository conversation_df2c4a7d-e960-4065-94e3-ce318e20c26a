import {useDeviceInfo} from '@ledvance/base/src/models/modules/NativePropsSlice'
import React from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import {Utils} from 'tuya-panel-kit'
import I18n from "@ledvance/base/src/i18n";
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import {useTimeFormat} from "../../features/FeatureHooks";
import Card from "@ledvance/base/src/components/Card";

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface SettingsProp {
  theme?: ThemeType
}

const SettingsPage = (_: SettingsProp) => {
  const devInfo = useDeviceInfo()
  const [timeFormat, setTimeFormat] = useTimeFormat()

  return (
    <Page
      backText={devInfo.name}
      headlineText={I18n.getLang('contact_sensor_specific_settings')}
    >
      <Card style={{ marginHorizontal: cx(24) }}>
        <LdvSwitch
          title={I18n.getLang('time_format')}
          colorAlpha={1}
          leftValue={'12h'}
          rightValue={'24h'}
          enable={timeFormat === '24'}
          setEnable={async (v) => {
            await setTimeFormat(v ? '24' : '12')
          }}
        />
      </Card>
    </Page>
  )
}

export default withTheme(SettingsPage)
