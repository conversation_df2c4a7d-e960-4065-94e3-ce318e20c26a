import ThemeType from "@ledvance/base/src/config/themeType";
import React, {PropsWithChildren, useEffect} from "react";
import {StyleSheet, Text, TouchableOpacity, View, ViewProps} from "react-native";
import {Popup, Utils} from "tuya-panel-kit";
import {useReactive} from "ahooks";
import I18n from "@ledvance/base/src/i18n/index";
import dayjs from "dayjs";
import {DateType} from "./DateTypeItem";

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

interface DateSelectedItemProps extends PropsWithChildren<ViewProps> {
  theme?: ThemeType
  date: string,
  dateType: DateType,
  onDateChange: (year: string, month: string) => void
}

export default withTheme(function DateSelectedItem(props: DateSelectedItemProps) {
  const {dateType, date, onDateChange} = props;
  const state = useReactive({
    date: date,
    dateType: dateType,
  });

  const styles = StyleSheet.create({
    root: {
      width: '100%',
      flexDirection: 'row',
      borderRadius: cx(4),
      backgroundColor: props.theme?.textInput.background,
      alignItems: 'center',
      height: cx(44),
      borderBottomWidth: cx(1),
      borderBottomColor: props.theme?.textInput.line,
    },
    date: {
      fontSize: cx(16),
      textAlign: 'center',
      flex: 1,
      color: props.theme?.textInput.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
    }
  });

  useEffect(() => {
    const format = dateType === DateType.Year ? 'YYYY' : 'MM/YYYY'
    state.date = dayjs(date).format(format)
  }, [dateType, date])

  return (<View style={props.style}>
    <TouchableOpacity
      style={{width: '100%',}}
      onPress={() => {
        Popup.datePicker({
          title: I18n.getLang('date'),
          mode: dateType === DateType.Year ? 'year' : 'month',
          defaultDate: dayjs(date).toDate(),
          cancelText: I18n.getLang('auto_scan_system_cancel'),
          confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
          onConfirm: (date, {close}) => {
            const [year, month] = dayjs(date).format('YYYY-MM').split('-')
            onDateChange(year, month)
            close()
          }
        })
      }}
    >
      <View style={styles.root}>
        <Text style={styles.date}>{state.date}</Text>
      </View>
    </TouchableOpacity>

  </View>)
})

