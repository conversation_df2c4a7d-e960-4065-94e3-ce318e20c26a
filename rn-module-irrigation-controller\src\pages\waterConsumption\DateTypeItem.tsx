import ThemeType from "@ledvance/base/src/config/themeType";
import React, {PropsWithChildren} from "react";
import {Text, TouchableOpacity, View, ViewProps} from "react-native";
import {Popup, Utils} from "tuya-panel-kit";
import {useReactive, useUpdateEffect} from "ahooks";
import I18n from "@ledvance/base/src/i18n/index";

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

export enum DateType {
  Year = 'Year',
  Month = 'Month',
}

interface DateTypeItemProps extends PropsWithChildren<ViewProps> {
  theme?: ThemeType
  dateType: DateType,
  onDateTypeChange: (dateType: DateType) => void
}

const getDateTypeTitle = (dateType: DateType) => {
  switch (dateType) {
    case DateType.Year:
      return I18n.getLang('year')
    case DateType.Month:
      return I18n.getLang('month')
  }
}

export default withTheme(function DateTypeItem(props: DateTypeItemProps) {
  const {dateType} = props;
  const state = useReactive({
    dateType: dateType,
    dateTypeTitle: getDateTypeTitle(dateType)
  });
  useUpdateEffect(() => {
    state.dateType = dateType;
    state.dateTypeTitle = getDateTypeTitle(dateType);
  }, [dateType])
  return (<View style={props.style}>
    <TouchableOpacity
      style={{width: '100%',}}
      onPress={() => {
        Popup.list({
          title: I18n.getLang('date_type'),
          type: 'radio',
          value: state.dateType,
          dataSource: [
            {
              title: getDateTypeTitle(DateType.Month),
              key: DateType.Month,
              value: DateType.Month,
            },
            {
              title: getDateTypeTitle(DateType.Year),
              key: DateType.Year,
              value: DateType.Year,
            }
          ],
          cancelText: I18n.getLang('auto_scan_system_cancel'),
          confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
          onConfirm: (value: DateType, { close }) => {
            props.onDateTypeChange(value)
            close()
          }
        })
      }}
    >
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          borderRadius: cx(4),
          backgroundColor: props.theme?.textInput.background,
          alignItems: 'center',
          height: cx(44),
          borderBottomWidth: cx(1),
          borderBottomColor: props.theme?.textInput.line,
        }}
      >
        <Text style={{
          fontSize: cx(16),
          textAlign: 'center',
          flex: 1,
          color: props.theme?.textInput.fontColor,
          fontFamily: 'helvetica_neue_lt_std_roman',
        }}>{state.dateTypeTitle}</Text>
      </View>
    </TouchableOpacity>
  </View>)
})
