import React from "react";
import {FlatList, Image, StyleProp, StyleSheet, Text, TouchableOpacity, View, ViewStyle} from "react-native";
import {Utils} from "tuya-panel-kit";
import res from "@ledvance/base/src/res";
import Spacer from "@ledvance/base/src/components/Spacer";
import {isEmpty} from "lodash";
import ThemeType from '@ledvance/base/src/config/themeType'
import {DataItem} from "./WaterConsumptionAction";
import I18n from "@ledvance/base/src/i18n";

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

interface OverViewProps {
  theme?: ThemeType
  style?: StyleProp<ViewStyle>
  headlineText?: string
  headlineClick?: () => void
  overviewItemClick?: (item: DataItem) => void
  overViewList: DataItem[]
}

const OverView = (props: OverViewProps) => {

  const styles = StyleSheet.create({
    listEmptyContainer: {
      alignItems: 'center'
    },
    listEmptyImage: {
      width: cx(200),
      height: cx(200),
    },
    listEmptyTextIcon: {
      width: cx(16),
      height: cx(16),
      tintColor: props.theme?.global.fontColor,
    },
    listEmptyText: {
      color: props.theme?.global.fontColor,
      fontSize: cx(12),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    overviewItemText: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    },
  })
  return (
    <View style={[props.style]}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text style={{color: props.theme?.global.fontColor, fontWeight: 'bold'}}>{props.headlineText}</Text>
        {!isEmpty(props.overViewList) && <TouchableOpacity
            onPress={() => {
              props.headlineClick && props.headlineClick()
            }}
        >
            <Image
                source={{ uri: res.energy_consumption_chart}}
                style={{width: cx(16), height: cx(16), marginLeft: cx(8)}}
            />
        </TouchableOpacity>}
      </View>
      <Spacer/>
      <FlatList
        data={props.overViewList}
        renderItem={({item}) => (
          <View>
            <View style={{
              backgroundColor: props.theme?.card.background,
              alignItems: 'center',
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}>
              <Text style={styles.overviewItemText}>{item.label}</Text>
              <View style={{flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                <View style={{flexDirection: 'row'}}>
                  <Text
                    style={[styles.overviewItemText, {color: props.theme?.global.secondFontColor}]}>{item.value}</Text>
                  <Text style={[styles.overviewItemText, {color: props.theme?.global.secondFontColor}]}> L</Text>
                </View>
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item) => item.label}
        ItemSeparatorComponent={() => <Spacer height={cx(10)}/>}
        ListFooterComponent={() => <Spacer height={cx(30)}/>}
        ListEmptyComponent={() => (
          <View style={styles.listEmptyContainer}>
            <Image
              style={styles.listEmptyImage}
              source={{ uri: res.energy_consumption_empty}}/>
            <Spacer height={cx(10)}/>
            <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: cx(25)}}>
              <Image style={styles.listEmptyTextIcon} source={{ uri: res.co2_icon}}/>
              <Spacer width={cx(4)} height={0}/>
              <Text style={styles.listEmptyText}>
                {I18n.getLang('energyconsumption_emptydata')}
              </Text>
            </View>
          </View>
        )}
      />
    </View>
  )
}

export default withTheme(OverView) as React.ComponentType<OverViewProps>
