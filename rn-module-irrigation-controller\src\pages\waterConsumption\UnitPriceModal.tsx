import React from "react";
import Card from "@ledvance/base/src/components/Card";
import Spacer from "@ledvance/base/src/components/Spacer";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import I18n from "@ledvance/base/src/i18n";
import res from "@ledvance/base/src/res";
import {FlatList, Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View} from "react-native";
import {Modal, Popup, Utils} from "tuya-panel-kit";
import {useReactive, useUpdateEffect} from "ahooks";
import {cloneDeep} from "lodash";
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx, height, statusBarHeight } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

export const UnitList = [
  I18n.getLang('consumption_data_price_per_kwh_currency_value1'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value2'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value3'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value4'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value5'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value6'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value7'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value8'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value9'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value10'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value11'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value12'),
  I18n.getLang('consumption_data_price_per_kwh_currency_value13'),
]

export interface UnitPrice {
  price: string
  unit: string
}
interface UnitPriceModalProps {
  theme?: ThemeType
  visible: boolean
  popupType: 'money' | 'unit'
  title: string
  confirmText?: string
  cancelText?: string
  unitPrice?: UnitPrice
  motionType?: 'none'
  onConfirm?: (data?: UnitPrice) => void
  onCancel?: () => void
}
const UnitPriceModal = (props: UnitPriceModalProps) => {
  const state = useReactive({
    unitPrice: cloneDeep(props.unitPrice),
    unitPopup: false
  })

  useUpdateEffect(() =>{
    state.unitPrice = cloneDeep(props.unitPrice)
  }, [JSON.stringify(props.unitPrice)])

  const styles = StyleSheet.create({
    popupTip: {
      fontSize: cx(16),
      color: props.theme?.global.fontColor,
      fontWeight: 'bold'
    },
    textInput: {
      flex: 1,
      height: cx(44),
      marginStart: cx(16),
      marginEnd: cx(6),
      fontSize: cx(16),
      color: props.theme?.textInput.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    textInputGroup: {
      flexDirection: 'row',
      borderRadius: cx(4),
      backgroundColor: props.theme?.textInput.background,
      alignItems: 'center',
    },
    iconTouchable: {
      marginEnd: cx(18),
      padding: cx(4),
    },
    line: {
      height: 1,
      position: 'absolute',
      start: cx(4),
      end: cx(4),
      bottom: 0,
      backgroundColor: props.theme?.textInput.line,
    },
    unitItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: cx(10),
      alignItems: 'center',
      height: cx(40),

    }
  })

  const getContent = () => {
    if (props.popupType === 'money') {
      return (
        <View>
          <Spacer />
          <Text style={styles.popupTip}>{I18n.getLang('consumption_data_price_per_l_headline_text')}</Text>
          <Spacer height={cx(40)} />
          <Text style={{ fontSize: cx(14), color: props.theme?.global.fontColor }}>{I18n.getLang('consumption_data_price_per_l_description_text')}</Text>
          <Spacer height={cx(15)} />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 3 }}>
              <Spacer height={cx(4)} />
              <Text style={{ color: props.theme?.global.secondFontColor, marginStart: cx(13), fontFamily: 'helvetica_neue_lt_std_bd' }}>{I18n.getLang('consumption_data_price_per_l_headline_text')}</Text>
              <View style={styles.textInputGroup}>
                <TextInput
                  value={state.unitPrice?.price}
                  onChangeText={(t: string) => {
                    const value = t.replace(/[^0-9.,]/g, '')
                    if (Number(value) > 999999) {
                      if (state.unitPrice) state.unitPrice.price = '999999'
                    } else {
                      if (state.unitPrice) state.unitPrice.price = value
                    }
                  }}
                  style={styles.textInput}
                  keyboardType="numeric"
                />
                <View style={styles.line} />
              </View>
            </View>
            <View style={{ flex: 2, marginLeft: cx(20) }}>
              <TextFieldStyleButton
                text={state.unitPrice?.unit || ''}
                placeholder={I18n.getLang('consumption_data_price_per_kwh_currency_headline_text')}
                onPress={() => (
                  unitPricePopup({
                    unitPrice: state.unitPrice,
                    onItemClick: (title) => {
                      if (state.unitPrice) state.unitPrice.unit = title
                    }
                  })
                )}
              />
            </View>
          </View>
        </View>
      )
    } else if (props.popupType === 'unit') {
      return (
        <View>
          <Spacer />
          <Card>
            <FlatList
              data={UnitList}
              initialNumToRender={15}
              renderItem={({ item }) => (
                <View style={styles.unitItem}>
                  <Text style={{ fontSize: cx(16), color: props.theme?.global.fontColor }}>{item}</Text>
                  {props.unitPrice && props.unitPrice.unit === item && <Image
                      style={{ width: cx(16), height: cx(16) }}
                      source={{ uri: res.app_music_check}}
                      resizeMode="contain"
                  />}
                </View>
              )}
              ItemSeparatorComponent={() => (
                <View style={{ flex: 1, height: 1, backgroundColor: props.theme?.card.background }}></View>
              )}
              keyExtractor={item => item}
            />
          </Card>
        </View>
      )
    } else {
      return (
        <View />
      )
    }

  }

  const unitPricePopup = ({unitPrice, onItemClick}: {
    unitPrice?: UnitPrice,
    onItemClick: (unit: string) => void
  }) => {
    return (
      Popup.custom({
        title: (
          <View style={{ backgroundColor: props.theme?.card.head, flexDirection: 'row', height: cx(60), justifyContent: 'space-between', alignItems: 'center', borderTopLeftRadius: cx(10), borderTopRightRadius: cx(10), paddingHorizontal: cx(8) }}>
            <TouchableOpacity onPress={() => Popup.close()}>
              <Text style={{ color: props.theme?.global.secondBrand, fontSize: cx(16) }}>{I18n.getLang('auto_scan_system_cancel')}</Text>
            </TouchableOpacity>
          </View>
        ),
        wrapperStyle: {
          height: height - statusBarHeight - cx(40),
          backgroundColor: props.theme?.global.background
        },
        footer: (<View style={{ backgroundColor: props.theme?.global.background}}></View>),
        onMaskPress: () => { },
        motionType: 'none',
        useKeyboardView: true,
        content: (
          <View
            style={{ backgroundColor: props.theme?.global.background, paddingHorizontal: cx(16) }}>
            <Spacer />
            <Card>
              <FlatList
                data={UnitList}
                initialNumToRender={15}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    onPress={() => {
                      onItemClick(item)
                      Popup.close()
                    }}
                  >
                    <View style={styles.unitItem}>
                      <Text style={{ fontSize: cx(16), color: props.theme?.global.fontColor }}>{item}</Text>
                      {unitPrice && unitPrice.unit === item && <Image
                          style={{ width: cx(16), height: cx(16) }}
                          source={{ uri: res.app_music_check}}
                          resizeMode="contain"
                      />}
                    </View>
                  </TouchableOpacity>
                )}
                ItemSeparatorComponent={() => (
                  <View style={{ flex: 1, height: 1, backgroundColor: props.theme?.card.background }}></View>
                )}
                keyExtractor={item => item}
              />
            </Card>
            <Spacer />
          </View>
        )
      })
    )
  }
  return (
    <Modal
      visible={props.visible}
      onRequestClose={() => {
        props.onCancel && props.onCancel()
      }}
    >
      <ScrollView>
        <View style={{ backgroundColor: props.theme?.card.head, flexDirection: 'row', height: cx(60), justifyContent: 'space-between', alignItems: 'center', borderTopLeftRadius: cx(10), borderTopRightRadius: cx(10), paddingHorizontal: cx(8) }}>
          <TouchableOpacity onPress={() => {
            props.onCancel && props.onCancel()
          }}>
            <Text style={{ color: props.theme?.button.cancel, fontSize: cx(16) }}>{props.cancelText}</Text>
          </TouchableOpacity>
          <Text style={{ color: props.theme?.global.fontColor, fontSize: cx(16), fontWeight: 'bold' }}>{props.title}</Text>
          <TouchableOpacity onPress={() => {
            props.onConfirm && props.onConfirm(state.unitPrice)
          }}>
            <Text style={{ color: props.theme?.button.primary, fontSize: cx(16) }}>{props.confirmText}</Text>
          </TouchableOpacity>
        </View>
        <View style={{ height: height - statusBarHeight - cx(100), paddingHorizontal: cx(16), backgroundColor: props.theme?.global.background }}>
          {getContent()}
        </View>
      </ScrollView>
    </Modal>
  )
}

export default withTheme(UnitPriceModal)
