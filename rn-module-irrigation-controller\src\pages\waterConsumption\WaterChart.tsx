import React, {useEffect, useRef} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import ECharts from '@ledvance/react-native-echarts-pro';
import ThemeType from "@ledvance/base/src/config/themeType";
import {Utils} from "tuya-panel-kit";
import {useWaterStatistic} from "./WaterConsumptionAction";
import Page from "@ledvance/base/src/components/Page";
import res from "@ledvance/base/src/res";
import dayjs from "dayjs";
import DateTypeItem, {DateType} from './DateTypeItem';
import {useDebounceEffect, useReactive, useThrottleEffect, useWhyDidYouUpdate} from "ahooks";
import DateSelectedItem from "./DateSelectedItem";
import {exportCsvFile, loopsText, monthFormatShort} from '@ledvance/base/src/utils/common';
import Spacer from '@ledvance/base/src/components/Spacer';
import InfoText from "@ledvance/base/src/components/InfoText";
import I18n from "@ledvance/base/src/i18n";
import {useParams} from "@ledvance/base/src/hooks/Hooks";
import {UnitPrice} from "./UnitPriceModal";

const {withTheme} = Utils.ThemeUtils
const cx = Utils.RatioUtils.convertX;

interface BarChartProps {
  theme?: ThemeType
}

interface ChartState {
  dateType: DateType
  year: string
  month: string
  loading: boolean
  option: any
  labels: string[],
  values: number[],
  prices: number[]
  flag: number
}

const WaterChart = (props: BarChartProps) => {
  const echarts = useRef()
  const { unitPrice } = useParams<{unitPrice: UnitPrice}>()
  const waterStatistic = useWaterStatistic()
  const state = useReactive<ChartState>({
    dateType: DateType.Year,
    year: '2024',
    month: '12',
    loading: false,
    option: {},
    labels: [],
    values: [],
    prices: [],
    flag: 0
  })

  useEffect(() => {
    const today = dayjs()
    state.year = today.format('YYYY')
    state.month = today.format('MM')
  }, [])

  useThrottleEffect(() => {
    state.loading = true
    const arr = getLabelAndValues(state.dateType, state.year, state.month, Number(unitPrice.price))
    const labels: string[] = arr[0]
    const values: number[] = arr[1]
    const prices: number[] = arr[2]
    state.labels = labels
    state.values = values
    state.prices = prices
    state.option = {
      tooltip: {
        show: true,
        triggerOn: 'click',
        trigger: 'axis',
      },
      legend: {
        show: true,
        data: [I18n.formatValue('format_unit', 'L'), unitPrice.unit],
        textStyle: {
          color: props.theme?.global.fontColor,
        }
      },
      grid: {
        left: '15%',
      },
      xAxis: {
        id: 'date',
        data: labels,
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: props.theme?.global.secondFontColor,
          interval: 0,
        }
      },
      yAxis: [{
        id: 'volume',
        type: 'value',
        name: I18n.formatValue('format_unit', 'L'),
        nameTextStyle: {
          fontSize: 14,
          align: 'left',
          padding: [0, 0, 0, cx(-30)],
          color: props.theme?.global.secondFontColor,
        },
      }, {
        type: 'value',
        name: I18n.formatValue('format_unit', unitPrice.unit),
        position: 'right',
        alignTicks: true,
        min: 0,
        minInterval: 1,
        axisLabel: {
          formatter: function (value: string) {
            const price= parseFloat(value);
            let toFixed = 2;
            if (price >= 100) {
              toFixed = 0;
            } else if (price >= 10) {
              toFixed = 1;
            }
            return price.toFixed(toFixed);
          },
          color:props.theme?.global.secondFontColor,
        },
        nameTextStyle: {
          fontSize: 14,
          align:'right',
          padding: [0, cx(-30), 0, 0],
          color: props.theme?.global.secondFontColor,
        },
      }],
      series: [
        {
          id: 'values',
          name: I18n.formatValue('format_unit', 'L'),
          type: 'bar',
          data: values,
          itemStyle: {
            emphasis: {
              color: '#FFC2A9', // Color when bar is clicked
            },
            color: '#FFC2A9',
            borderRadius: 2,
          },
          barMaxWidth: 10,
          select: {
            itemStyle: {
              borderColor: '#FFC2A9',
            }
          },
          selectedMode: 'single',
        },
        {
          name: unitPrice.unit,
          type: 'line',
          data: prices,
          yAxisIndex: 1,
          smooth: true,
          showSymbol: false,
          color: '#F49431',
          selectedMode: "single",
        }
      ],
      dataZoom: {
        start: 0,
        type: "inside",
        maxValueSpan: 5,
        zoomLock: true,
      },
      customMapData: {}
    }
    state.flag = Math.random()
    state.loading = false
  }, [state.dateType, state.year, state.month], { wait: 500 });

  const getLabelAndValues = (dateType: DateType, year: string, month: string, price: number): [string[], number[], number[]] => {
    const labels: string[] = []
    const values: number[] = []
    const prices: number[] = []
    if (dateType === DateType.Year) {
      if (!waterStatistic[year]) {
        return [[], [], []]
      }
      Object.keys(waterStatistic[year])
        .sort((a, b) => Number(a) - Number(b))
        .forEach(month => {
          const monthStr = monthFormatShort(month)
          labels.push(`${monthStr}\n${year}`)
          const value = Number(Object.values(waterStatistic[year][month]).reduce<number>((p, c) => p + c, 0).toFixed(1))
          values.push(value)
          prices.push(value * price)
        })
    } else {
      if (!waterStatistic[year] || !waterStatistic[year][month]) {
        return [[], [], []]
      }
      Object.keys(waterStatistic[year][month])
        .sort((a, b) => Number(a) - Number(b))
        .forEach(day => {
          const dayOfWeek = dayjs(`${year}-${month}-${day}`).day()
          labels.push(`${day}\n${loopsText[dayOfWeek]}`)
          const value = Number(Number(waterStatistic[year][month][day]).toFixed(1))
          values.push(value)
          prices.push(value * price)
        })
    }
    return [labels, values, prices]
  }

  const styles = StyleSheet.create({
    listEmptyView: {
      alignItems: 'center',
    },
    listEmptyImage: {
      width: cx(200),
      height: cx(200),
    },
    listEmptyText: {
      flex: 0
    },
    downloadIcon: {
      width: cx(24),
      height: cx(24),
      tintColor: props.theme?.global.brand,
      position: 'absolute',
      right: 0,
      top: cx(10)
    }
  })

  return (
    <Page
      backText={I18n.getLang('water_consumption')}
      headlineText={I18n.getLang('controller')}
      headlineIcon={state.values.length ? res.download_icon : undefined}
      onHeadlineIconClick={() => {
        if (state.values.length === 0) {
          return
        }
        const headers = [I18n.getLang('date'), I18n.getLang('water_consumption'), `Price(${unitPrice.unit})`]
        const values = state.labels.map((item, index) => [item, state.values[index], state.prices[index]])
        exportCsvFile(headers, values, 'waterConsumption')
      }}
      loading={state.loading}
    >
      <View>
        <View style={{marginHorizontal: cx(24), flexDirection: 'row'}}>
          <DateTypeItem
            style={{flex: 1}}
            dateType={state.dateType}
            onDateTypeChange={(dateType) => {
              state.dateType = dateType;
            }}/>
          <DateSelectedItem
            style={{flex: 1, marginStart: cx(10), marginBottom: cx(15)}}
            dateType={state.dateType}
            date={`${state.year}-${state.month}`}
            onDateChange={(year, month) => {
              state.year = year
              state.month = month
            }}
          />
        </View>

        <View>
          {
            (state.values.length <= 0) ? (
                <View style={styles.listEmptyView}>
                  <Spacer height={cx(26)}/>
                  <Image
                    style={styles.listEmptyImage}
                    source={{uri: res.ldv_timer_empty}}/>
                  <Spacer height={cx(14)}/>
                  <InfoText
                    style={{width: 'auto', alignItems: 'center'}}
                    text={I18n.getLang('energyconsumption_emptydata')}
                    icon={res.ic_info}
                    textStyle={{flex: undefined}}
                  />
                </View>
              ) :
              (state.values.length > 0 && !state.loading && <ECharts
                  key={state.flag}
                  option={state.option}
                  ref={echarts}
                  height={cx(400)}
                  enableParseStringFunction={true}
              />)
          }
        </View>
      </View>
    </Page>
  )
};

export default withTheme(WaterChart)
