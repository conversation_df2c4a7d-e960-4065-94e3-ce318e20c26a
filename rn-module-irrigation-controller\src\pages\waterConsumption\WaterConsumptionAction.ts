import {useDp} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import {DpKeys} from "../../features/FeatureHooks";
import dayjs from "dayjs";
import CustomParseFormat from 'dayjs/plugin/customParseFormat'
import {getFeature, putFeature} from "@ledvance/base/src/api/native";
import {useCallback, useEffect, useState} from "react";
import {UnitList, UnitPrice} from "./UnitPriceModal";

dayjs.extend(CustomParseFormat)

export interface DataItem {
  label: string
  value: number
}

const waterKeys = [DpKeys.janWater, DpKeys.febWater, DpKeys.marWater, DpKeys.aprWater, DpKeys.mayWater, DpKeys.junWater, DpKeys.julWater, DpKeys.augWater, DpKeys.sepWater, DpKeys.octWater, DpKeys.novWater, DpKeys.decWater]

export function useWaterStatistic(): Record<string, Record<string, number>> {
  const statistic = {}
  waterKeys.forEach(key => {
    const [dpValue] = useDp<string, any>(getGlobalParamsDp(key))
    const daysValue = parseMonthlyWater(dpValue)
    if (daysValue) {
      const {year, month, ...rest} = daysValue
      if (statistic[year] === undefined) {
        statistic[year] = {}
      }
      if (statistic[year][month] === undefined) {
        statistic[year][month] = {}
      }

      statistic[year][month] = rest
    }
  })
  return statistic
}

export function useUnitPrice(devId: string): [UnitPrice, (v: UnitPrice) => Promise<void>] {
  const FeatureId = 'UnitPrice';
  const [value, setValue] = useState<UnitPrice>({
    price: '0',
    unit: UnitList[0]
  });

  useEffect(() => {
    // 获取特性数据并处理异常
    getFeature(devId, FeatureId).then(res => {
      if (res.result && res.data && typeof res.data === 'object') {
        setValue(res.data as UnitPrice); // 明确类型转换
      }
    })
  }, [devId, FeatureId]);

  const setFeature = useCallback(async (v: UnitPrice) => {
    const res = await putFeature(devId, FeatureId, v)
    if (res.result) {
      setValue(v)
    }
  }, [devId, FeatureId]); // 确保依赖变化时重新生成函数

  return [value, setFeature];
}

export function useFlowCount() {
  const [dpValue] = useDp<string, any>(getGlobalParamsDp(DpKeys.flowCount))
  return parseFlowCount(dpValue)
}

function parseMonthlyWater(monthly?: string) {
  if (!monthly) {
    return undefined
  }
  const buffer = new Uint8Array(64);
  for (let i = 0; i < 128; i++) {
    buffer[i] = parseInt(monthly.substring(i * 2, i * 2 + 2), 16)
  }
  const year = dayjs(buffer[0].toString().padStart(2, '0'), 'YY').format('YYYY')
  const month = buffer[1].toString().padStart(2, '0')
  const days = {
    year,
    month
  }
  const validLength = dayjs(`${year}-${month}`, 'YYYY-MM').daysInMonth() * 2 + 2
  for (let i = 2; i < validLength;) {
    const day = (i / 2).toString().padStart(2, '0')
    days[day] = ((buffer[i] << 8) | buffer[i + 1]) / 10
    i += 2
  }
  return days
}

function parseFlowCount(flowCount: string) {
  if (!flowCount) {
    return []
  }
  const buffer = new Uint8Array(20);
  for (let i = 0; i < 40; i++) {
    buffer[i] = parseInt(flowCount.substring(i * 2, i * 2 + 2), 16)
  }
  const today = dayjs()
  const values: DataItem[] = []
  for (let i = 0; i < 10; i++) {
    const value = ((buffer[i * 2] << 8) | buffer[i * 2 + 1]) / 10
    const label = today.add(-1 * (i + 1), 'day').format('DD/MM/YYYY')
    values.push({
      label,
      value
    })
  }
  return values
}

export function getValueByKeys<T>(
  waterStatistic: Record<string, Record<string, number>>,
  year: string,
  month?: string,
  day?: string
): T | undefined {
  const yearlyData = waterStatistic[year];
  if (!yearlyData) return undefined
  if (month) {
    const monthlyData = yearlyData[month]
    if (day) {
      return monthlyData ? monthlyData[day] as T : undefined
    }
    return monthlyData as unknown as T
  }
  return yearlyData as unknown as T
}
