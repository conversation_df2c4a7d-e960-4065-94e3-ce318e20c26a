import React, {useEffect, useState} from 'react'
import {Utils} from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import {useDeviceId, useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {useReactive} from "ahooks";
import {ScrollView, StyleSheet, Text, TouchableOpacity, View, Image} from "react-native";
import ThemeType from '@ledvance/base/src/config/themeType'
import Card from "@ledvance/base/src/components/Card";
import Spacer from "@ledvance/base/src/components/Spacer";
import {DataItem, getValueByKeys, useUnitPrice, useWaterStatistic} from "./WaterConsumptionAction";
import dayjs from "dayjs";
import {flatMap} from "lodash";
import OverView from "./Overview";
import {exchangeNumber, localeNumber, monthFormat} from "@ledvance/base/src/utils/common";
import {useNavigation} from '@react-navigation/core';
import {RouterKey} from "../../navigation/Router";
import I18n from "@ledvance/base/src/i18n";
import res from '@ledvance/base/src/res';
import UnitPricePopup from './UnitPriceModal';

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

const WaterConsumptionPage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const deviceId = useDeviceId()
  const navigation = useNavigation();
  const waterStatistic = useWaterStatistic()
  const [unitPrice, setUnitPrice] = useUnitPrice(deviceId)
  const [annualOverview, setAnnualOverview] = useState<DataItem[]>([]);

  const state = useReactive({
    type: 'Controller',
    daily: 0,
    monthly: 0,
    yearly: 0,
    showPopup: false,
  })

  useEffect(() => {
    const [year, month, day] = dayjs().format('YYYY-MM-DD').split("-")
    state.daily = getValueByKeys<number>(waterStatistic, year, month, day) || 0
    const monthValues = getValueByKeys<Record<string, number>>(waterStatistic, year, month)
    state.monthly = monthValues && Object.values(monthValues).reduce((p, c) => p + c, 0) || 0
    const yearValues = getValueByKeys<Record<string, Record<string, number>>>(waterStatistic, year)
    state.yearly = yearValues && flatMap(Object.values(yearValues), item => Object.values(item)).reduce((p, c) => p + c, 0) || 0
    parseAnnualOverview()
  }, [JSON.stringify(waterStatistic)])

  const parseAnnualOverview = () => {
    const arr: DataItem[] = []
    Object.keys(waterStatistic)
      .sort((a, b) => Number(b) - Number(a))
      .forEach(year => {
        const yearly = getValueByKeys<Record<string, Record<string, number>>>(waterStatistic, year)
        yearly && Object.keys(yearly)
          .sort((a, b) => Number(b) - Number(a))
          .forEach(month => {
            const label = `${monthFormat(month)} ${year}`
            const monthValues = getValueByKeys<Record<string, number>>(waterStatistic, year, month)
            const value = monthValues && Number((Object.values(monthValues).reduce((p, c) => p + c, 0)).toFixed(1)) || 0
            if (value > 0) {
              arr.push({
                label,
                value
              })
            }
          })
      })
    setAnnualOverview(arr)
  }

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      marginTop: cx(10)
    },
    title: {
      marginStart: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    water: {
      flexDirection: 'row',
      marginStart: cx(16),
      alignItems: 'baseline'
    },
    waterValue: {
      color: props.theme?.global.brand,
      fontSize: cx(30),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    waterUnit: {
      color: props.theme?.global.brand,
      fontSize: cx(20),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginStart: cx(16),
    },
    priceBg: {
      height: cx(40),
      width: cx(40),
      borderRadius: cx(40),
      backgroundColor: props.theme?.global.thirdBrand,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: cx(10),
    },
    priceNum: {
      marginRight: cx(10),
    },
    priceButton: {
      borderRadius: cx(5),
      paddingHorizontal: cx(10),
      paddingVertical: cx(8),
      marginTop: cx(8),
      backgroundColor: props.theme?.button.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    unitItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: cx(10),
      alignItems: 'center',
      height: cx(40),
    },
  })

  return (
    <Page
      backText={devInfo.name}
      headlineText={I18n.getLang('water_consumption')}
    >
      <ScrollView>
        {
          [
            {title: I18n.getLang('daily'), value: Number(state.daily.toFixed(1))},
            {title: monthFormat(dayjs().month() + 1), value: Number(state.monthly.toFixed(1))},
            {title: dayjs().year(), value: Number(state.yearly.toFixed(1))}
          ].map((item, index) => (
            <Card key={item.title} style={styles.card}>
              <Text style={styles.title}>{item.title}</Text>
              <View style={styles.water}>
                <Text style={styles.waterValue}>{item.value}</Text>
                <Text style={styles.waterUnit}> L</Text>
              </View>
              {
                index === 2 && (
                  <View style={styles.priceContainer}>
                    <Spacer/>
                    <View style={styles.priceBg}>
                      <Image
                        source={{ uri: res.energy_consumption_cash}}
                        resizeMode="contain"
                        style={{ height: cx(20), width: cx(20), tintColor: props.theme?.button.primary }}
                      />
                    </View>
                    <View>
                      <View style={styles.priceNum}>
                        <Text style={{ color: props.theme?.global.secondFontColor }}>
                          {I18n.getLang('consumption_data_field3_value_text2')}
                        </Text>
                        <Text style={{ color: props.theme?.global.fontColor, fontWeight: 'bold' }}>
                          {unitPrice
                            ? `${localeNumber(Number(unitPrice.price) * Number(item.value || '0'), 2)} ${unitPrice.unit}`
                            : '-'}
                        </Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => {
                          state.showPopup = true;
                        }}
                      >
                        <View style={styles.priceButton}>
                          <Text style={{ color: props.theme?.button.fontColor }}>
                            {I18n.getLang('consumption_data_field4_button_text')}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                )
              }
              <Spacer/>
            </Card>
          ))
        }
        <Spacer/>
        <OverView style={{marginHorizontal: cx(24)}}
                  headlineText={I18n.getLang('consumption_data_field4_headline_text')} overViewList={annualOverview}
                  headlineClick={() => navigation.navigate(RouterKey.water_chart, { unitPrice })}/>
        <Spacer/>
        <UnitPricePopup
          visible={state.showPopup}
          popupType={'money'}
          title={''}
          cancelText={I18n.getLang('auto_scan_system_cancel')}
          confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
          unitPrice={unitPrice}
          onConfirm={unitPrice => {
            state.showPopup = false;
            if (unitPrice) {
              setUnitPrice({
                price: exchangeNumber(unitPrice.price),
                unit: unitPrice.unit,
              }).then()
            }
          }}
          onCancel={() => {
            state.showPopup = false;
          }}
        />
      </ScrollView>
    </Page>
  )
}

export default withTheme(WaterConsumptionPage)
