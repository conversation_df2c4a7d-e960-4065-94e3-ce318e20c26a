import {useDp,useDeviceId,useTimeSchedule,useFlagMode} from '@ledvance/base/src/models/modules/NativePropsSlice';
import { NativeApi } from '@ledvance/base/src/api/native';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { useUpdateEffect, useReactive } from 'ahooks';
import { useCallback, useMemo, useEffect, useState } from 'react';
import { SupportUtils, nToHS, parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import I18n from '@ledvance/base/src/i18n';
import { RouterKey } from '../navigation/Router';
import {MoodInfo, MoodPageParams } from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface';
import { getHSVByHex, getHexByHSV } from '@ledvance/base/src/utils';
import {AdvancedData,AdvancedStatus,getAdvancedStatusColor} from '@ledvance/base/src/components/AdvanceCard';
import { BiorhythmPageParams } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmPage';
import { SleepWakeUpPageRouteParams } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpPage';
import { RandomTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage'
import { FixedTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimePage'
import { createParams } from '@ledvance/base/src/hooks/Hooks';
import { LightBehaviorPageParams } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/LightBehaviorPage';
import { usePowerOffMemory } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions';
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { DeviceStateType, DeviceType, MixLightData } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { MixLightBean, mixDp2Obj, mixObj2Dp } from './ProtocolConvert';
import { Buffer } from 'buffer';
import { MixDp2Obj, MixObj2Dp, dp2Obj, obj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import { cloneDeep } from 'lodash';
import { RemoteControlPageParams } from '@ledvance/ui-biz-bundle/src/newModules/remoteControl/RemoteControlPage';
import { useRemoteControl } from '@ledvance/ui-biz-bundle/src/newModules/remoteControl/RemoteControlActions';
import { useRandomTime } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions';
import { useFixedTime } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimeActions';
import { useSleepMode, useWakeUp } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpActions';
import { useBiorhythm } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions';
import { getFlagMode, saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions';
import { FlagPageProps } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import res from '@ledvance/base/src/res';
import { Formatter } from '@tuya/tuya-panel-lamp-sdk';
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import { ApplyForItem } from '@ledvance/base/src/utils/interface';

const { ControlDataFormatter } = Formatter
const control = new ControlDataFormatter()

export const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  temp_value: { key: 'temp_value', code: '23' },
  colour_data: { key: 'colour_data', code: '24' },
  scene_data: { key: 'scene_data', code: '25' },
  countdown: { key: 'countdown', code: '26' },
  music_data: { key: 'music_data', code: '27' },
  control_data: { key: 'control_data', code: '28' },
  rhythm_mode: { key: 'rhythm_mode', code: '30' },
  sleep_mode: { key: 'sleep_mode', code: '31' },
  wakeup_mode: { key: 'wakeup_mode', code: '32' },
  power_memory: { key: 'power_memory', code: '33' },
  do_not_disturb: { key: 'do_not_disturb', code: '34' },
  mix_light_scene: { key: 'mix_light_scene', code: '36' },
  remote_switch: { key: 'remote_switch', code: '41' },
  mix_rgbcw: { key: 'mix_rgbcw', code: '51' },
  cycle_timing: { key: 'cycle_timing', code: '209' },
  random_timing: { key: 'random_timing', code: '210' },
};

const lightApplyFor: ApplyForItem[] = [
  {
    type: 'light',
    name: I18n.getLang('Onoff_button_socket'),
    key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
    dp: dpKC.switch_led.code,
    enable: true,
  },
];

const mixApplyFor: ApplyForItem[] = [
  {
    type: 'mainLight',
    key: I18n.getLang('light_sources_tile_main_lighting_headline'),
    dp: 'white',
    enable: true,
  },
  {
    type: 'secondaryLight',
    key: I18n.getLang('light_sources_tile_sec_lighting_headline'),
    dp: 'color',
    enable: true,
  },
];

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.switch_led.code);
}

export enum WorkMode {
  White = 'white',
  Colour = 'colour',
  Scene = 'scene',
  Music = 'music',
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(dpKC.work_mode.code)
  const [workMode, setWokMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWokMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWokMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

type HSV = {
  h: number;
  s: number;
  v: number;
};

export const getColorData = (str: string) => {
  const h = str.substring(0, 4);
  const s = str.substring(4, 6);
  const v = str.substring(6, 8);
  const brightness = str.substring(8, 10);
  const temperature = str.substring(10, 12);
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16),
    v: parseInt(v, 16),
    brightness: parseInt(brightness, 16),
    temperature: parseInt(temperature, 16),
  };
};

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80));
};

export function useColorData(): [HSV, (h: number, s: number, v: number) => Promise<Result<any>>] {
  const [color, setColor] = useDp(dpKC.colour_data.code);
  const hsv = useMemo(() => {
    if (!color)
      return {
        h: 360,
        s: 100,
        v: 100,
      };
    const hsvData = getHSVByHex(color);
    return {
      h: hsvData.h,
      s: Math.round(hsvData.s / 10),
      v: Math.round(hsvData.v / 10),
    };
  }, [color]);

  const setColorFn = (h: number, s: number, v: number) => {
    return setColor(
      getHexByHSV({
        h,
        s: s * 10,
        v: v * 10,
      })
    );
  };

  return [hsv, setColorFn];
}

export function useMixRgbcw(): [MixLightBean, (mix: MixLightBean) => Promise<Result<any>>] {
  const [mixDp, setMixDp] = useDp<string, any>(dpKC.mix_rgbcw.code);
  const [mix, setMixLightState] = useState(mixDp2Obj(mixDp));

  useUpdateEffect(() => {
    setMixLightState(mixDp2Obj(mixDp));
  }, [mixDp]);
  const setMixDpFn = (mix: MixLightBean) => {
    return setMixDp(mixObj2Dp(mix));
  };
  return [mix, setMixDpFn];
}

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(
    dpKC.bright_value.code
  );
  const setBrightFn = (v: number) => {
    return setBright(v * 10);
  };
  return [Math.round((bright ?? 0) / 10), setBrightFn];
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue]: [number, (v: number) => Promise<Result<any>>] = useDp(
    dpKC.temp_value.code
  );
  const setTempValueFn = (v: number) => {
    return setTempValue(v * 10);
  };
  return [Math.round((tempValue ?? 0) / 10), setTempValueFn];
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code);
}

interface ControlData {
  h: number;
  s: number;
  v: number;
  brightness: number;
  temperature: number
}

export function putControlData(): (isColorMode: boolean, value: ControlData, isMixLight?: boolean) => Promise<any> {
  const [, setControlData] = useDp(dpKC.control_data.code)
  return async (isColorMode: boolean, value: ControlData, isMixLight?: boolean) => {
    const v = control.format({
      mode: 1,
      hue: (isColorMode || isMixLight) ? value.h : 0,
      saturation: (isColorMode || isMixLight) ? Math.round(value.s * 10) : 0,
      value: (isColorMode || isMixLight) ? Math.round(value.v * 10) : 0,
      brightness: (!isColorMode || isMixLight) ? Math.round(value.brightness * 10) : 0,
      temperature: (!isColorMode || isMixLight) ? Math.round(value.temperature * 10) : 0,
    })
    await setControlData(v)
  }
}

export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.bright_value.key);
}

export function isSupportTemperature(): boolean {
  return SupportUtils.isSupportDp(dpKC.temp_value.key);
}

export function isSupportColor(): boolean {
  return SupportUtils.isSupportDp(dpKC.colour_data.key);
}

export function isSupportMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.music_data.key);
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key);
}

export function isSupportMood(): boolean {
  return (
    SupportUtils.isSupportDp(dpKC.scene_data.key) ||
    SupportUtils.isSupportDp(dpKC.mix_light_scene.key)
  );
}

export function isSupportMixLight(): boolean {
  return SupportUtils.isSupportDp(dpKC.mix_rgbcw.key);
}

export function isSupportRhythm() {
  return SupportUtils.isSupportDp(dpKC.rhythm_mode.key);
}

export function isSupportSleepWakeUp() {
  return (
    SupportUtils.isSupportDp(dpKC.sleep_mode.key) || SupportUtils.isSupportDp(dpKC.wakeup_mode.key)
  );
}

export function isSupportRandomTime() {
  return SupportUtils.isSupportDp(dpKC.random_timing.key);
}

export function isSupportFixedTime() {
  return SupportUtils.isSupportDp(dpKC.cycle_timing.key);
}

export function isSupportPowerBehavior() {
  return SupportUtils.isSupportDp(dpKC.power_memory.key);
}

export function isSupportDoNotDisturb() {
  return SupportUtils.isSupportDp(dpKC.do_not_disturb.key);
}

export function isSupportRemoteControl() {
  return SupportUtils.isSupportDp(dpKC.remote_switch.key);
}

export function useAdvancedData(isSuspend: boolean): AdvancedData[] {
  const advanceData: AdvancedData[] = [];
  const deviceId = useDeviceId();
  const [workMode] = useWorkMode();
  const [switchLed] = useSwitch();
  const [countdown] = useCountDowns();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [powerMemory] = usePowerOffMemory(getGlobalParamsDp('power_memory'));
  const [remoteControl] = useRemoteControl(getGlobalParamsDp('remote_switch'));
  const [randomTimeList] = useRandomTime(getGlobalParamsDp('random_timing'), false, true);
  const [fixedTimeList] = useFixedTime(getGlobalParamsDp('cycle_timing'), false, true);
  const [sleepList] = useSleepMode(getGlobalParamsDp('sleep_mode'), true);
  const [wakeUpList] = useWakeUp(getGlobalParamsDp('wakeup_mode'), true);
  const [biorhythm] = useBiorhythm(getGlobalParamsDp('rhythm_mode'), true);
  const [flagMode, setFlagMode] = useFlagMode();
  const [mix] = useMixRgbcw()
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    randomTimeStatus: AdvancedStatus.Disable,
    fixedTimeStatus: AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable,
    rhythmModeStatus: AdvancedStatus.Disable,
  });
  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });

      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          setFlagMode(parseJSON(res.data));
        }
      });
    }
  }, [deviceId]);

  useEffect(() => {
    if (biorhythm.enable) {
      state.rhythmModeStatus = isSuspend ? AdvancedStatus.Suspend : AdvancedStatus.Enable
    } else {
      state.rhythmModeStatus = AdvancedStatus.Disable
    }
  }, [isSuspend, biorhythm.enable])

  useUpdateEffect(() => {
    if (workMode !== WorkMode.Scene && flagMode?.flagMode) {
      setFlagMode({
        flagMode: false,
        flagId: undefined,
      });
      saveFlagMode(
        deviceId,
        JSON.stringify({
          flagMode: false,
          flagId: undefined,
        })
      ).then();
    }
    if (workMode !== WorkMode.Music){
      MusicManager.close()
    }
  }, [workMode]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  useEffect(() => {
    state.randomTimeStatus = randomTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [randomTimeList]);

  useEffect(() => {
    state.fixedTimeStatus = fixedTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [fixedTimeList]);

  useEffect(() => {
    const sleepWakeUp = [...sleepList, ...wakeUpList];
    state.sleepWakeUpStatus = sleepWakeUp.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [sleepList, wakeUpList]);


  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: isSupportMixLight() ? DeviceType.MixLight : DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: isSupportColor() ? true : false,
        },
      },
      isManual: !(
        dps.hasOwnProperty(getGlobalParamsDp('scene_data')) || dps.hasOwnProperty(getGlobalParamsDp('mix_light_scene'))
      ),
      mood: undefined,
    };

    if (isSupportMixLight()) {
      if (dps.hasOwnProperty(dpKC.mix_rgbcw.code)) {
        const mix = mixDp2Obj(Buffer.from(dps[dpKC.mix_rgbcw.code], 'base64').toString('hex'));
        deviceState.deviceData.deviceData = {
          ...mix,
          s: Math.round(mix.s),
          v: Math.round(mix.v),
          brightness: Math.round(mix.brightness),
          temperature: Math.round(mix.temperature),
          isColorMode: true,
        };
      }
      if (dps.hasOwnProperty(dpKC.mix_light_scene.code)) {
        const mood = MixDp2Obj(
          Buffer.from(dps[dpKC.mix_light_scene.code], 'base64').toString('hex')
        );
        deviceState.mood = cloneDeep(mood);
      }
    } else {
      if (dps.hasOwnProperty(dpKC.bright_value.code)) {
        deviceState.deviceData.deviceData.brightness = Math.round(dps[dpKC.bright_value.code] / 10);
        deviceState.deviceData.deviceData.isColorMode = false;
      }
      if (dps.hasOwnProperty(dpKC.temp_value.code)) {
        deviceState.deviceData.deviceData.temperature = Math.round(dps[dpKC.temp_value.code] / 10);
        deviceState.deviceData.deviceData.isColorMode = false;
      }
      if (dps.hasOwnProperty(dpKC.colour_data.code)) {
        const { h, s, v } = getHSVByHex(dps[dpKC.colour_data.code]);
        deviceState.deviceData.deviceData.h = Math.round(h);
        deviceState.deviceData.deviceData.s = Math.round(s / 10);
        deviceState.deviceData.deviceData.v = Math.round(v / 10);
        deviceState.deviceData.deviceData.isColorMode = true;
      }
      if (dps.hasOwnProperty(dpKC.scene_data.code)) {
        const mood = dp2Obj(dps[dpKC.scene_data.code]);
        deviceState.mood = cloneDeep(mood);
      }
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (deviceData.type === DeviceType.MixLight) {
        if (!isManual && mood) {
          manualDps[dpKC.mix_light_scene.code] = Buffer.from(MixObj2Dp(mood as MoodInfo), 'hex').toString('base64');
          manualDps[dpKC.work_mode.code] = WorkMode.Scene;
        } else {
          const mixDeviceData: MixLightData = {
            ...deviceData.deviceData,
            colorLightSwitch: applyForList[1].enable,
            whiteLightSwitch: applyForList[0].enable,
            mixRgbcwEnabled: true,
          };
          manualDps[dpKC.mix_rgbcw.code] = Buffer.from(mixObj2Dp(mixDeviceData), 'hex').toString(
            'base64'
          );
        }
        manualDps[dpKC.switch_led.code] = true;
      } else {
        if (!isManual && mood) {
          manualDps[dpKC.scene_data.code] = obj2Dp(mood as MoodInfo);
          manualDps[dpKC.switch_led.code] = true;
          manualDps[dpKC.work_mode.code] = WorkMode.Scene;
        } else {
          applyForList.forEach(apply => {
            manualDps[apply.dp] = apply.enable;
          });
          if (manualDps[dpKC.switch_led.code]) {
            if (deviceData.deviceData.isColorMode) {
              if (isSupportColor()) {
                manualDps[dpKC.colour_data.code] = getHexByHSV({
                  h: deviceData.deviceData.h,
                  s: deviceData.deviceData.s * 10,
                  v: deviceData.deviceData.v * 10,
                });
                manualDps[dpKC.work_mode.code] = WorkMode.Colour;
              }
            } else {
              if (isSupportBrightness()) {
                manualDps[dpKC.bright_value.code] = deviceData.deviceData.brightness * 10;
              }
              if (isSupportTemperature()) {
                manualDps[dpKC.temp_value.code] = deviceData.deviceData.temperature * 10;
              }
              manualDps[dpKC.work_mode.code] = WorkMode.White;
            }
          }
        }
      }
      return manualDps;
    },
    []
  );

  const flagParams = createParams<FlagPageProps>({
    sceneDataCode: isSupportMixLight() ? dpKC.mix_light_scene.code : dpKC.scene_data.code,
    workModeCode: dpKC.work_mode.code,
    isSupportColor: true,
    switchLedCode: dpKC.switch_led.code,
    isSupportMixScene: isSupportMixLight(),
    isSupportBrightness: isSupportBrightness(),
    isSupportTemperature: isSupportTemperature()
  });
  if (isSupportColor() && isSupportMood()) {
    advanceData.push({
      title: I18n.getLang('Feature_devicepanel_flags'),
      dp: { key: 'flag', code: 'flag' },
      icons: res.flag_icon,
      statusColor: getAdvancedStatusColor(
        flagMode?.flagMode && switchLed && (isSupportMixLight() ? !mix.mixRgbcwEnabled : true) ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      router: {
        key: RouterKey.ui_biz_flag_page,
        params: flagParams,
      },
    });
  }

  if (isSupportMood()) {
    const params = createParams<MoodPageParams>({
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      switchLedDp: getGlobalParamsDp('switch_led'),
      mainDp: isSupportMixLight() ? getGlobalParamsDp('mix_light_scene') : getGlobalParamsDp('scene_data'),
      mainWorkMode: getGlobalParamsDp('work_mode'),
      mainSwitch: getGlobalParamsDp('switch_led'),
      isMixLight: isSupportMixLight(),
    });
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(
        !flagMode?.flagMode && workMode === WorkMode.Scene && switchLed && (isSupportMixLight() ? !mix.mixRgbcwEnabled : true) ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: isSupportMixLight() ? dpKC.mix_light_scene : dpKC.scene_data,
      router: {
        key: RouterKey.ui_biz_mood,
        params,
      },
    });
  }

  if (isSupportRhythm()) {
    const params = createParams<BiorhythmPageParams>({
      biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        randomTimeDpCode: getGlobalParamsDp('random_timing'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      isMixLight: isSupportMixLight(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
    })
    advanceData.push({
      title: I18n.getLang('add_new_trigger_time_system_back_text'),
      statusColor: getAdvancedStatusColor(state.rhythmModeStatus),
      subtitles: state.rhythmModeStatus === AdvancedStatus.Suspend
          ? [I18n.getLang('light_sources_feature_4_text_name')]
          : [],
      dp: { key: 'rhythm_mode', code: getGlobalParamsDp('rhythm_mode') },
      router: {
        key: RouterKey.bi_biz_biological,
        params
      },
    });
  }

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: isSupportMixLight() ? mixApplyFor : lightApplyFor,
        applyForDisabled: true,
        isSupportMood: isSupportMood(),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: isSupportColor(),
        isSupportBrightness: isSupportBrightness(),
        isSupportTemperature: isSupportTemperature(),
        isMixLight: isSupportMixLight(),
      } as TimeSchedulePageParams,
    },
  });

  if (isSupportMusic()) {
    const params = createParams({
      switch_led: dpKC.switch_led.code,
      work_mode: dpKC.work_mode.code,
      mix_rgbcw: dpKC.mix_rgbcw.code,
      mix_light_scene: dpKC.mix_light_scene.code,
      music_data: dpKC.music_data.code,
      isMixRGBWLamp: isSupportMixLight(),
    });

    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(
        workMode === WorkMode.Music && switchLed && (isSupportMixLight() ? mix.mixRgbcwEnabled : true) ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: dpKC.music_data,
      router: {
        key: RouterKey.ui_biz_music,
        params,
      },
    });
  }

  if (isSupportTimer()) {
    const params = useMemo(() =>{
      return {
        dps: [
          {
            label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
            value: 'lighting',
            dpId: getGlobalParamsDp('countdown'),
            enableDp: getGlobalParamsDp('switch_led'),
            cloudKey: 'lightingInfo',
            stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
            stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
          },
        ],
      }
    }, [])
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles:
        countdown > 0
          ? [
            I18n.formatValue(
              switchLed
                ? 'ceiling_fan_feature_2_light_text_min_off'
                : 'ceiling_fan_feature_2_light_text_min_on',
              timeFormat(countdown, true)
            ),
          ]
          : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  if (isSupportFixedTime()) {
    const params = createParams<FixedTimePageParams>({
      fixedTimeDpCode: dpKC.cycle_timing.code,
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_timing'),
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      applyForList: lightApplyFor,
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
    });

    advanceData.push({
      title: I18n.getLang('fixedTimeCycle_socket_headline'),
      statusColor: getAdvancedStatusColor(state.fixedTimeStatus),
      dp: dpKC.sleep_mode,
      router: {
        key: RouterKey.ui_biz_fixed_time_new,
        params,
      },
    });
  }

  if (isSupportRandomTime()) {
    const params = createParams<RandomTimePageParams>({
      randomTimeDpCode: getGlobalParamsDp('random_timing'),
      conflictDps: {
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      applyForList: lightApplyFor,
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
    });

    advanceData.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      statusColor: getAdvancedStatusColor(state.randomTimeStatus),
      dp: {key: 'random_timing', code: getGlobalParamsDp('random_timing')},
      router: {
        key: RouterKey.ui_biz_random_time_new,
        params,
      },
    });
  }

  if (isSupportSleepWakeUp()) {
    const params = createParams<SleepWakeUpPageRouteParams>({
      wakeUpDpCode: dpKC.wakeup_mode.code,
      sleepDpCode: dpKC.sleep_mode.code,
      conflictDps: {
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        randomTimeDpCode: getGlobalParamsDp('random_timing')
      },
      isMixLight: isSupportMixLight(),
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      applyForList: isSupportMixLight() ? mixApplyFor : lightApplyFor,
    });
    advanceData.push({
      title: I18n.getLang('add_sleepschedule_one_source_system_back_text'),
      statusColor: getAdvancedStatusColor(state.sleepWakeUpStatus),
      dp: { key: 'sleep_mode', code: getGlobalParamsDp('sleep_mode')},
      router: {
        key: RouterKey.ui_biz_sleep_wakeUp_new,
        params,
      },
    });
  }

  if (isSupportPowerBehavior()) {
    const params = createParams<LightBehaviorPageParams>({
      memoryDpCode: dpKC.power_memory.code,
      disturbDpCode: dpKC.do_not_disturb.code,
      isSupportColor: isSupportColor(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      isSupportDoNotDisturb: isSupportDoNotDisturb(),
    });

    advanceData.push({
      title: I18n.getLang('light_sources_specific_settings_power_off'),
      statusColor: getAdvancedStatusColor(
        powerMemory ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'power_memory', code: getGlobalParamsDp('power_memory')},
      router: {
        key: RouterKey.ui_biz_power_behavior,
        params,
      },
    });
  }

  if (isSupportRemoteControl()) {
    const params = createParams<RemoteControlPageParams>({
      remoteControlDpCode: getGlobalParamsDp('remote_switch'),
    });
    advanceData.push({
      title: I18n.getLang('light_sources_specific_settings_remote_control'),
      statusColor: getAdvancedStatusColor(
        remoteControl ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'remote_switch', code: getGlobalParamsDp('remote_switch')},
      router: {
        key: RouterKey.ui_biz_remote_control,
        params,
      },
    });
  }

  return advanceData;
}
