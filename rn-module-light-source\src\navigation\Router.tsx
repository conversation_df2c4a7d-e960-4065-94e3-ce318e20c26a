import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import MusicPageRouters from '@ledvance/ui-biz-bundle/src/modules/music/Router'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import BiologicalRouters from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/Router'
import MoodPageRouters from '@ledvance/ui-biz-bundle/src/newModules/mood/Router'
import SelectPagePageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'
import SleepWakeUpPageRouters from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/Router'
import { LightPowerOnBehaviorPageRouters } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/Router'
import RandomTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/randomTime/Router'
import FixedTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/Router'
import RemoteControlPageRouters from '@ledvance/ui-biz-bundle/src/newModules/remoteControl/Router'
import FlagPageRouters from '@ledvance/ui-biz-bundle/src/modules/flags/Router'

export const RouterKey = {
  main: 'main',
  ...ui_biz_routerKey,
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...MoodPageRouters,
  ...TimeSchedulePageRouters,
  ...TimerPageRouters,
  ...BiologicalRouters,
  ...SleepWakeUpPageRouters,
  ...MusicPageRouters,
  ...SelectPagePageRouters,
  ...LightPowerOnBehaviorPageRouters,
  ...RandomTimePageRouters,
  ...FixedTimePageRouters,
  ...RemoteControlPageRouters,
  ...FlagPageRouters,
]