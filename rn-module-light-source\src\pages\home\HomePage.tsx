import React, { useCallback } from 'react';
import { ScrollView, View } from 'react-native';
import { useReactive } from 'ahooks';
import Page from '@ledvance/base/src/components/Page';
import { useDeviceId, useDeviceInfo, useFamilyName, useFlagMode } from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import { NativeApi } from '@ledvance/base/src/api/native';
import {
  isSupportMixLight,
  useAdvancedData,
  useCountDowns,
} from '../../hooks/FeatureHooks';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import LightHome from './LightHome';
import MixHome from './MixHome';
import { saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import { useRhythmSuspend } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions';



const HomePage = () => {
  const devInfo = useDeviceInfo();
  const devId = useDeviceId()
  const familyName = useFamilyName();
  const [flagMode, setFlagMode] = useFlagMode();
  const [countDown, setCountDown] = useCountDowns()
  const [isSuspend, getSuspendTime, setSuspendTime] = useRhythmSuspend(getGlobalParamsDp('rhythm_mode'))
  const advanceData = useAdvancedData(isSuspend);
  const state = useReactive({
    loading: false,
  });

  const closeFlagMode = useCallback(() =>{
    if (flagMode?.flagMode) {
      setFlagMode({
        ...flagMode,
        flagMode: false,
      });
      saveFlagMode(
        devId,
        JSON.stringify({
          ...flagMode,
          flagMode: false,
        })
      );
    }
  }, [flagMode])

  const closeCountDown = useCallback(() =>{
    if(countDown){
      setCountDown(0).then()
    }
  }, [countDown])

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          {isSupportMixLight() ? 
          <MixHome 
            closeFlagMode={closeFlagMode}
            closeCountDown={closeCountDown}
            getSuspendTime={getSuspendTime}
            setSuspendTime={setSuspendTime}
          /> : 
          <LightHome 
            closeFlagMode={closeFlagMode}
            closeCountDown={closeCountDown}
            getSuspendTime={getSuspendTime}
            setSuspendTime={setSuspendTime}
          />}
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  );
};

export default HomePage;
