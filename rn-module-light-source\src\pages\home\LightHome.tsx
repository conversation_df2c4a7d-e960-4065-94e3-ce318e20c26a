import Card from '@ledvance/base/src/components/Card';
import LampAdjustView from '@ledvance/base/src/components/LampAdjustView';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import I18n from '@ledvance/base/src/i18n';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils';
import { useReactive, useThrottleFn, useUpdateEffect } from 'ahooks';
import dayjs from 'dayjs';
import {
  isSupportBrightness,
  isSupportColor,
  isSupportTemperature,
  putControlData,
  useBrightness,
  useColorData,
  useSwitch,
  useTemperature,
  useWorkMode,
  WorkMode,
  dpKC,
} from 'hooks/FeatureHooks';
import React, { useCallback, useEffect } from 'react';
import { Utils } from 'tuya-panel-kit';
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { NativeApi, sendAppEvent } from '@ledvance/base/src/api/native'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks'

const { convertX: cx } = Utils.RatioUtils;

interface LightHomeProps {
  closeCountDown: () => void
  closeFlagMode: () => void
  getSuspendTime: () => void
  setSuspendTime: (v: string | undefined) => Promise<Result<any>>
}

const LightHome = (props: LightHomeProps) => {
  const [switchLed, setSwitchLed] = useSwitch();
  const [workMode, setWorkMode] = useWorkMode();
  const [brightness, setBrightness] = useBrightness();
  const [temperature, setTemperature] = useTemperature();
  const [hsv, setHsv] = useColorData();
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const setControlData = putControlData()
  
  // 使用 dp 响应时间校验 hook
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator(1000);
  
  const state = useReactive({
    switchLed,
    ...hsv,
    brightness,
    temperature,
    workMode,
    flag: Symbol(),
    control: Symbol(),
    suspendFlag: Symbol(),
    showLoading: false, // 用于显示加载状态
  });



  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useEffect(() => {
    const shouldBlock = onDpResponse(dpKC.switch_led.code);
    if (!shouldBlock) {
      state.switchLed = switchLed;
    }
  }, [switchLed, onDpResponse]);

  useUpdateEffect(() => {
    const shouldBlockWorkMode = onDpResponse(dpKC.work_mode.code);
    if (!shouldBlockWorkMode) {
      state.workMode = workMode;
    }
    
    const shouldBlockBrightness = onDpResponse(dpKC.bright_value.code);
    if (!shouldBlockBrightness) {
      state.brightness = brightness;
    }
    
    const shouldBlockTemperature = onDpResponse(dpKC.temp_value.code);
    if (!shouldBlockTemperature) {
      state.temperature = temperature;
    }
  }, [workMode, brightness, temperature, onDpResponse]);

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(dpKC.colour_data.code);
    if (!shouldBlock) {
      console.log(hsv.h, hsv.s, hsv.v, shouldBlock,'hsv.h, hsv.s, hsv.v')
      state.h = hsv.h;
      state.s = hsv.s;
      state.v = hsv.v;
    }
  }, [hsv, onDpResponse]);

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      NativeApi.log(`gestureSwitch: ${gestureSwitch}`)
      state.switchLed = gestureSwitch
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = workMode === WorkMode.Colour
    if (!isFocused || !isColorMode || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    runColour()
  }, [isFocused, workMode, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = workMode === WorkMode.Colour
    if (isColorMode) {
      state.v = gestureBrightness
      runColour()
    } else {
      state.brightness = gestureBrightness
      runBrightness()
    }
  }, [isFocused, workMode, gestureBrightness])

  const { run: runColour } = useThrottleFn(() => {
    if (state.switchLed) {
      state.flag = Symbol()
      setHsv(state.h, state.s, state.v).then()
    }
  }, { wait: 500 })
  const { run: runBrightness } = useThrottleFn(() => {
    if (state.switchLed) {
      state.flag = Symbol()
      setBrightness(state.brightness).then()
    }
  }, { wait: 500 })

  const getBlockColor = useCallback(() => {
    if (workMode === WorkMode.Colour) {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100));
      return hsv2Hex(state.h, s, 100);
    }
    return '#FEAC5B';
  }, [state.workMode, state.h, state.s, state.v]);

  useUpdateEffect(() => {
    props.closeFlagMode()
    props.setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
  }, [state.flag]);

  useUpdateEffect(() =>{
    props.closeCountDown()
  }, [state.switchLed])

  useUpdateEffect(() =>{
    run()
  }, [state.control])

  const putControlAction = () => {
    const isColorMode = workMode === WorkMode.Colour
    const v = {
      h: state.h,
      s: state.s,
      v: state.v,
      brightness: state.brightness,
      temperature: state.temperature,
    }
    if (state.switchLed) {
      setControlData(isColorMode, v).then()
    }
  }

  const { run } = useThrottleFn(putControlAction, { wait: 400 })

  useUpdateEffect(() =>{
    props.getSuspendTime()
  }, [workMode, JSON.stringify(hsv), brightness, switchLed])
  

  console.log(state.h, state.s, state.v, 'state.h, state.s, state.v')
  return (
    <Card style={{ marginVertical: cx(12), marginHorizontal: cx(24) }}>
      <LdvSwitch
        title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
        color={getBlockColor()}
        colorAlpha={1}
        enable={state.switchLed}
        setEnable={async (enable: boolean) => {
          state.switchLed = enable
          await sendDpWithTimestamps(dpKC.switch_led.code, () => setSwitchLed(enable));
          props.setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
        }}
      />
      {state.switchLed && (
        <LampAdjustView
          isSupportColor={isSupportColor()}
          isSupportTemperature={isSupportTemperature()}
          isSupportBrightness={isSupportBrightness()}
          isColorMode={workMode === WorkMode.Colour}
          reserveSV={true}
          minSaturation={1}
          setIsColorMode={async isColorMode => {
            state.flag = Symbol();
            const mode = isColorMode ? WorkMode.Colour : WorkMode.White;
            state.workMode = mode
            await sendDpWithTimestamps(dpKC.work_mode.code, () => setWorkMode(mode));
          }}
          h={state.h}
          s={state.s}
          v={state.v}
          onHSVChange={(h, s, v) => {
            state.h = h;
            state.s = s;
            state.v = v;
            state.control = Symbol();
          }}
          onHSVChangeComplete={async (h, s, v) => {
            state.flag = Symbol();
            state.h = h;
            state.s = s;
            state.v = v;
            sendDpWithTimestamps(dpKC.work_mode.code, () => setWorkMode(WorkMode.Colour));
            sendDpWithTimestamps(dpKC.colour_data.code, () => setHsv(h, s, v));
          }}
          colorTemp={state.temperature}
          brightness={state.brightness}
          onCCTChange={cct => {
            state.temperature = cct;
            state.control = Symbol();
          }}
          onCCTChangeComplete={async temperature => {
            state.flag = Symbol();
            state.temperature = temperature;
            sendDpWithTimestamps(dpKC.work_mode.code, () => setWorkMode(WorkMode.White));
            sendDpWithTimestamps(dpKC.temp_value.code, () => setTemperature(temperature));
          }}
          onBrightnessChange={brightness => {
            state.brightness = brightness;
            state.control = Symbol();
          }}
          onBrightnessChangeComplete={async brightness => {
            state.flag = Symbol();
            state.brightness = brightness;
            sendDpWithTimestamps(dpKC.work_mode.code, () => setWorkMode(WorkMode.White));
            sendDpWithTimestamps(dpKC.bright_value.code, () => setBrightness(brightness));
          }}
        />
      )}
    </Card>
  );
};

export default LightHome;
