import React, {useEffect} from 'react';
import Card from '@ledvance/base/src/components/Card';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import I18n from '@ledvance/base/src/i18n';
import { useReactive, useThrottleFn, useUpdateEffect } from 'ahooks';
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView';
import ColorAdjustView from '@ledvance/base/src/components/ColorAdjustView';
import { WorkMode, isSupportBrightness, isSupportTemperature, putControlData, useMixRgbcw, useSwitch, useWorkMode, dpKC } from 'hooks/FeatureHooks';
import { Utils } from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import { Result } from '@ledvance/base/src/models/modules/Result';
import dayjs from 'dayjs';
import ThemeType from '@ledvance/base/src/config/themeType'
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface MixHomeProps {
  theme?: ThemeType
  closeCountDown: () => void
  closeFlagMode: () => void
  getSuspendTime: () => void
  setSuspendTime: (v: string | undefined) => Promise<Result<any>>
}

const MixHome = (props: MixHomeProps) => {
  const [mix, setMix] = useMixRgbcw();
  const [switchLed, setSwitchLed] = useSwitch()
  const [workMode] = useWorkMode()
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const setControlData = putControlData()
  
  // 使用 dp 响应时间校验 hook
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator(1000);
  
  const state = useReactive({
    mixRgbcw: mix,
    flag: Symbol(),
    control: Symbol(),
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])



  useUpdateEffect(() => {
    // 监听 mix_rgbcw 变化，如果是 1000ms 内的响应则不更新 state
    const shouldBlock = onDpResponse(dpKC.mix_rgbcw.code);
    if (!shouldBlock) {
      state.mixRgbcw = mix;
    }
  }, [mix, onDpResponse]);


  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    if (!isFocused || !switchLed || !state.mixRgbcw.colorLightSwitch || gestureHue === undefined) {
      return
    }
    state.mixRgbcw.h = gestureHue
    runSend()
  }, [isFocused, switchLed, state.mixRgbcw.colorLightSwitch, gestureHue])

  useEffect(() => {
    if (!isFocused || !switchLed || gestureBrightness === undefined) {
      return
    }
    if (state.mixRgbcw.colorLightSwitch) {
      state.mixRgbcw.v = gestureBrightness
    }
    if (state.mixRgbcw.whiteLightSwitch) {
      state.mixRgbcw.brightness = gestureBrightness
    }
    runSend()
  }, [isFocused, switchLed, state.mixRgbcw.whiteLightSwitch, state.mixRgbcw.colorLightSwitch, gestureBrightness])

  useUpdateEffect(() =>{
    sendDpWithTimestamps(dpKC.mix_rgbcw.code, () => setMix({
      ...state.mixRgbcw,
      mixRgbcwEnabled: true
    })).then()
    props.closeFlagMode()
    props.setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
  }, [state.flag, sendDpWithTimestamps])

  useUpdateEffect(() =>{
    props.closeCountDown()
  }, [mix.colorLightSwitch, mix.whiteLightSwitch, switchLed])

  useUpdateEffect(() =>{
    run()
  }, [state.control])

  const putControlAction = () => {
    const isColorMode = workMode === WorkMode.Colour
    const v = {
      ...state.mixRgbcw,
      h: state.mixRgbcw.colorLightSwitch ? state.mixRgbcw.h : 0,
      s: state.mixRgbcw.colorLightSwitch ? state.mixRgbcw.s : 0,
      v: state.mixRgbcw.colorLightSwitch ? state.mixRgbcw.v : 0,
      brightness: state.mixRgbcw.whiteLightSwitch ? state.mixRgbcw.brightness : 0,
      temperature: state.mixRgbcw.whiteLightSwitch ? state.mixRgbcw.temperature : 0
    }
    setControlData(isColorMode, v, true).then()
  }

  const { run } = useThrottleFn(putControlAction, { wait: 350 })
  const { run: runSend } = useThrottleFn(() => {
    if (switchLed && (state.mixRgbcw.whiteLightSwitch || state.mixRgbcw.colorLightSwitch)) {
      state.flag = Symbol()
    }
  }, { wait: 500 })

  useUpdateEffect(() =>{
    props.getSuspendTime()
  }, [workMode, JSON.stringify(mix), switchLed])

  return (
    <>
      <Card style={{ marginHorizontal: cx(24), marginVertical: cx(12) }}>
        <LdvSwitch
          title={I18n.getLang('light_sources_tile_main_lighting_headline')}
          color={props.theme?.card.background}
          colorAlpha={1}
          enable={switchLed && state.mixRgbcw.whiteLightSwitch}
          setEnable={async v => {
            // if(workMode !== WorkMode.White && workMode !== WorkMode.Colour){
            //   await setWorkMode(WorkMode.White)
            // }
            if(v){
              if(!switchLed){
                state.mixRgbcw.colorLightSwitch = false
              }
              state.mixRgbcw.whiteLightSwitch = true
              sendDpWithTimestamps(dpKC.mix_rgbcw.code, () => setMix({
                ...state.mixRgbcw,
                mixRgbcwEnabled: true
              })).then()
              if (!switchLed) {
                setSwitchLed(true).then()
              }
            }else{
              if(!state.mixRgbcw.colorLightSwitch){
                setSwitchLed(false).then()
              }else{
                state.mixRgbcw.whiteLightSwitch = false
                sendDpWithTimestamps(dpKC.mix_rgbcw.code, () => setMix({
                  ...state.mixRgbcw,
                  mixRgbcwEnabled: true
                })).then()
              }
            }
            props.setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
          }}
        />
        {switchLed && state.mixRgbcw.whiteLightSwitch && (
          <>
            <ColorTempAdjustView
              isSupportBrightness={isSupportBrightness()}
              isSupportTemperature={isSupportTemperature()}
              colorTemp={state.mixRgbcw.temperature}
              brightness={state.mixRgbcw.brightness}
              onBrightnessChange={bright => {
                state.mixRgbcw.brightness = bright;
                state.control = Symbol()
              }}
              onBrightnessChangeComplete={async bright => {
                state.mixRgbcw.brightness = bright
                state.flag = Symbol()
                // if(workMode !== WorkMode.White && workMode !== WorkMode.Colour){
                //   await setWorkMode(WorkMode.White)
                // }
              }}
              onCCTChange={cct => {
                state.mixRgbcw.temperature = cct;
                state.control = Symbol()
              }}
              onCCTChangeComplete={async cct => {
                state.mixRgbcw.temperature = cct
                state.flag = Symbol()
                // if(workMode !== WorkMode.White && workMode !== WorkMode.Colour){
                //   await setWorkMode(WorkMode.White)
                // }
              }}
            />
            <Spacer height={cx(16)} />
          </>
        )}
      </Card>
      <Card style={{ marginHorizontal: cx(24), marginVertical: cx(12) }}>
        <LdvSwitch
          title={I18n.getLang('light_sources_tile_sec_lighting_headline')}
          color={props.theme?.card.background}
          colorAlpha={1}
          enable={switchLed && state.mixRgbcw.colorLightSwitch}
          setEnable={async v => {
            // if(workMode !== WorkMode.White && workMode !== WorkMode.Colour){
            //   await setWorkMode(WorkMode.Colour)
            // }
            if(v){
              if(!switchLed){
                state.mixRgbcw.whiteLightSwitch = false
              }
              state.mixRgbcw.colorLightSwitch = true
              sendDpWithTimestamps(dpKC.mix_rgbcw.code, () => setMix({
                ...state.mixRgbcw,
                mixRgbcwEnabled: true
              })).then()
              if (!switchLed) {
                setSwitchLed(true).then()
              }
            }else{
              if(!state.mixRgbcw.whiteLightSwitch){
                setSwitchLed(false).then()
              }else{
                state.mixRgbcw.colorLightSwitch = false
                sendDpWithTimestamps(dpKC.mix_rgbcw.code, () => setMix({
                ...state.mixRgbcw,
                mixRgbcwEnabled: true
              })).then()
              }
            }
            props.setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
          }}
        />
        {switchLed && state.mixRgbcw.colorLightSwitch && (
          <>
            <ColorAdjustView
              h={state.mixRgbcw.h}
              s={state.mixRgbcw.s}
              v={state.mixRgbcw.v}
              reserveSV={true}
              onHSVChange={(h, s, v) => {
                state.mixRgbcw.h = h;
                state.mixRgbcw.s = s;
                state.mixRgbcw.v = v;
                state.control = Symbol()
              }}
              onHSVChangeComplete={async (h, s, v) => {
                state.mixRgbcw.h = h;
                state.mixRgbcw.s = s;
                state.mixRgbcw.v = v;
                state.flag = Symbol()
                // if(workMode !== WorkMode.White && workMode !== WorkMode.Colour){
                //   await setWorkMode(WorkMode.Colour)
                // }
              }}
            />
            <Spacer height={cx(16)} />
          </>
        )}
      </Card>
    </>
  );
};

export default withTheme(MixHome)
