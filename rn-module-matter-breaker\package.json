{"name": "rn-module-matter-breaker", "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "replaceStart": "python ../.temp/replace-blacklist.py && node node_modules/react-native/local-cli/cli.js start", "lint": "prettier --write '**/*.{js,jsx,ts,tsx}'", "postinstall": "patch-package --patch-dir node_modules/@tuya/tuya-panel-patches/patches"}, "main": "index.js", "dependencies": {"@ledvance/base": "^1.3.62-0", "@ledvance/ui-biz-bundle": "^1.1.135-2", "ahooks": "^2.x", "lodash": "^4.17.21", "react": "16.8.3", "react-native": "0.59.10", "react-native-linear-gradient": "^2.8.3", "tuya-panel-kit": "^4.9.4"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.12.1", "@tuya/tuya-panel-patches": "0.59.10", "@types/lodash": "^4.17.13", "@types/react": "17.0.83", "@types/react-native": "^0.65", "@types/tuya-panel-kit": "^4.7.3", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "eslint-config-tuya": "^1.0.0", "eslint-import-resolver-alias": "^1.1.2", "metro-react-native-babel-preset": "^0.63.0", "react-native-typescript-transformer": "^1.2.13", "typescript": "^4.1.2"}, "resolutions": {"react-native-gesture-handler": "1.3.0"}, "description": ""}