import { NavigationRoute } from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import RandomTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/randomTime/Router'
import FixedTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/Router'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import SwitchInchingPageRouters from '@ledvance/ui-biz-bundle/src/newModules/swithInching/Router'
import SwitchStatePage from 'pages/switchState/SwitchStatePage'
import LightModePageRouters from '@ledvance/ui-biz-bundle/src/newModules/lightMode/Router'
import {PlugPowerOnBehaviorPageRouters} from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/Router'
export const RouterKey = {
    main: 'main',
    switch_state: 'switch_state',
    ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.switch_state,
        component: SwitchStatePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    ...TimerPageRouters,
    ...TimeSchedulePageRouters,
    ...RandomTimePageRouters,
    ...FixedTimePageRouters,
    ...HistoryPageRouters,
    ...SwitchInchingPageRouters,
    ...LightModePageRouters,
    ...PlugPowerOnBehaviorPageRouters,
]
