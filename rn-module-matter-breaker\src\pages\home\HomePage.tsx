import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, { useMemo } from 'react'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Utils } from 'tuya-panel-kit'
import { NativeApi } from '@ledvance/base/src/api/native'
import { ScrollView, StyleSheet, View } from "react-native"
import Spacer from '@ledvance/base/src/components/Spacer'
import { cloneDeep } from 'lodash'
import SocketItem from '@ledvance/base/src/components/SocketItem'
import I18n from '@ledvance/base/src/i18n'
import { isSupportFunctions } from '@ledvance/base/src/utils/common'
import { useSwitch, useSwitch2, useSwitchNames, useAdvanceData } from '../../features/FeatureHooks'
import { useReactive } from 'ahooks'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import HybridSwitchView from './HybridSwitchView'

const { withTheme } = Utils.ThemeUtils
const { convertX: cx } = Utils.RatioUtils

const HomePage = () => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const [switch1, setSwitch1] = useSwitch()
  const [switch2, setSwitch2] = useSwitch2()
  const [switchNames, setSwitchNames] = useSwitchNames()
  const advanceData = useAdvanceData(switchNames)
  const state = useReactive({
    loading: false,
  })
  const switchChannels = useMemo(() => {
    const channels = [switch1]
    if (isSupportFunctions('switch_2')) {
      channels.push(switch2)
    }
    return channels
  }, [switch1, switch2])
  const styles = StyleSheet.create({
    content: {
      flex: 1
    }
  })
  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={styles.content}>
          <Spacer />
          <HybridSwitchView
            switchChannels={switchChannels}
            style={{marginHorizontal: cx(24)}}
            onSwitchChange={async (index, value) => {
              if (index === 0) {
                await setSwitch1(value)
              } else if (index === 1) {
                await setSwitch2(value)
              }
            }}/>
          <Spacer height={cx(16)}/>
          <SocketItem
            title={I18n.getLang(isSupportFunctions('switch_2') ? 'switchmodule_switch1title' : 'Onoff_button_socket')}
            name={switchNames[0].secondaryTitle}
            onNameChange={async newName => {
              state.loading = true
              const newSwitchNames = cloneDeep(switchNames)
              newSwitchNames[0].secondaryTitle = newName
              await setSwitchNames(newSwitchNames)
              state.loading = false
            }}
            enabled={switch1}
            onSwitchChange={async enable => {
              state.loading = true
              await setSwitch1(enable)
              state.loading = false
            }}
          />
          {isSupportFunctions('switch_2') && <>
            <Spacer />
            <SocketItem
              title={I18n.getLang('switchmodule_switch2title')}
              name={switchNames[1].secondaryTitle}
              onNameChange={async newName => {
                state.loading = true
                const newSwitchNames = cloneDeep(switchNames)
                newSwitchNames[1].secondaryTitle = newName
                console.log('newSwitchNames22', newSwitchNames)
                const res = await setSwitchNames(newSwitchNames)
                console.log('res22', res)
                state.loading = false
              }}
              enabled={switch2}
              onSwitchChange={async enable => {
                state.loading = true
                await setSwitch2(enable)
                state.loading = false
              }}
            />
          </>}
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
