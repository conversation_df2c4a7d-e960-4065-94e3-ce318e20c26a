import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet, StyleProp, ViewStyle, Platform } from 'react-native'
import { Utils } from 'tuya-panel-kit'
import LinearGradient from 'react-native-linear-gradient'
import Card from '@ledvance/base/src/components/Card'
import ThemeType from '@ledvance/base/src/config/themeType'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

interface HybridSwitchViewProps {
  theme?: ThemeType
  style?: StyleProp<ViewStyle>
  switchChannels: boolean[]
  onSwitchChange?: (index: number, value: boolean) => void
}

const HybridSwitchView = (props: HybridSwitchViewProps) => {

  const styles = StyleSheet.create({
    root: {
      paddingHorizontal: cx(16),
    },
    switchPanelContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    switchPanelCardTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    switchPanel: {
      width: cx(120),
      height: cx(120),
      borderRadius: cx(6),
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: cx(3),
      borderColor: '#ECECEC',
      overflow: 'hidden',
      backgroundColor: '#F3F1F1',
    },
    container: {
      width: cx(90),
      height: cx(90),
      flexDirection: 'row',
      borderRadius: cx(6),
      borderWidth: 1,
      borderColor: '#B8B7B7',
      overflow: 'hidden'
    },
    touchContainer: {
      flex: 1,
      borderRightWidth: 1,
      borderColor: '#9E9E9E',
    },
    innerPanel: {
      flex: 1,
      backgroundColor: '#F8F8F8',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    insertColor: {
      width: '100%',
      height: cx(10),
      backgroundColor: '#FF6A00',
    },
    indicator: {
      width: cx(20),
      height: cx(2),
      borderRadius: cx(1),
      backgroundColor: '#F60',
      marginBottom: cx(15),
    },
  })

  return (
    <Card style={[styles.root, props.style]}>
      <Spacer width={cx(16)} />
      <Text style={styles.switchPanelCardTitle}>{I18n.getLang('hybrid_headline_text')}</Text>
      <Spacer />
      <View style={styles.switchPanelContainer}>
        <LinearGradient
          colors={['#FFFFFF', '#F3F1F1']}
          style={styles.switchPanel}
        >
          <View style={styles.container}>
            {props.switchChannels.map((channel, index) => (
              <TouchableOpacity
                key={index}
                activeOpacity={Platform.OS === 'ios' ? 0.5 : 0.9}
                onPress={() => {
                  props.onSwitchChange?.(index, !channel)
                }}
                style={[styles.touchContainer, { borderRightWidth: index === props.switchChannels.length - 1 ? 0 : 1 }]}
              >
                <View style={styles.innerPanel}>
                  <View style={[styles.indicator, { backgroundColor: channel ? '#F60' : '#585858' }]} />
                  <LinearGradient
                    colors={['#DDDDDD', '#EBEBEB']}
                    style={styles.insertColor}
                  />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </LinearGradient>
        <Spacer height={cx(30)}/>
      </View>
    </Card>
  )
}

export default withTheme(HybridSwitchView)
