import React from 'react'
import {ScrollView, StyleSheet, Text} from 'react-native'
import Page from '@ledvance/base/src/components/Page'
// import I18n from '@ledvance/base/src/i18n'
import {Utils} from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import {useReactive, useUpdateEffect} from 'ahooks'
import ThemeType from '@ledvance/base/src/config/themeType'
import {useSwitchState} from './SwitchStateActions'
import {useDeviceInfo} from '@ledvance/base/src/models/modules/NativePropsSlice'
import I18n from '@ledvance/base/src/i18n'
import OptionGroup from '@ledvance/base/src/components/OptionGroup'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const SWITCH_STATE_FLIP = 'flip'
const SWITCH_STATE_SYNC = 'sync'
const SWITCH_STATE_BUTTON = 'button'

const SwitchStatePage = (props: { theme?: ThemeType }) => {
  const [switchState, setSwitchState] = useSwitchState()
  const deviceInfo = useDeviceInfo()

  const state = useReactive({
    switchState,
    loading: false,
  })

  useUpdateEffect(() => {
    state.switchState = switchState
  }, [switchState])

  const styles = StyleSheet.create({
    root: {
      flex: 1,
    },
    tipText: {
      marginHorizontal: cx(24),
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
  })

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('hybrid_switchstate_title')}
      loading={state.loading}>
      <ScrollView style={styles.root} nestedScrollEnabled={true}>
        <Spacer height={cx(16)} />
        <Text style={styles.tipText}>{I18n.getLang('switchmodule_typesettingdescription')}</Text>
        <Spacer />
        <OptionGroup
          tips={I18n.getLang('hybrid_switchstate_setting_text')}
          selected={state.switchState}
          options={[{
            title: I18n.getLang('hybrid_switchstate_setting1'),
            content: I18n.getLang('switchmodule_typesetting1'),
            value: SWITCH_STATE_FLIP,
            onPress: (value) => {
              state.switchState = value
              setSwitchState(value).then()
            }
          }, {
            title: I18n.getLang('hybrid_switchstate_setting2'),
            content: I18n.getLang('switchmodule_typesetting2'),
            value: SWITCH_STATE_SYNC,
            onPress: (value) => {
              state.switchState = value
              setSwitchState(value).then()
            }
          }, {
            title: I18n.getLang('hybrid_switchstate_setting3'),
            content: I18n.getLang('hybrid_switchstate_setting3_description'),
            value: SWITCH_STATE_BUTTON,
            onPress: (value) => {
              state.switchState = value
              setSwitchState(value).then()
            }
          }]}
        />
      </ScrollView>
    </Page>
  )
}

export default withTheme(SwitchStatePage)
