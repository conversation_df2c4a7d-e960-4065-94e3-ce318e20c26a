import React, { PropsWithChildren } from 'react'
import { StyleSheet, Text, View, ViewProps } from 'react-native'
import { Utils } from 'tuya-panel-kit'
import Card from '@ledvance/base/src/components/Card'
import Spacer from '@ledvance/base/src/components/Spacer'

const { convertX: cx } = Utils.RatioUtils

export interface AdvancedData {
  title: string
  subtitles?: string[]
  statusColor: string
  dp: { key: string, code: string }
  router: { key: string, params?: any }
}

export interface AdvanceCardProps extends PropsWithChildren<ViewProps> {
  data: AdvancedData
  onPress: () => void
}

const AdvanceCard = (props: AdvanceCardProps) => {
  return (
    <Card
      style={styles.itemContainer}
      containerStyle={styles.itemContent}
      onPress={props.onPress}>
      <View style={styles.dotBg}>
        <View
          style={[
            styles.dot,
            { backgroundColor: props.data.statusColor },
          ]} />
      </View>
      {
        props.children ?
          props.children :
          <View style={styles.titleBg}>
            <Text style={styles.title}>{props.data.title}</Text>
            <Spacer height={cx(8)} />
            {
              (!!props.data.subtitles) && props.data.subtitles.map(subtitle => (
                <Text style={styles.subtitle} key={subtitle}>{subtitle}</Text>
              ))
            }
          </View>
      }
    </Card>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    width: cx(154),
    borderRadius: cx(16),
  },
  itemContent: {},
  titleBg: {
    height: cx(118),
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    marginHorizontal: cx(16),
    color: '#000',
    fontSize: cx(14),
    textAlign: 'center',
    fontFamily: 'helvetica_neue_lt_std_roman',
  },
  subtitle: {
    color: '#666',
    fontSize: cx(10),
    textAlign: 'center',
    fontFamily: 'helvetica_neue_lt_std_roman',
  },
  dotBg: {
    position: 'absolute',
    top: cx(10),
    end: cx(10),
    borderWidth: cx(.5),
    borderColor: '#cbcbcb80',
    borderRadius: cx(7),
  },
  dot: {
    width: cx(10),
    height: cx(10),
    backgroundColor: 'rgb(0, 201, 49)',
    borderRadius: cx(5),
    borderColor: '#fff',
    borderWidth: cx(1),
  },
})

export default AdvanceCard