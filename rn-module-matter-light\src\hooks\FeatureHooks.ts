import { useDeviceId, useDp, useFlagMode, useTimeSchedule } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { Result } from '@ledvance/base/src/models/modules/Result'
import { mapFloatToRange, mapValueToRatio } from '@ledvance/base/src/utils'
import { useCallback, useEffect, useState } from 'react'
import { SupportUtils, parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import I18n from '@ledvance/base/src/i18n/index'
import { RouterKey } from '../navigation/Router'
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard';
import {
  ControlData,
  dp2PowerMemory,
  dp2RandomTimingList,
  PowerMemory,
  powerMemory2Dp,
  RandomTiming,
  randomTimingList2Dp,
} from './ProtocolConvert'
import { cloneDeep } from 'lodash'
import { SleepWakeUpPageRouteParams } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpPage'
import { useSleepMode, useWakeUp } from '@ledvance/ui-biz-bundle/src/newModules/sleepWakeUp/SleepWakeUpActions'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { BiorhythmPageParams } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmPage';
import { NativeApi } from '@ledvance/base/src/api/native'
import {MoodInfo, MoodPageParams } from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface';
import { useReactive, useUpdateEffect } from 'ahooks'
import { Formatter } from '@tuya/tuya-panel-lamp-sdk'
import { dp2Obj, obj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import { RandomTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage'
import { FlagPageProps } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage'
import { getFlagMode, saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { createParams } from '@ledvance/base/src/hooks/Hooks'
import { LightBehaviorPageParams } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/LightBehaviorPage'
import { usePowerOffMemory } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions'
import res from '@ledvance/base/src/res'
import { DeviceData } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import { useSwitchGradient } from '@ledvance/ui-biz-bundle/src/newModules/switchGradient/SwitchGradientActions'
import { useRandomTime } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions'
import { SwitchGradientPageParams } from '@ledvance/ui-biz-bundle/src/newModules/switchGradient/SwitchGradientPage'
import { useBiorhythm } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions'
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'
import { RemoteControlPageParams } from '@ledvance/ui-biz-bundle/src/newModules/remoteControl/RemoteControlPage';
import { useRemoteControl } from '@ledvance/ui-biz-bundle/src/newModules/remoteControl/RemoteControlActions';
import { ApplyForItem } from '@ledvance/base/src/utils/interface'

const { ControlDataFormatter } = Formatter
const control = new ControlDataFormatter()

const dpKC = {
  work_mode: { key: 'work_mode', code: '2' },
  scene_data: { key: 'scene_data', code: '6' },
  countdown: { key: 'countdown', code: '7' },
  music_data: { key: 'music_data', code: '8' },
  rhythm_mode: { key: 'rhythm_mode', code: '30' },
  sleep_mode: { key: 'sleep_mode', code: '31' },
  wakeup_mode: { key: 'wakeup_mode', code: '32' },
  power_memory: { key: 'power_memory', code: '33' },
  do_not_disturb: { key: 'do_not_disturb', code: '34' },
  switch_gradient: { key: 'switch_gradient', code: '35' },
  switch: { key: 'switch', code: '83' },
  brightness_control: { key: 'brightness_control', code: '85' },
  color_temp_control: { key: 'color_temp_control', code: '86' },
  hs_color_set: { key: 'hs_color_set', code: '88' },
  random_timing: { key: 'random_timing', code: '210' },
  control_data: { key: 'control_data', code: '9' },
  remote_switch: { key: 'remote_switch', code: '41' },
}

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.switch.code)
}

export enum WorkMode {
  White = 'white',
  Colour = 'colour',
  Scene = 'scene',
  Music = 'music'
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWokeModeDp] = useDp<WorkMode, any>(dpKC.work_mode.code)
  const [workMode, setWokeMode] = useState(workModeDp)

  useUpdateEffect(() => {
    setWokeMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) => {
    setWokeMode(v)
    return setWokeModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

export function putControlData(): (isColorMode: boolean, value: ControlData) => Promise<any> {
  const [, setControlData] = useDp(dpKC.control_data.code)
  return async (isColorMode: boolean, value: ControlData) => {
    const v = control.format({
      mode: 1,
      hue: isColorMode ? value.h : 0,
      saturation: isColorMode ? Math.round(mapFloatToRange(value.s / 100, 0, 1000)) : 0,
      value: isColorMode ? Math.round(mapFloatToRange(value.brightness / 100, 0, 1000)) : 0,
      brightness: isColorMode ? 0 : Math.round(mapFloatToRange(value.brightness / 100, 0, 1000)),
      temperature: isColorMode ? 0 : Math.round(mapFloatToRange((value.cct || 0) / 100, 0, 1000)),
    })
    await setControlData(v)
  }
}

const dpMinBrightnessControl = 1
const dpMaxBrightnessControl = 254

export function useBrightnessControl(): [number, (value: number) => Promise<Result<any>>] {
  const [bc, setBc] = useDp<number, any>(dpKC.brightness_control.code)
  const ratio = mapValueToRatio(bc, dpMinBrightnessControl, dpMaxBrightnessControl)
  const setBrightnessControl = useCallback(async (value: number) => {
    return await setBc(brightness2dpNumber(value))
  }, [setBc])
  const brightness = Math.max(Math.round(ratio * 100), 1)
  return [brightness, setBrightnessControl]
}

function brightness2dpNumber(brightness: number): number {
  const bv = mapFloatToRange(brightness / 100, dpMinBrightnessControl, dpMaxBrightnessControl)
  return Math.round(bv)
}

const dpMinColorTempControl = 153
const dpMaxColorTempControl = 370

export function useColorTempControl(): [number, (value: number) => Promise<Result<any>>] {
  const [ctc, setCTC] = useDp<number, any>(dpKC.color_temp_control.code)
  const ratio = mapValueToInverseRatio(ctc, dpMinColorTempControl, dpMaxColorTempControl)
  const setColorTempControl = useCallback(async (value: number) => {
    return await setCTC(cct2dpNumber(value))
  }, [setCTC])
  return [Math.round(ratio * 100), setColorTempControl]
}

function cct2dpNumber(cct: number): number {
  const dpValue = mapInverseRatioToValue(cct / 100, dpMinColorTempControl, dpMaxColorTempControl)
  return Math.round(dpValue)
}

const dpMinHSColorSet = 0
const dpMaxHSColorSet = 254

export function useHSColorSet(): [number, number, (h: number, s: number) => Promise<Result<any>>] {
  const [hsHex, setHsHex] = useDp<string, any>(dpKC.hs_color_set.code)

  const { h, s } = dpHex2hs(hsHex)

  const setHSColorSet = useCallback(async (h: number, s: number) => {
    const hsHexStr = hs2dpHex(h, s)
    return await setHsHex(hsHexStr)
  }, [setHsHex])

  return [h, s, setHSColorSet]
}

function dpHex2hs(hsHex: string) {
  const hsHexFixed = !!hsHex && hsHex.length == 4 ? hsHex : '0000'

  const hDpV = parseInt(hsHexFixed.substring(0, 2), 16)
  const sDpV = parseInt(hsHexFixed.substring(2), 16)

  const h = mapValueToRatio(hDpV, dpMinHSColorSet, dpMaxHSColorSet)
  const s = mapValueToRatio(sDpV, dpMinHSColorSet, dpMaxHSColorSet) * 100
  return {
    h: Math.round(mapFloatToRange(h, 0, 360)),
    s: Math.round(s),
  }
}

function hs2dpHex(h: number, s: number): string {
  const hDpv = Math.round(mapFloatToRange(mapValueToRatio(h, 0, 360), dpMinHSColorSet, dpMaxHSColorSet))
  const sDpv = Math.round(mapFloatToRange(s / 100, dpMinHSColorSet, dpMaxHSColorSet))
  return `${hDpv.toString(16).padStart(2, '0')}${sDpv.toString(16).padStart(2, '0')}`.toUpperCase()
}

export function useRandomTiming(): [RandomTiming[], (value: RandomTiming[]) => Promise<Result<any>>] {
  const [rt, setRt] = useDp<string, any>(dpKC.random_timing.code)
  const setRandomTiming = useCallback(async (value: RandomTiming[]) => {
    const dpValue = randomTimingList2Dp(cloneDeep(value))
    return await setRt(dpValue)
  }, [setRt])
  const randomTimingList = dp2RandomTimingList(rt)
  return [randomTimingList, setRandomTiming]
}

export function usePowerMemory(): [PowerMemory, (value: PowerMemory) => Promise<Result<any>>] {
  const [pm, setPm] = useDp<string, any>(dpKC.power_memory.code)

  const setPowerMemory = useCallback(async (value: PowerMemory) => {
    const obj = cloneDeep(value)
    const dpValue = powerMemory2Dp(obj)
    return await setPm(dpValue)
  }, [setPm])

  return [dp2PowerMemory(pm), setPowerMemory]
}

export function useDoNotDisturb(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.do_not_disturb.code)
}

// export function useRhythmMode() {
//   return useBiorhythm(dpKC.rhythm_mode.code)
// }

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code)
}

function mapValueToInverseRatio(value: number, min: number, max: number): number {
  // 确保 value 在 [min, max] 范围内
  value = Math.max(min, Math.min(max, value))

  // 计算与给定范围 [min, max] 相对应的比例
  return 1 - (value - min) / (max - min)
}

function mapInverseRatioToValue(inverseRatio: number, min: number, max: number): number {
  // 将反比例映射回值
  return min + (1 - inverseRatio) * (max - min)
}

export function isSupportHS(): boolean {
  return SupportUtils.isSupportDp(dpKC.hs_color_set.key)
}

export function isSupportCT(): boolean {
  return SupportUtils.isSupportDp(dpKC.color_temp_control.key)
}

export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.brightness_control.key)
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp(dpKC.scene_data.key)
}

export function isSupportMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.music_data.key)
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key)
}

export function isSupportSleepSchedule(): boolean {
  return SupportUtils.isSupportDp(dpKC.sleep_mode.key)
}

export function isSupportRhythm(): boolean {
  return SupportUtils.isSupportDp(dpKC.rhythm_mode.key)
}

export function isSupportPowerOnBehaviour(): boolean {
  return SupportUtils.isSupportDp(dpKC.power_memory.key)
}

export function isSupportSwitchGradient(): boolean {
  return SupportUtils.isSupportDp(dpKC.switch_gradient.key)
}

export function isSupportRandomTiming(): boolean {
  return SupportUtils.isSupportDp(dpKC.random_timing.key)
}

export function isSupportDoNotDisturb() {
  return SupportUtils.isSupportDp(dpKC.do_not_disturb.key);
}

export function isSupportRemoteControl() {
  return SupportUtils.isSupportDp(dpKC.remote_switch.key);
}

export function useAdvancedData(isSuspend: boolean): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const [workMode] = useWorkMode()
  const [rhythmMode] = useBiorhythm(getGlobalParamsDp('rhythm_mode'), true)
  const deviceId = useDeviceId()
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const [sleepList] = useSleepMode(getGlobalParamsDp('sleep_mode'), true);
  const [wakeUpList] = useWakeUp(getGlobalParamsDp('wakeup_mode'), true);
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const [switchOnDuration, switchOffDuration] = useSwitchGradient(getGlobalParamsDp('switch_gradient'))
  const [powerMemory] = usePowerOffMemory(getGlobalParamsDp('power_memory'));
  const [remoteControl] = useRemoteControl(getGlobalParamsDp('remote_switch'));
  const [flagMode, setFlagMode] = useFlagMode()
  const [randomTime] = useRandomTime(getGlobalParamsDp('random_timing'), false, true)
  const state = useReactive({
    rhythmModeStatus: AdvancedStatus.Disable,
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (rhythmMode.enable) {
      state.rhythmModeStatus = isSuspend ? AdvancedStatus.Suspend : AdvancedStatus.Enable
    } else {
      state.rhythmModeStatus = AdvancedStatus.Disable
    }
  }, [isSuspend, rhythmMode.enable])


  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })
      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          setFlagMode(parseJSON(res.data))
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    if (workMode !== WorkMode.Scene && workMode !== WorkMode.Music && flagMode?.flagMode) {
      setFlagMode({
        flagMode: false,
        flagId: undefined
      })
      saveFlagMode(deviceId, JSON.stringify({
        flagMode: false,
        flagId: undefined
      })).then()
    }
  }, [workMode])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  useEffect(() => {
    const sleepWakeUp = [...sleepList, ...wakeUpList];
    state.sleepWakeUpStatus = sleepWakeUp.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [sleepList, wakeUpList])


  const lightApplyFor: ApplyForItem[] = [
    {
      type: 'light',
      name: I18n.getLang('Onoff_button_socket'),
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch'),
      enable: true,
    },
  ];

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      deviceData: {
        type: DeviceType.LightSource,
        // @ts-ignore
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
        }
      },
      isManual: !(dps.hasOwnProperty(getGlobalParamsDp('scene_data'))),
      mood: undefined
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('brightness_control'))) {
      const brightness = Math.round(mapValueToRatio(dps[getGlobalParamsDp('brightness_control')], dpMinBrightnessControl, dpMaxBrightnessControl) * 100)
      deviceState.deviceData.deviceData.brightness = brightness
      deviceState.deviceData.deviceData.v = brightness
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('color_temp_control'))) {
      deviceState.deviceData.deviceData.temperature = Math.round(mapValueToInverseRatio(dps[getGlobalParamsDp('color_temp_control')], dpMinColorTempControl, dpMaxColorTempControl) * 100)
      deviceState.deviceData.deviceData.isColorMode = false
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('hs_color_set'))) {
      const hs = dpHex2hs(dps[getGlobalParamsDp('hs_color_set')])
      deviceState.deviceData.deviceData.h = hs.h
      deviceState.deviceData.deviceData.s = hs.s
      deviceState.deviceData.deviceData.isColorMode = true
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('scene_data'))) {
      const sceneMood = dp2Obj(dps[getGlobalParamsDp('scene_data')])
      deviceState.mood = {
        ...sceneMood,
        mainLamp: {
          ...sceneMood.mainLamp,
          id: sceneMood.id
        }
      }
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('switch')] = true
        if ((mood as MoodInfo).mainLamp && (mood as MoodInfo).mainLamp?.nodes?.length) {
          manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
          manualDps[getGlobalParamsDp('scene_data')] = obj2Dp((mood as MoodInfo))
        }
      } else {
        const device = deviceData.deviceData as DeviceData
        manualDps[getGlobalParamsDp('switch')] = applyForList[0].enable
        manualDps[getGlobalParamsDp('work_mode')] = device.isColorMode ? WorkMode.Colour : WorkMode.White
        if (applyForList[0].enable) {
          const { h, s, v, brightness, temperature, isColorMode } = deviceData.deviceData
          if (isColorMode) {
            manualDps[getGlobalParamsDp('hs_color_set')] = hs2dpHex(h, s)
            manualDps[getGlobalParamsDp('brightness_control')] = brightness2dpNumber(v)
          } else {
            if (isSupportCT()) {
              manualDps[getGlobalParamsDp('color_temp_control')] = cct2dpNumber(temperature)
            }
            manualDps[getGlobalParamsDp('brightness_control')] = brightness2dpNumber(brightness)
          }
        }
      }
      return manualDps;
    },
    []
  );

  if (isSupportHS()) {
    advanceData.push({
      title: I18n.getLang('Feature_devicepanel_flags'),
      dp: { key: 'flag', code: 'flag' },
      statusColor: getAdvancedStatusColor(flagMode?.flagMode && workMode !== WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      icons: res.flag_icon,
      router: {
        key: RouterKey.ui_biz_flag_page,
        params: {
          sceneDataCode: dpKC.scene_data.code,
          workModeCode: dpKC.work_mode.code,
          isSupportColor: true,
          switchLedCode: dpKC.switch.code
        } as FlagPageProps
      }
    })
  }

  if (isSupportMood()) {
    const params = createParams<MoodPageParams>({
      isSupportColor: isSupportHS(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportCT(),
      switchLedDp: getGlobalParamsDp('switch'),
      mainDp: getGlobalParamsDp('scene_data'),
      mainWorkMode: getGlobalParamsDp('work_mode'),
      mainSwitch: getGlobalParamsDp('switch'),
    });
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(!flagMode?.flagMode && workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.scene_data,
      router: {
        key: RouterKey.ui_biz_mood,
        params
      },
    })
  }

  if (isSupportRhythm()) {
    const params = createParams<BiorhythmPageParams>({
      biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        randomTimeDpCode: getGlobalParamsDp('random_timing'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportCT(),
    })
    advanceData.push({
      title: I18n.getLang('add_new_trigger_time_system_back_text'),
      statusColor: getAdvancedStatusColor(state.rhythmModeStatus),
      subtitles: state.rhythmModeStatus === AdvancedStatus.Suspend ? [I18n.getLang('light_sources_feature_4_text_name')] : [],
      dp: { key: 'rhythm_mode', code: getGlobalParamsDp('rhythm_mode') },
      router: {
        key: RouterKey.bi_biz_biological,
        params
      },
    })
  }

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: lightApplyFor,
        applyForDisabled: true,
        isSupportMood: isSupportMood(),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: isSupportHS(),
        isSupportBrightness: isSupportBrightness(),
        isSupportTemperature: isSupportCT(),
        isMatterLight: true
      } as TimeSchedulePageParams,
    },
  })

  if (isSupportMusic()) {
    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.music_data,
      router: {
        key: RouterKey.ui_biz_music,
        params: {
          switch_led: dpKC.switch.code,
          work_mode: dpKC.work_mode.code,
          mix_rgbcw: undefined,
          mix_light_scene: undefined,
          music_data: dpKC.music_data.code,
          isMixRGBWLamp: false,
        },
      },
    })
  }

  if (isSupportTimer()) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: dpKC.countdown.code,
              enableDp: dpKC.switch.code,
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }

  if (isSupportRandomTiming()) {
    const params = createParams<RandomTimePageParams>({
      randomTimeDpCode: getGlobalParamsDp('random_timing'),
      conflictDps: {
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        sleepDpCode: getGlobalParamsDp('sleep_mode'),
        wakeUpDpCode: getGlobalParamsDp('wakeup_mode')
      },
      applyForList: lightApplyFor,
      isSupportColor: isSupportHS(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportCT(),
    });

    advanceData.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      statusColor: getAdvancedStatusColor(!!randomTime.find(item => item.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'random_timing', code: getGlobalParamsDp('random_timing') },
      router: {
        key: RouterKey.ui_biz_random_time_new,
        params
      },
    })
  }

  if (isSupportSleepSchedule()) {
    const params = createParams<SleepWakeUpPageRouteParams>({
      wakeUpDpCode: dpKC.wakeup_mode.code,
      sleepDpCode: dpKC.sleep_mode.code,
      conflictDps: {
        biorhythmDpCode: getGlobalParamsDp('rhythm_mode'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_timing'),
        randomTimeDpCode: getGlobalParamsDp('random_timing')
      },
      isSupportColor: isSupportHS(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportCT(),
      applyForList: lightApplyFor,
    });

    advanceData.push({
      title: I18n.getLang('add_sleepschedule_one_source_system_back_text'),
      statusColor: getAdvancedStatusColor(state.sleepWakeUpStatus),
      dp: dpKC.sleep_mode,
      router: {
        key: RouterKey.ui_biz_sleep_wakeUp_new,
        params
      },
    })
  }

  if (isSupportPowerOnBehaviour()) {
    const params = createParams<LightBehaviorPageParams>({
      memoryDpCode: dpKC.power_memory.code,
      disturbDpCode: dpKC.do_not_disturb.code,
      isSupportColor: isSupportHS(),
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportCT(),
      isSupportDoNotDisturb: isSupportDoNotDisturb(),
    });
    advanceData.push({
      title: I18n.getLang('light_sources_specific_settings_power_off'),
      statusColor: getAdvancedStatusColor(powerMemory ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'power_memory', code: getGlobalParamsDp('power_memory') },
      router: {
        key: RouterKey.ui_biz_power_behavior,
        params,
      },
    })
  }
  if (isSupportSwitchGradient()) {
    const params = createParams<SwitchGradientPageParams>({
      switchGradientDpCode: getGlobalParamsDp('switch_gradient')
    })
    advanceData.push({
      title: I18n.getLang('matter_gradient_overview_headline_text'),
      statusColor: getAdvancedStatusColor((switchOnDuration > 0 || switchOffDuration > 0) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'switch_gradient', code: getGlobalParamsDp('switch_gradient') },
      router: {
        key: RouterKey.ui_biz_switch_gradient,
        params
      },
    })
  }

  if (isSupportRemoteControl()) {
    const params = createParams<RemoteControlPageParams>({
      remoteControlDpCode: getGlobalParamsDp('remote_switch'),
    });
    advanceData.push({
      title: I18n.getLang('light_sources_specific_settings_remote_control'),
      statusColor: getAdvancedStatusColor(
        remoteControl ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'remote_switch', code: getGlobalParamsDp('remote_switch')},
      router: {
        key: RouterKey.ui_biz_remote_control,
        params,
      },
    });
  }

  return advanceData
}
