import { WorkMode } from './FeatureHooks'

/**
 * 协议解析参照：https://iot.tuya.com/pmg/dpProtocol?id=owljzadzw7oke7rt
 */
export interface RandomTiming {
  enable: boolean // 2 char
  week: number[] // 2 char
  startTime: {
    hour: number
    min: number
  } // 4 char
  endTime: {
    hour: number
    min: number
  } // 4 char
  hHundreds: number // hsv 中 h 的百位数 2 char
  hTensOnes: number // hsv 中 h 的十&个位数 2 char
  s: number // 2 char
  v: number // 2 char
  brightness: number // 2 char
  cct: number // 2 char
}

export enum PowerMemoryMode {
  Default = '00',
  Last = '01',
  Custom = '02'
}

export interface PowerMemory {
  version: string // 2 char
  mode: PowerMemoryMode // 2 char
  workMode: WorkMode // not in dp
  h: number // 4 char
  s: number // 4 char
  v: number // 4 char
  brightness: number // 4 char
  cct: number // 4 char
}

export interface ControlData {
  h: number;
  s: number;
  cct: number;
  brightness: number;
}

export function dp2PowerMemory(dpValue?: string): PowerMemory {
  if (!dpValue) return getDefPowerMemory()
  const mode = dpValue.substring(2, 4) as PowerMemoryMode
  const h = subHexStr2Int(dpValue, 4, 8)
  const s = scaleDownValue(subHexStr2Int(dpValue, 8, 12))
  const v = scaleDownValue(subHexStr2Int(dpValue, 12, 16))
  const b = scaleDownValue(subHexStr2Int(dpValue, 16, 20))
  const cct = scaleDownValue(subHexStr2Int(dpValue, 20, 24))
  return {
    version: dpValue.substring(0, 2),
    mode: mode,
    workMode: (b === 0 && cct === 0) ? WorkMode.Colour : WorkMode.White,
    h,
    s,
    v,
    brightness: b === 0 ? 1 : b,
    cct: cct,
  }
}

export function getDefPowerMemory(): PowerMemory {
  return {
    version: '00',
    mode: PowerMemoryMode.Default,
    workMode: WorkMode.Colour,
    h: 0,
    s: 100,
    v: 100,
    brightness: 1,
    cct: 0,
  }
}

export function powerMemory2Dp(powerMemory: PowerMemory): string {
  return [
    powerMemory.version,
    powerMemory.mode,
    powerMemory.workMode === WorkMode.White ? '0000' : int2Hex(powerMemory.h, 4),
    powerMemory.workMode === WorkMode.White ? '0000' : int2Hex(scaleUpValue(powerMemory.s), 4),
    powerMemory.workMode === WorkMode.White ? '0000' : int2Hex(scaleUpValue(powerMemory.v), 4),
    powerMemory.workMode === WorkMode.Colour ? '0000' : int2Hex(scaleUpValue(powerMemory.brightness === 0 ? 1 : powerMemory.brightness), 4),
    powerMemory.workMode === WorkMode.Colour ? '0000' : int2Hex(scaleUpValue(powerMemory.cct), 4),
  ].join('')
}

export function dp2RandomTimingList(dpValue?: string): RandomTiming[] {
  if (!dpValue) return []
  // 前4位一定是 000c
  return stringChunk(dpValue.substring(4), 24)
    .map(item => {
      const startTime = subHexStr2Int(item, 4, 8)
      const startHour = Math.trunc(startTime / 60)
      const startMin = startTime % 60

      const endTime = subHexStr2Int(item, 8, 12)
      const endHour = Math.trunc(endTime / 60)
      const endMin = endTime % 60
      return {
        enable: item.substring(0, 2) === '03',
        week: subHexStr2Int(item, 2, 4).toString(2).padStart(7, '0').split('').map(v => parseInt(v)).reverse(),
        startTime: { hour: startHour, min: startMin },
        endTime: { hour: endHour, min: endMin },
        hHundreds: subHexStr2Int(item, 12, 14),
        hTensOnes: subHexStr2Int(item, 14, 16),
        s: subHexStr2Int(item, 16, 18),
        v: subHexStr2Int(item, 18, 20),
        brightness: subHexStr2Int(item, 20, 22),
        cct: subHexStr2Int(item, 22),
      }
    })
}

export function randomTimingList2Dp(list: RandomTiming[]): string {
  const prefix = '000c'
  return prefix + list
    .map(item => {
      return [
        item.enable ? '03' : '02',
        int2Hex(parseInt(item.week.reverse().join(''), 2)),
        int2Hex(item.startTime.hour * 60 + item.startTime.min, 4),
        int2Hex(item.endTime.hour * 60 + item.endTime.min, 4),
        int2Hex(item.hHundreds),
        int2Hex(item.hTensOnes),
        int2Hex(item.s),
        int2Hex(item.v),
        int2Hex(item.brightness),
        int2Hex(item.cct),
      ].join('')
    })
    .join('')
}

export function stringChunk(str: string, n: number): string[] {
  const resultArray: string[] = []
  for (let i = 0; i < str.length; i += n) {
    const chunk = str.substr(i, n)
    resultArray.push(chunk)
  }
  return resultArray
}

export function subHexStr2Int(str: string, start: number, end?: number) {
  return parseInt(str.substring(start, end), 16)
}

export function int2Hex(num: number, hexLength: number = 2): string {
  return num.toString(16).padStart(hexLength, '0')
}

export function scaleDownValue(value: number, scale: number = 10) {
  if (value < scale) return 0
  return Math.round(value / scale)
}

export function scaleUpValue(value: number, scale: number = 10) {
  return value * scale
}

