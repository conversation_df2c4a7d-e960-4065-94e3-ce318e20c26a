import React, { useCallback, useEffect } from 'react'
import Page from '@ledvance/base/src/components/Page'
import {
  useDeviceId,
  useDeviceInfo,
  useFamilyName,
  useFlagMode,
  useGestureControl
} from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import { NativeApi, sendAppEvent } from '@ledvance/base/src/api/native'
import { ScrollView, View } from 'react-native'
import { Utils } from 'tuya-panel-kit'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import I18n from '@ledvance/base/src/i18n'
import LampAdjustView from '@ledvance/base/src/components/LampAdjustView'
import { useIsFocused } from '@react-navigation/core'

import {
  isSupportBrightness,
  isSupportCT,
  isSupportHS,
  putControlData,
  useAdvancedData,
  useBrightnessControl,
  useColorTempControl,
  useHSColorSet,
  useSwitch,
  useWorkMode,
  WorkMode,
} from '../../hooks/FeatureHooks'
import { useReactive, useThrottleFn, useUpdateEffect } from 'ahooks'
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import { saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import { useRhythmSuspend } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmActions'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import dayjs from 'dayjs'
import ThemeType from '@ledvance/base/src/config/themeType'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks'

const { convertX: cx } = Utils.RatioUtils

const HomePage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const devId = useDeviceId()
  const familyName = useFamilyName()
  const isFocused = useIsFocused()
  const [switchLed, setSwitchLed] = useSwitch()
  const [workMode, setWorkMode] = useWorkMode()
  const [flagMode, setFlagMode] = useFlagMode()
  const [h, s, setHS] = useHSColorSet()
  const [cct, setCCT] = useColorTempControl()
  const [brightness, setBrightness] = useBrightnessControl()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const [isSuspend, getSuspendTime, setSuspendTime] = useRhythmSuspend(getGlobalParamsDp('rhythm_mode'))
  const setControlData = putControlData()
  const advanceData = useAdvancedData(isSuspend)
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator();

  const state = useReactive({
    switchLed,
    h,
    s,
    cct,
    brightness,
    flag: Symbol(),
    updateSuspendTimeFlag: Symbol(),
    controlFlag: Symbol()
  })

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useUpdateEffect(() => {
    state.switchLed = switchLed
  }, [switchLed])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('hs_color_set')); // hs_color_set
    if (!shouldBlock) {
      state.h = h
      state.s = s
    }
  }, [h, s, onDpResponse])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('color_temp_control')); // color_temp_control
    if (!shouldBlock) {
      state.cct = cct
    }
  }, [cct, onDpResponse])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('brightness_control')); // brightness_control
    if (!shouldBlock) {
      state.brightness = brightness
    }
  }, [brightness, onDpResponse])

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      NativeApi.log(`gestureSwitch: ${gestureSwitch}`)
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = workMode === WorkMode.Colour
    if (!isFocused || !isColorMode || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    runColour()
  }, [isFocused, workMode, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = workMode === WorkMode.Colour
    state.brightness = gestureBrightness
    if (isColorMode) {
      runColour()
    } else {
      runBrightness()
    }
  }, [isFocused, gestureBrightness])

  const { run: runColour } = useThrottleFn(() => {
    if (state.switchLed) {
      setWorkMode(WorkMode.Colour).then()
      setHS(state.h, state.s).then()
      setBrightness(state.brightness).then()
      state.controlFlag = Symbol()
    }
  }, { wait: 500 })

  const { run: runBrightness } = useThrottleFn(() => {
    if (state.switchLed) {
      setWorkMode(WorkMode.White).then()
      setBrightness(state.brightness).then()
      state.controlFlag = Symbol()
    }
  }, { wait: 500 })

  useUpdateEffect(() => {
    run()
  }, [state.flag])

  const putControlAction = () => {
    const isColorMode = workMode === WorkMode.Colour
    const v = {
      h: state.h,
      s: state.s,
      brightness: state.brightness,
      cct: state.cct,
    }
    if (state.switchLed) {
      NativeApi.log(`putControlAction: ${JSON.stringify(v)}`)
      setControlData(isColorMode, v).then()
    }
  }

  const { run } = useThrottleFn(putControlAction, { wait: 500 })

  const getBlockColor = useCallback(() => {
    if (workMode === 'white') return cctToColor(state.cct)
    if (workMode === 'colour') {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100))
      return hsv2Hex(state.h, s, 100)
    }
    return props.theme?.card.background
  }, [workMode, state.s, state.h, state.cct, state.brightness])

  useUpdateEffect(() => {
    if (flagMode?.flagMode) {
      setFlagMode({
        flagMode: false,
        flagId: undefined
      })
      saveFlagMode(devId, JSON.stringify({
        flagMode: false,
        flagId: undefined
      })).then()
    }
    setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
  }, [state.controlFlag])

  useUpdateEffect(() => {
    setSuspendTime(dayjs().format('YYYY-MM-DD')).then()
  }, [state.updateSuspendTimeFlag])

  useUpdateEffect(() => {
    getSuspendTime()
  }, [workMode, s, h, cct, brightness, switchLed])


  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Card style={{ marginVertical: cx(12), marginHorizontal: cx(24) }}>
            <View>
              <LdvSwitch
                title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
                color={getBlockColor()}
                colorAlpha={1}
                enable={state.switchLed}
                setEnable={async (enable: boolean) => {
                  state.switchLed = enable
                  await setSwitchLed(enable)
                  state.updateSuspendTimeFlag = Symbol()
                }} />
              {state.switchLed &&
                <LampAdjustView
                  isSupportColor={isSupportHS()}
                  isSupportTemperature={isSupportCT()}
                  isSupportBrightness={isSupportBrightness()}
                  isColorMode={workMode === WorkMode.Colour}
                  reserveSV={true}
                  minSaturation={1}
                  setIsColorMode={async isColorMode => {
                    state.controlFlag = Symbol()
                    const mode = isColorMode ? WorkMode.Colour : WorkMode.White;
                    await sendDpWithTimestamps(getGlobalParamsDp('work_mode'), () => setWorkMode(mode)) // work_mode
                  }}
                  h={state.h} s={state.s} v={state.brightness}
                  onHSVChange={(h, s, v) => {
                    state.h = h
                    state.s = s
                    state.brightness = v
                    state.flag = Symbol()
                  }}
                  onHSVChangeComplete={async (h, s, v) => {
                    state.h = h;
                    state.s = s;
                    state.brightness = v;
                    state.controlFlag = Symbol()
                    sendDpWithTimestamps(getGlobalParamsDp('work_mode'), () => setWorkMode(WorkMode.Colour)) // work_mode
                    sendDpWithTimestamps(getGlobalParamsDp('hs_color_set'), () => setHS(h, s)) // hs_color_set
                    sendDpWithTimestamps(getGlobalParamsDp('brightness_control'), () => setBrightness(v)) // brightness_control
                  }}
                  colorTemp={state.cct}
                  brightness={state.brightness}
                  onCCTChange={cct => {
                    state.cct = cct
                    state.flag = Symbol()
                  }}
                  onCCTChangeComplete={async cct => {
                    state.cct = cct;
                    state.controlFlag = Symbol()
                    sendDpWithTimestamps(getGlobalParamsDp('work_mode'), () => setWorkMode(WorkMode.White)) // work_mode
                    sendDpWithTimestamps(getGlobalParamsDp('color_temp_control'), () => setCCT(cct)) // color_temp_control
                  }}
                  onBrightnessChange={brightness => {
                    state.brightness = brightness
                    state.flag = Symbol()
                  }}
                  onBrightnessChangeComplete={async brightness => {
                    state.brightness = brightness;
                    state.controlFlag = Symbol()
                    sendDpWithTimestamps(getGlobalParamsDp('work_mode'), () => setWorkMode(WorkMode.White)) // work_mode
                    sendDpWithTimestamps(getGlobalParamsDp('brightness_control'), () => setBrightness(brightness)) // brightness_control
                  }} />}
            </View>
          </Card>
        </View>
        <AdvanceList advanceData={advanceData} />
      </ScrollView>
    </Page>
  )
}

export default HomePage
