import {useDeviceId, useDp, useTimeSchedule} from '@ledvance/base/src/models/modules/NativePropsSlice';
import {Result} from '@ledvance/base/src/models/modules/Result';
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from "@ledvance/base/src/components/AdvanceCard";
import {getGlobalParamsDp, isSupportFunctions, localeNumber} from '@ledvance/base/src/utils/common'
import I18n from "@ledvance/base/src/i18n";
import {RouterKey} from "../navigation/Router";
import {createParams} from "@ledvance/base/src/hooks/Hooks";
import {useEffect, useMemo} from "react";
import {useReactive, useUpdateEffect} from "ahooks";
import {NativeApi} from "@ledvance/base/src/api/native";
import { DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import {ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers";
import {FixedTimePageParams} from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimePage';
import {useFixedTime} from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimeActions';
import {RandomTimePageParams} from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage';
import {useRandomTime} from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions';
import {
  PowerBehaviorPageParams,
  usePowerBehavior
} from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions';
import { SwitchInchingPageParams, useSwitchInching} from '@ledvance/ui-biz-bundle/src/newModules/swithInching/SwithInchingAction';
import { LightModePageParams, useLightMode } from '@ledvance/ui-biz-bundle/src/newModules/lightMode/LightModePage';
import { ChildLockPageParams, useChildLock } from '@ledvance/ui-biz-bundle/src/newModules/childLock/ChildLockPage';
import { OverchargeSwitchPageParams, useOverchargeSwitch } from '@ledvance/ui-biz-bundle/src/newModules/overchargeSwitch/OverchargeSwitchPage';
import { SwitchHistoryPageRouteParams } from "@ledvance/ui-biz-bundle/src/modules/history/HistoryPage";
import { ApplyForItem } from '@ledvance/base/src/utils/interface';

function useElectricCurrent(): number {
  const current = useDp<number, any>(getGlobalParamsDp('cur_current'))[0] || 0;
  return localeNumber(current, 1)
}

function useVoltage(): number {
  const voltage = useDp<number, any>(getGlobalParamsDp('cur_voltage'))[0] / 10 || 0;
  return localeNumber(voltage, 1);
}

function usePower(): number {
  const power = useDp<number, any>(getGlobalParamsDp('cur_power'))[0] / 10 || 0;
  return localeNumber(power, 1);
}

function useSwitch1(): [boolean, (enable: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('switch_1'));
}

function useCountdown1(): [number, (countdown1: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('countdown_1'));
}

function useRelayStatus(): [string, (relayStatus: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('relay_status'));
}

export function timeFormatToRealTime(mCountdown: number) {
  const date = new Date()
  const now = date.getTime()
  const after = new Date(now + mCountdown * 1000)
  return `${after.getHours()}:${after.getMinutes().toString().padStart(2, '0')}`
}

function useAdvanceData(): AdvancedData[] {
  const deviceId = useDeviceId();
  const [switch1] = useSwitch1()
  const [countdown1] = useCountdown1()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [fixedTimeList] = useFixedTime(getGlobalParamsDp('cycle_time'), true, true)
  const [randomTimeList] = useRandomTime(getGlobalParamsDp('random_time'), true, true)
  const [lightMode] = useLightMode(getGlobalParamsDp('light_mode'))
  const [powerBehaviors] = usePowerBehavior(['relay_status_1'])
  const [childLock] = useChildLock(getGlobalParamsDp('child_lock'))
  const [overchargeSwitch] = useOverchargeSwitch(getGlobalParamsDp('overcharge_switch'))
  const [switchInching] = useSwitchInching(getGlobalParamsDp('switch_inching'))
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    randomTimeStatus: AdvancedStatus.Disable,
    fixedTimeStatus: AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable
  });
  const defAdvancedData: AdvancedData[] = [];

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const fixedTimeStatus = useMemo(() => {
    return fixedTimeList.some(item => item.enable)
  }, [JSON.stringify(fixedTimeList)])

  const randomTimeStatus = useMemo(() => {
    return randomTimeList.some(item => item.enable)
  }, [JSON.stringify(randomTimeList)])

  defAdvancedData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: [{
          type: 'socket',
          key: I18n.getLang('manual_search_button_socket'),
          name: I18n.getLang('Onoff_button_socket'),
          dp: getGlobalParamsDp('switch_1'),
          enable: true,
        }],
        manualDataDp2Obj: () => {
          const deviceState: DeviceStateType = {
            // @ts-ignore
            deviceData: {
              type: DeviceType.LightSource,
              deviceData: {
                h: 0,
                s: 100,
                v: 100,
                brightness: 100,
                temperature: 0,
                isColorMode: false,
              },
            },
            isManual: true,
            mood: undefined
          }
          return deviceState
        },
        manualDataObj2Dp: (_: DeviceStateType, applyForList: ApplyForItem[]) => {
          return {
            [applyForList[0].dp]: applyForList[0].enable
          }
        }
      },
    },
  })

  if (isSupportFunctions('countdown_1')) {
    const params = useMemo(() => createParams<any>({
      dps: [
        {
          label: I18n.getLang('timer_ceiling_fan_headline_text'),
          value: 'socket',
          dpId: getGlobalParamsDp('countdown_1'),
          enableDp: getGlobalParamsDp('switch_1'),
          cloudKey: 'socketInfo',
          stringOn: 'timer_nightplug_active_timer_field_description_on_text',
          stringOff: 'timer_nightplug_active_timer_field_description_off_text',
        }
      ]
    }), [switch1])

    const timerSubtitles = useMemo(() => {
      if (!countdown1) {
        return []
      }
      return [I18n.formatValue(
        switch1 ? params.dps[0].stringOff : params.dps[0].stringOn,
        timeFormatToRealTime(countdown1),
      )]
    }, [countdown1, switch1])

    defAdvancedData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerSubtitles,
      statusColor: getAdvancedStatusColor(countdown1 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: {key: 'countdown_1', code: getGlobalParamsDp('countdown_1')},
      router: {
        key: RouterKey.ui_biz_timer,
        params: params
      }
    })
  }

  if(isSupportFunctions('cycle_time')){
    defAdvancedData.push({
      title: I18n.getLang('fixedTimeCycle_socket_headline'),
      subtitles: [],
      statusColor: getAdvancedStatusColor(fixedTimeStatus ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'cycle_time', code: getGlobalParamsDp('cycle_time')},
      router:{
        key: ui_biz_routerKey.ui_biz_fixed_time_new,
        params: {
          fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
          isPlug: true,
          conflictDps: {
            randomTimeDpCode: getGlobalParamsDp('random_time'),
            switchIngCode: getGlobalParamsDp('switch_inching')
          },
          applyForList: [{
            type: 'socket',
            key: I18n.getLang('manual_search_button_socket'),
            dp: getGlobalParamsDp('switch_1'),
            enable: true,
          }]
        } as FixedTimePageParams
      }
    })
  }

  if(isSupportFunctions('random_time')){
    defAdvancedData.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      subtitles: [],
      statusColor: getAdvancedStatusColor(randomTimeStatus ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'random_time', code: getGlobalParamsDp('random_time')},
      router:{
        key: ui_biz_routerKey.ui_biz_random_time_new,
        params: {
          randomTimeDpCode: getGlobalParamsDp('random_time'),
          isPlug: true,
          conflictDps: {
            fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
            switchIngCode: getGlobalParamsDp('switch_inching')
          },
          applyForList: [{
            type: 'socket',
            key: I18n.getLang('manual_search_button_socket'),
            dp: getGlobalParamsDp('switch_1'),
            enable: true,
          }]
        } as RandomTimePageParams
      }
    })
  }

  if(isSupportFunctions('light_mode')){
    const params = createParams<LightModePageParams>({
      lightModeCode: getGlobalParamsDp('light_mode')
    })
    defAdvancedData.push({
      title: I18n.getLang('matterplug_LED'),
      subtitles: [],
      statusColor: getAdvancedStatusColor(lightMode ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'light_mode', code: getGlobalParamsDp('light_mode')},
      router: {
        key: ui_biz_routerKey.ui_biz_light_mode,
        params
      },
    })
  }

  if(isSupportFunctions('relay_status_1')){
    const params = createParams<PowerBehaviorPageParams>({
      powerBehaviorKeys: ['relay_status_1']
    })
    defAdvancedData.push({
      title: I18n.getLang('sockets_specific_settings_relay_status'),
      statusColor: getAdvancedStatusColor(powerBehaviors.some(item => !!item) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'relay_status', code: getGlobalParamsDp('relay_status') },
      router: {
        key: RouterKey.ui_biz_power_behavior_plug,
        params
      },
    })
  }

  if(isSupportFunctions('child_lock')){
    const params = createParams<ChildLockPageParams>({
      childLockCode: getGlobalParamsDp('child_lock')
    })
    defAdvancedData.push({
      title: I18n.getLang('sockets_specific_settings_child_lock'),
      subtitles: [],
      statusColor: getAdvancedStatusColor(childLock ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'child_lock', code: getGlobalParamsDp('child_lock')},
      router: {
        key: ui_biz_routerKey.ui_biz_child_lock,
        params
      },
    })
  }

  if(isSupportFunctions('overcharge_switch')){
    const params = createParams<OverchargeSwitchPageParams>({
      overchargeSwitchCode: getGlobalParamsDp('overcharge_switch')
    })
    defAdvancedData.push({
      title: I18n.getLang('switch_overcharge_headline_text'),
      subtitles: [],
      statusColor: getAdvancedStatusColor(overchargeSwitch ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'overcharge_switch', code: getGlobalParamsDp('overcharge_switch')},
      router: {
        key: ui_biz_routerKey.ui_biz_overcharge_switch,
        params
      },
    })
  }

  if (isSupportFunctions('switch_inching')) {
    const params = createParams<SwitchInchingPageParams>({
      switchIngCode: getGlobalParamsDp('switch_inching'),
      countdownCode: getGlobalParamsDp('countdown_1'),
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_time'),
        fixedTimeDpCode: getGlobalParamsDp('cycle_time')
      }
    })
    defAdvancedData.push({
      title: I18n.getLang('sockets_specific_settings_switch_inching'),
      statusColor: getAdvancedStatusColor(switchInching.some(item => item.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      router: {
        key: ui_biz_routerKey.ui_biz_switch_inching,
        params
      },
    })
  }

  const historyParams = createParams<SwitchHistoryPageRouteParams>({
    dpIds: [getGlobalParamsDp('switch_1')],
    getActionsText: (dpData:any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
  })
  defAdvancedData.push({
    title: I18n.getLang('history_socket_headline_text'),
    router: {
      key: ui_biz_routerKey.ui_biz_history,
      params: historyParams
    }
  })

  return defAdvancedData;
}

export {
  useElectricCurrent,
  useVoltage,
  usePower,
  useSwitch1,
  useCountdown1,
  useAdvanceData,
  useRelayStatus,
};
