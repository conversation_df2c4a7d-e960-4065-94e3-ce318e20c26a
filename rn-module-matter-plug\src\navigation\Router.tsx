import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers"
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import NewTimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import NewFixedTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/Router'
import NewRandomTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/randomTime/Router'
import {PlugPowerOnBehaviorPageRouters} from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/Router'
import EnergyConsumptionPageRouters from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/Router'
import SwitchInchingPageRouters from '@ledvance/ui-biz-bundle/src/newModules/swithInching/Router'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import LightModePageRouters from '@ledvance/ui-biz-bundle/src/newModules/lightMode/Router'
import ChildLockPageRouters from '@ledvance/ui-biz-bundle/src/newModules/childLock/Router'
import OverchargeSwitchPageRouters from '@ledvance/ui-biz-bundle/src/newModules/overchargeSwitch/Router'

export const RouterKey = {
  main: 'main',
  ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...TimerPageRouters,
  ...NewTimeSchedulePageRouters,
  ...NewFixedTimePageRouters,
  ...NewRandomTimePageRouters,
  ...PlugPowerOnBehaviorPageRouters,
  ...EnergyConsumptionPageRouters,
  ...SwitchInchingPageRouters,
  ...HistoryPageRouters,
  ...LightModePageRouters,
  ...ChildLockPageRouters,
  ...OverchargeSwitchPageRouters
]
