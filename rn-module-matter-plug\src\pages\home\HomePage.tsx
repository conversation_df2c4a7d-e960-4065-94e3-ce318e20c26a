import React, {useEffect} from 'react'
import { useNavigation, useIsFocused } from '@react-navigation/core'
import Page from '@ledvance/base/src/components/Page'
import {
    useDeviceId,
    useDeviceInfo,
    useFamilyName
} from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import { NativeApi, queryDpIds } from '@ledvance/base/src/api/native'
import Spacer from '@ledvance/base/src/components/Spacer'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import I18n from '@ledvance/base/src/i18n'
import {ScrollView, View, StyleSheet, Text} from "react-native";
import {Utils} from "tuya-panel-kit";
import {useReactive, useInterval} from "ahooks";
import {useAdvanceData, useElectricCurrent, usePower, useSwitch1, useVoltage} from "../../hooks/FeatureHooks";
import {getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import {ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers";
import { EnergyConsumptionPageProps } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionPage'
import { getEnergyGenerationValue } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionActions'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const HomePage = (props: {theme: any}) => {
    const deviceInfo = useDeviceInfo()
    const devId = useDeviceId()
    const familyName = useFamilyName()
    const navigation = useNavigation()
    const isFocused = useIsFocused()
    const [switch1, setSwitch1] = useSwitch1()
    const power = usePower()
    const electricCurrent = useElectricCurrent()
    const voltage = useVoltage()
    const advanceData = useAdvanceData()
    const state = useReactive({
      loading: false,
      advancedDataList: [],
      isGeneration: false
    })

  useInterval(() => {
      if (isFocused) {
        const jsonData = JSON.stringify([getGlobalParamsDp('cur_current'), getGlobalParamsDp('cur_power'), getGlobalParamsDp('cur_voltage')])
        queryDpIds(jsonData, devId).then()
      }
    },
    3000,
    {immediate: true}
  )

  useEffect(() => {
    getEnergyGenerationValue(devId).then(data => {
      state.isGeneration = !!data?.generationMode
    })
  }, [isFocused])

    const unitDivision = (str) => {
        if (!str) { return ['', ''] }
        const strIndex = str.indexOf('(') || str.indexOf('（')
        const unit = str.substring(strIndex)
        const name = str.split(unit)[0]
        return [name, unit]
    }

    const styles = StyleSheet.create({
        consumedEnergyCard: {
            marginHorizontal: cx(24),
        },
        consumedEnergyCardTitle: {
            marginHorizontal: cx(16),
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            // fontFamily: 'helvetica_neue_lt_std_bd',
            fontWeight: 'bold',
        },
        consumedEnergyContent: {
            flexDirection: 'row',
        },
        subContent: {
            flex: 1,
            alignItems: 'center',
            marginBottom: cx(9)
        },
        valueText: {
            fontSize: cx(24),
            fontWeight: 'bold',
            color: props.theme?.global.secondBrand,
        },
        titleText: {
            fontFamily: 'helvetica_neue_lt_std_roman',
            fontSize: cx(14),
            color: props.theme?.global.secondFontColor,
            textAlign: 'center',
        },
    })

    const ConsumedEnergyItem = (props: { value: number, unit: string }) => {
        return (
          <View style={styles.subContent}>
              <Text style={styles.valueText}>{(props.value) || 0}</Text>
              <Spacer height={cx(4)} />
              <Text style={styles.titleText}>
                  {unitDivision(props.unit)[0]}
              </Text>
              <Text style={styles.titleText}>
                  {unitDivision(props.unit)[1]}
              </Text>
          </View>
        )
    }

    return (
        <Page
            backText={familyName}
            onBackClick={NativeApi.back}
            headlineText={deviceInfo.name}
            headlineIcon={res.ic_more}
            onHeadlineIconClick={() => {
                NativeApi.toDeviceSettingsPage(deviceInfo.devId)
            }}
            loading={state.loading}
        >
            <ScrollView nestedScrollEnabled={true}>
                <View>
                    <Spacer />
                    <Card
                      style={styles.consumedEnergyCard}
                      onPress={() => navigation.navigate(ui_biz_routerKey.ui_biz_energy_consumption, {
                          electricDpCode: getGlobalParamsDp('cur_current'),
                          powerDpCode: getGlobalParamsDp('cur_power'),
                          voltageDpCode: getGlobalParamsDp('cur_voltage'),
                          addEleDpCode: getGlobalParamsDp('add_ele'),
                      } as EnergyConsumptionPageProps)}>
                        <Spacer height={cx(16)} />
                      <Text style={styles.consumedEnergyCardTitle}>{I18n.getLang(state.isGeneration ? 'sockets_headline_power' : 'sockets_ce')}</Text>
                        <Spacer height={cx(18)} />
                        <View style={styles.consumedEnergyContent}>
                            <ConsumedEnergyItem
                              value={power}
                              unit={I18n.getLang('consumption_data_field2_value_text1')} />
                            <ConsumedEnergyItem
                              value={electricCurrent}
                              unit={I18n.getLang('consumption_data_field2_value_text2')} />
                            <ConsumedEnergyItem
                              value={voltage}
                              unit={I18n.getLang('consumption_data_field2_value_text3')} />
                        </View>
                        <Spacer height={cx(17)} />
                    </Card>
                    <Spacer />
                    <Card style={{ marginHorizontal: cx(24) }}>
                        <LdvSwitch
                          title={I18n.getLang('Onoff_button_socket')}
                          color={props.theme?.card.background}
                          colorAlpha={1}
                          enable={switch1}
                          setEnable={async (enable: boolean) => {
                              state.loading = true
                              await setSwitch1(enable)
                              state.loading = false
                          }} />
                    </Card>
                    <Spacer height={cx(19)} />
                    <AdvanceList advanceData={advanceData} />
                    <Spacer height={cx(40)} />
                </View>
            </ScrollView>
        </Page>
    )
}

export default withTheme(HomePage)
