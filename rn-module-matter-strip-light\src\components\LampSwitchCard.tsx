import React from "react";
import { FlatList, TouchableOpacity, View, Image } from "react-native";
import ColorAdjustView, { ColorAdjustViewProps } from "@ledvance/base/src/components/ColorAdjustView";
import ColorTempAdjustView, { ColorTempAdjustViewProps } from "@ledvance/base/src/components/ColorTempAdjustView";
import Spacer from "@ledvance/base/src/components/Spacer";
import { TabBar, Utils } from "tuya-panel-kit";
import { ColorList } from "./ColorList";
import { ColorDisk } from "@tuya/tuya-panel-lamp-sdk";
import res from "@ledvance/base/src/res";
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

type TabsNode = {
  key: number | string
  title: string
}
interface LampSwitchCardView extends ColorAdjustViewProps, ColorTempAdjustViewProps {
  theme?: ThemeType
  lampTabs: TabsNode[]
  activeKey: number | string
  onActiveKeyChange: (key: number | string) => void
  colorDiskActiveKey? : number | undefined
  onColorDiskChange?: (color: string[], idx: number) => void
}

const LampSwitchCard = (props: LampSwitchCardView) => {
  return (
    <View>
      <TabBar
        type='radio'
        tabs={props.lampTabs}
        style={{ borderRadius: cx(8), backgroundColor: props.theme?.segment.background, height: cx(40), marginHorizontal: cx(16) }}
        tabTextStyle={{ color: props.theme?.segment.fontColor }}
        tabActiveTextStyle={{ color: props.theme?.segment.fontColor, fontWeight: 'bold' }}
        activeColor={props.theme?.segment.active}
        activeKey={props.activeKey}
        onChange={props.onActiveKeyChange}
      />
      {
        props.activeKey === 1 &&
        <>
          <Spacer height={cx(10)} />
          <ColorAdjustView
            h={props.h}
            s={props.s}
            v={props.v}
            minSaturation={1}
            reserveSV={true}
            onHSVChange={props.onHSVChange}
            onHSVChangeComplete={props.onHSVChangeComplete} />
          <Spacer />
        </>
      }
      {
        props.activeKey === 0 &&
        <>
          <Spacer height={cx(10)} />
          <ColorTempAdjustView
            minBrightness={1}
            isSupportTemperature={props.isSupportTemperature}
            isSupportBrightness={props.isSupportBrightness}
            colorTemp={props.colorTemp}
            brightness={props.brightness}
            onCCTChange={props.onCCTChange}
            onCCTChangeComplete={props.onCCTChangeComplete}
            onBrightnessChange={props.onBrightnessChange}
            onBrightnessChangeComplete={props.onBrightnessChangeComplete} />
          <Spacer />
        </>
      }
      {
        props.activeKey === 3 &&
        <FlatList
          numColumns={6}
          style={{
            marginHorizontal: cx(16),
          }}
          data={ColorList}
          renderItem={({ item, index }) => {
            return (
              <TouchableOpacity
                accessibilityLabel="ColorDisk"
                accessibilityHint={`${index}`}
                accessibilityState={{ checked: props.colorDiskActiveKey === index }}
                onPress={() => {
                  props.onColorDiskChange && props.onColorDiskChange(item, index)
                }}
                style={{
                  width: '16.66%', alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <ColorDisk
                  radius={cx(20)}
                  colors={item}
                />
                {props.colorDiskActiveKey === index && <View style={{ width: cx(44), height: cx(44), borderRadius: cx(44), borderColor: props.theme?.button.primary, borderWidth: cx(2), alignItems: 'center', justifyContent: 'center', position: 'absolute' }}>
                  <Image source={{ uri: res.ic_check }} style={{ width: cx(40), height: cx(40), tintColor: props.theme?.button.fontColor }} />
                </View>}
              </TouchableOpacity>
            )
          }}
          ListHeaderComponent={() => <Spacer height={cx(10)} />}
          ItemSeparatorComponent={() => <Spacer height={cx(10)} />}
          ListFooterComponent={() => <Spacer height={cx(10)} />}
          keyExtractor={(_, idx) => idx.toString()}
        />
      }
    </View>
  )
}

export default withTheme(LampSwitchCard)
