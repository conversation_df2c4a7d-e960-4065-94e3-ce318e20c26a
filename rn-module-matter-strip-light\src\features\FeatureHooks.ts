import {useCallback, useEffect, useState} from 'react'
import {
  useDeviceId,
  useDeviceInfo,
  useDp,
  useDps,
  useFlagMode,
  useTimeSchedule
} from '@ledvance/base/src/models/modules/NativePropsSlice'
import {getTuyaCloudData, NativeApi} from '@ledvance/base/src/api/native'
import {Result} from '@ledvance/base/src/models/modules/Result'
import {avgSplit, ColorUtils, nToHS, parseJSON} from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import I18n from '@ledvance/base/src/i18n/index'
import {RouterKey} from '../navigation/Router'
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from '@ledvance/base/src/components/AdvanceCard'
import {MoodInfo, MoodPageParams} from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface'
import {mapFloatToRange, mapValueToRatio} from '@ledvance/base/src/utils'
import {cloneDeep, flatMap, range} from 'lodash'
import {useCreation, useReactive, useThrottleFn, useUpdateEffect} from 'ahooks'
import {ColorTool, Combination, drawToolFormat, drawToolParse, WhiteTool} from './ProtocolConvert'
import {Node} from '@ledvance/base/src/components/DrawToolView'
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import {MusicPageRouterParams} from '@ledvance/ui-biz-bundle/src/modules/music/MusicPage'
import {cctToColor} from '@ledvance/base/src/utils/cctUtils'
import {lampApi} from '@tuya/tuya-panel-api'
import {stripDp2Obj, stripObj2Dp} from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import {Buffer} from 'buffer'
import {FlagPageProps} from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage'
import {getFlagMode, saveFlagMode} from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import res from '@ledvance/base/src/res'
import {TimeSchedulePageParams} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage'
import {ApplyForItem, WorkMode} from '@ledvance/base/src/utils/interface'
import {getGlobalParamsDp, isSupportFunctions} from '@ledvance/base/src/utils/common'
import {
  DeviceStateType,
  DeviceType,
  StripLightData
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import {timeFormat} from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch'))
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(getGlobalParamsDp('work_mode'))
  const [workMode, setWokMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWokMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWokMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4)
  const s = str.substring(4, 8)
  const v = str.substring(8, 12)
  const brightness = str.substring(12, 16)
  const temperature = str.substring(16, 20)
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16) / 10,
    v: parseInt(v, 16) / 10,
    brightness: parseInt(brightness, 16) / 10,
    temperature: parseInt(temperature, 16) / 10
  }
}

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80))
}

export const getHexByTemp = (temp: number, bright?: number) => {
  const rgb = cctToColor(Math.round(temp)).match(/\d+/g)
  return `#${getBrightOpacity(bright || 100)}${ColorUtils.rgb2hex(rgb[0], rgb[1], rgb[2]).slice(1)}`
}

const dpMinHSColorSet = 0
const dpMaxHSColorSet = 254
export function useHSColorSet(): [number, number, (h: number, s: number) => Promise<Result<any>>] {
  const [hsHex, setHsHex] = useDp<string, any>(getGlobalParamsDp('hs_color_set'))

  const { h, s } = dpHex2hs(hsHex)

  const setHSColorSet = useCallback(async (h: number, s: number) => {
    const hsHexStr = hs2dpHex(h, s)
    return await setHsHex(hsHexStr)
  }, [setHsHex])

  return [h, s, setHSColorSet]
}

function dpHex2hs(hsHex: string) {
  const hsHexFixed = !!hsHex && hsHex.length == 4 ? hsHex : '0000'

  const hDpV = parseInt(hsHexFixed.substring(0, 2), 16)
  const sDpV = parseInt(hsHexFixed.substring(2), 16)

  const h = mapValueToRatio(hDpV, dpMinHSColorSet, dpMaxHSColorSet)
  const s = mapValueToRatio(sDpV, dpMinHSColorSet, dpMaxHSColorSet) * 100
  return {
    h: Math.round(mapFloatToRange(h, 0, 360)),
    s: Math.round(s),
  }
}

function hs2dpHex(h: number, s: number): string {
  const hDpv = Math.round(mapFloatToRange(mapValueToRatio(h, 0, 360), dpMinHSColorSet, dpMaxHSColorSet))
  const sDpv = Math.round(mapFloatToRange(s / 100, dpMinHSColorSet, dpMaxHSColorSet))
  return `${hDpv.toString(16).padStart(2, '0')}${sDpv.toString(16).padStart(2, '0')}`.toUpperCase()
}

type ColorType = {
  h?: number
  s?: number
  v?: number
  b?: number
  t?: number
}
type PaintColorType = [any, (data: ColorTool | WhiteTool) => Promise<Result<any>>, Node[], (color: ColorType | string[], idxList: number[]) => void]
const whiteHex = '#FEAC5B'
export function usePaintColorData(): PaintColorType {
  const [dps, setPaintData]: [string, (hex: Record<string, any>) => Promise<Result<any>>] = useDps()
  const paintData = dps[getGlobalParamsDp('paint_colour_data')]
  const devId = useDeviceId()
  const ledNum = useLightPixel()
  const [workMode] = useWorkMode()
  const [flagMode, setFlagMode] = useFlagMode()
  const state = useReactive({
    cloudData: [] as any[],
    cloudHex: '',
    ledNum,
    sendingDp: [] as string[]
  })

  const hsv = useCreation(() => {
    return drawToolParse(paintData)
  }, [paintData])

  useEffect(() => {
    getTuyaData().then()
  }, [])

  useUpdateEffect(() => {
    state.ledNum = ledNum
  }, [ledNum])

  useUpdateEffect(() => {
    if (state.sendingDp.includes(paintData)) {
      state.sendingDp = state.sendingDp.filter(dp => dp === paintData)
      return
    }
    getTuyaCloud.run()

  }, [paintData])


  const getTuyaData = async () => {
    if (hsv.daubType === 0) {
      switch (hsv.adjustCode) {
        case 0:
          let { bright, temp } = hsv as WhiteTool
          temp = Math.round(temp / 10)
          state.cloudData = Array.from({ length: state.ledNum }, () => ({ color: getHexByTemp(temp, bright) }))
          state.cloudHex = `000000000000${nToHS(bright * 10, 4)}${nToHS(temp * 10, 4)}`.repeat(state.ledNum)
          break;
        case 3:
          const { colors } = hsv as Combination
          const colorsNode = flatMap(range(state.ledNum), (index) => ({
            color: colors[index % colors.length]
          }))
          state.cloudData = colorsNode
          state.cloudHex = colorsNode.map(c => {
            const v = ColorUtils.hex2hsv(c.color)
            return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
          }).join('')
          break;
        default:
          const { h, s, v } = hsv as ColorTool
          state.cloudData = Array.from({ length: state.ledNum }, () => ({ color: `#${getBrightOpacity(v)}${ColorUtils.hsv2hex(h, Math.max(s, 10), 100).slice(1)}` }))
          state.cloudHex = `${nToHS(h, 4)}${nToHS(s * 10, 4)}${nToHS(v * 10, 4)}00000000`.repeat(state.ledNum)
      }

    } else {
      const res = await getTuyaCloudData(devId)
      if (res && res['lights_0']) {
        const v = parseJSON(res['lights_0'])
        const isString = typeof v === 'string'
        const nodeHex = isString ? v : v?.data
        if (nodeHex) {
          state.cloudData = getColorFn(nodeHex)
          state.cloudHex = nodeHex
        } else {
          state.cloudHex = `000000000000${nToHS(0, 4)}${nToHS(100 * 10, 4)}`.repeat(state.ledNum)
        }
      } else {
        state.cloudHex = `000000000000${nToHS(0, 4)}${nToHS(100 * 10, 4)}`.repeat(state.ledNum)
      }
    }
  }

  const saveCloudData = async (data: string) => {
    const res = await lampApi.generalApi.saveCloudConfig('lights_0', data)
    return res
  }

  const { run } = useThrottleFn(saveCloudData, { wait: 300, leading: true })
  const getTuyaCloud = useThrottleFn(getTuyaData, { wait: 400, leading: true })

  const getColorFn = (hex: string, n: number = 20) => {
    const colorDataList = avgSplit(hex, n).map(hex => {
      return getColorData(hex)
    })
    return colorDataList.map(color => {
      if (color.v !== 0) {
        return {
          color: `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
        }
      } else {
        return {
          color: Object.values(color).every(item => item === 0) ? `#20${whiteHex.slice(1)}` : getHexByTemp(color.temperature, color.brightness)
        }
      }
    })
  }

  const addToSendDp = (dp: string) =>{
    const cloneArray = [...state.sendingDp]
    if (state.sendingDp.length >= 3){
      cloneArray.shift()
      state.sendingDp = cloneArray
    }else{
      cloneArray.push(dp)
      state.sendingDp = cloneArray
    }
  }


  const setDrawToolFn = async (data: ColorTool | WhiteTool | Combination) => {
    if (flagMode?.flagMode) {
      setFlagMode({
        ...flagMode,
        flagMode: false
      })
      saveFlagMode(devId, JSON.stringify({
        ...flagMode,
        flagMode: false
      })).then()
    }
    await run(state.cloudHex)
    const hex = drawToolFormat({
      ...data,
      num: data.adjustCode === 3 ? 20 : state.ledNum // 当类型为Combination时，num必须设置为20，否则设备会显示异常
    })
    const { adjustCode } = data
    if(data.daubType !== 0){
      addToSendDp(hex)
    }
    const extraDp = {
      [getGlobalParamsDp('paint_colour_data')]: hex
    }
    if (workMode === WorkMode.Music) {
      MusicManager.close()
    }
    if ((adjustCode !== 0 && workMode !== WorkMode.Colour) || (adjustCode === 0 && workMode !== WorkMode.White)) {
      // await setWorkMode(adjustCode === 0 ? WorkMode.White : WorkMode.Colour)
      extraDp[getGlobalParamsDp('work_mode')] = adjustCode === 0 ? WorkMode.White : WorkMode.Colour
    }

    return setPaintData(extraDp)
  }

  const setColorFn = useCallback(async (color: ColorType | string[], idxList: number[]) => {
    if (Array.isArray(color)) {
      const colors = flatMap(range(state.ledNum), (index) => ({
        color: color[index % color.length]
      }))
      state.cloudData = colors
      state.cloudHex = colors.map(c => {
        const v = ColorUtils.hex2hsv(c.color)
        return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
      }).join('')
    } else {
      const newNodeList = cloneDeep(state.cloudData).map((item, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            if (color.h !== undefined && color.s !== undefined && color.v !== undefined) {
              item.color = `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
            } else {
              item.color = getHexByTemp(color.t as number, color.b)
            }
          } else {
            item.color = `#20${whiteHex.slice(1)}`
          }
        }
        return item
      })
      const newHex = avgSplit(state.cloudHex, 20).map((hex, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            if (color.h !== undefined && color.s !== undefined && color.v !== undefined) {
              return `${nToHS(color.h, 4)}${nToHS(color.s * 10, 4)}${nToHS(color.v * 10, 4)}00000000`
            } else {
              return `000000000000${nToHS(color.b as number * 10, 4)}${nToHS(color.t as number * 10, 4)}`
            }
          } else {
            return '00000000000000000000'
          }
        }
        return hex
      }).join('')
      state.cloudData = newNodeList
      state.cloudHex = newHex
    }
  }, [state.ledNum, state.cloudData, state.cloudHex])

  return [hsv, setDrawToolFn, state.cloudData, setColorFn]
}

const dpMinBrightnessControl = 1
const dpMaxBrightnessControl = 254
export function useBrightnessControl(): [number, (value: number) => Promise<Result<any>>] {
  const [bc, setBc] = useDp<number, any>(getGlobalParamsDp('brightness_control'))
  const ratio = mapValueToRatio(bc, dpMinBrightnessControl, dpMaxBrightnessControl)
  const setBrightnessControl = useCallback(async (value: number) => {
    return await setBc(brightness2dpNumber(value))
  }, [setBc])
  const brightness = Math.max(Math.round(ratio * 100), 1)
  return [brightness, setBrightnessControl]
}

function brightness2dpNumber(brightness: number): number {
  const bv = mapFloatToRange(brightness / 100, dpMinBrightnessControl, dpMaxBrightnessControl)
  return Math.round(bv)
}

const dpMinColorTempControl = 153
const dpMaxColorTempControl = 370

export function useColorTempControl(): [number, (value: number) => Promise<Result<any>>] {
  const [ctc, setCTC] = useDp<number, any>(getGlobalParamsDp('color_temp_control'))
  const ratio = mapValueToInverseRatio(ctc, dpMinColorTempControl, dpMaxColorTempControl)
  const setColorTempControl = useCallback(async (value: number) => {
    return await setCTC(cct2dpNumber(value))
  }, [setCTC])
  return [Math.round(ratio * 100), setColorTempControl]
}

function mapValueToInverseRatio(value: number, min: number, max: number): number {
  // 确保 value 在 [min, max] 范围内
  value = Math.max(min, Math.min(max, value))

  // 计算与给定范围 [min, max] 相对应的比例
  return 1 - (value - min) / (max - min)
}

function mapInverseRatioToValue(inverseRatio: number, min: number, max: number): number {
  // 将反比例映射回值
  return min + (1 - inverseRatio) * (max - min)
}

function cct2dpNumber(cct: number): number {
  const dpValue = mapInverseRatioToValue(cct / 100, dpMinColorTempControl, dpMaxColorTempControl)
  return Math.round(dpValue)
}

export function useLightPixel(): number {
  return useDp(getGlobalParamsDp('light_pixel'))[0] as number
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('countdown'))
}

export function isSupportBrightness(): boolean {
  return isSupportFunctions('brightness_control')
}

export function isSupportTemperature(): boolean {
  return isSupportFunctions('color_temp_control')
}

export function isSupportColor(): boolean {
  return isSupportFunctions('hs_color_set')
}

export function isSupportMood(): boolean {
  return isSupportFunctions('dreamlight_scene_mode')
}

export function isSupportMusic(): boolean {
  return isSupportFunctions('music_data')
}

export function isSupportDreamMusic(): boolean {
  return isSupportFunctions('dreamlightmic_music_data')
}

export function isSupportTimer(): boolean {
  return isSupportFunctions('countdown')
}

const displayFlagPids = ['uodzotak0iagw5xs']
const reverseFlagPids = ['clilldwdqzsp1r15']

export function useAdvancedData(): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const deviceId = useDeviceId()
  const deviceInfo = useDeviceInfo()
  const [workMode] = useWorkMode()
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const ledNum = useLightPixel()
  const [flagMode, setFlagMode] = useFlagMode()
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    ledNum
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })

      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          const flagData = parseJSON(res.data)
          setFlagMode(flagData)
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    if (workMode !== WorkMode.Colour && flagMode?.flagMode) {
      setFlagMode({
        ...flagMode,
        flagMode: false,
      })
      saveFlagMode(deviceId, JSON.stringify({
        ...flagMode,
        flagMode: false
      })).then()
    }

    if (workMode !== WorkMode.Music) {
      MusicManager.close()
    }
  }, [workMode])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  useUpdateEffect(() => {
    state.ledNum = ledNum
  }, [ledNum])

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.StripLight,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: isSupportColor(),
          activeKey: 1,
          colorDiskActiveKey: 0,
          colors: []
        },
      },
      isManual: !(
        dps.hasOwnProperty(getGlobalParamsDp('scene_data')) || dps.hasOwnProperty(getGlobalParamsDp('mix_light_scene')) || dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))
      ),
      mood: undefined,
    };
    if (dps.hasOwnProperty(getGlobalParamsDp('paint_colour_data')) || dps.hasOwnProperty(getGlobalParamsDp('paint_color'))){
      const paintDp = getGlobalParamsDp('paint_colour_data') ? dps[getGlobalParamsDp('paint_colour_data')] : dps[getGlobalParamsDp('paint_color')]
      const paintColor = Buffer.from(paintDp, 'base64').toString('hex')
      const paintData: any = drawToolParse(paintColor)
      deviceState.deviceData.deviceData = {
        ...deviceState.deviceData.deviceData,
        ...paintData,
        s: paintData.hasOwnProperty('s') ? Math.trunc(paintData.s) / 10 : 100,
        v: paintData.hasOwnProperty('v') ? Math.trunc(paintData.v) / 10 : 100,
        brightness: paintData.hasOwnProperty('bright') ? Math.trunc(paintData.bright) / 10 : 100,
        temperature: paintData.hasOwnProperty('temp') ? Math.trunc(paintData.temp) / 10 : 0,
        colors: paintData?.colors || [],
        activeKey: paintData.adjustCode ?? 1,
        colorDiskActiveKey: paintData.colorDiskActiveKey ?? 0,
      }
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))) {
      const mood = stripDp2Obj(Buffer.from(dps[getGlobalParamsDp('dreamlight_scene_mode')], 'base64').toString('hex'));
      deviceState.mood = cloneDeep(mood);
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('dreamlight_scene_mode')] = Buffer.from(stripObj2Dp(mood as MoodInfo), 'hex').toString(
          'base64'
        );
        manualDps[getGlobalParamsDp('switch')] = true;
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene;
      } else {
        const device = deviceData.deviceData as StripLightData
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
        });
        if (manualDps[getGlobalParamsDp('switch')]) {
          const paint_color = drawToolFormat({
            ...device,
            temp: device.temperature,
            bright: device.brightness,
            adjustCode: device.activeKey ?? 1,
            version: 0,
            daubType: 0,
            effect: 1,
            num: ledNum
          })
          if (getGlobalParamsDp('paint_colour_data')){
            manualDps[getGlobalParamsDp('paint_colour_data')] = Buffer.from(paint_color, 'hex').toString('base64')
          }
          if (getGlobalParamsDp('paint_color')){
            manualDps[getGlobalParamsDp('paint_color')] = Buffer.from(paint_color, 'hex').toString('base64')
          }
          manualDps[getGlobalParamsDp('work_mode')] = device.activeKey === 0 ? WorkMode.White : WorkMode.Colour;
        }
      }

      return manualDps;
    },
    []
  );

  if (!displayFlagPids.includes(deviceInfo.productId)) {
    advanceData.push({
      title: I18n.getLang('Feature_devicepanel_flags'),
      dp: { key: 'flag', code: 'flag' },
      icons: res.flag_icon,
      statusColor: getAdvancedStatusColor(flagMode?.flagMode && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      router: {
        key: RouterKey.ui_biz_flag_page,
        params: {
          isSupportColor: isSupportColor(),
          isStripLight: true,
          switchLedCode: getGlobalParamsDp('switch'),
          workModeCode: getGlobalParamsDp('work_mode'),
          drawToolLight: {
            drawToolCode: getGlobalParamsDp('paint_colour_data'),
            drawToolObj2dp: (colors) => {
              const newColors = reverseFlagPids.includes(deviceInfo.productId) ? [...colors].reverse() : [...colors]
              const def = {
                version: 0,
                num: ledNum,
                daubType: 1,
                effect: 0,
                adjustCode: 1,
              }
              const distribute = distributeColorsEvenly(newColors, ledNum)
              const hexArray = newColors.slice(0, distribute.length).map((c, idx) => {
                return drawToolFormat({
                  ...def,
                  h: c.h,
                  s: c.s,
                  v: c.v,
                  selected: distribute[idx] as any
                })
              })
              return hexArray
            },
            drawToolDp2Obj: () => {
              return []
            }
          }
        } as FlagPageProps
      }
    })
  }

  if (isSupportMood()) {
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'dreamlight_scene_mode', code: getGlobalParamsDp('dreamlight_scene_mode')},
      router: {
        key: RouterKey.ui_biz_mood,
        params: {
          switchLedDp: getGlobalParamsDp('switch'),
          mainDp: getGlobalParamsDp('dreamlight_scene_mode'),
          mainWorkMode: getGlobalParamsDp('work_mode'),
          mainSwitch: getGlobalParamsDp('switch'),
          isSupportColor: isSupportColor(),
          isStripLight: true,
          featureId: 'dreamlight_scene_mode'
        } as MoodPageParams,
      },
    })
  }

  const lightApplyFor: ApplyForItem[] = [
    {
      type: 'light',
      name: I18n.getLang('Onoff_button_socket'),
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch'),
      enable: true,
    },
  ];

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: lightApplyFor,
        isSupportBrightness: isSupportBrightness(),
        isSupportColor: isSupportColor(),
        isSupportTemperature: isSupportTemperature(),
        isSupportMood: isSupportMood(),
        isStripLight: true,
        featureId: 'dreamlight_scene_mode',
        manualDataDp2Obj,
        manualDataObj2Dp
      } as TimeSchedulePageParams
    }
  })

  if (isSupportMusic()) {
    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'music_data', code: getGlobalParamsDp('music_data')},
      router: {
        key: RouterKey.ui_biz_music,
        params: {
          switch_led: getGlobalParamsDp('switch'),
          work_mode: getGlobalParamsDp('work_mode'),
          music_data: getGlobalParamsDp('music_data'),
          isMixRGBWLamp: false,
          dreamMusicDp: isSupportDreamMusic() ? getGlobalParamsDp('dreamlightmic_music_data') : undefined
        } as MusicPageRouterParams,
      },
    })
  }

  if (isSupportTimer()) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'countdown', code: getGlobalParamsDp('countdown')},
      router: {
        key: RouterKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: getGlobalParamsDp('countdown'),
              enableDp: getGlobalParamsDp('switch'),
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }

  return advanceData
}

function distributeColorsEvenly(colors: any[], num: number){
  const colorLen = colors.length
  const colorDistribution = colors.map(() => Math.floor(num / colorLen))
  const overDistribution = num >= colorLen ? num % colorLen : 0
  const finalDistribution = colorDistribution.map((dist, idx) => {
    if (overDistribution !== 0) {
      // 判断余数是否为奇数
      const isEven = overDistribution % 2 === 0
      const midIdx = Math.floor(colorLen / 2)
      let startIdx = midIdx
      let endIdx = midIdx
      const m = Math.floor(overDistribution / 2)
      if (overDistribution > 1) {
        startIdx = midIdx - m
        endIdx = midIdx + m - (isEven ? 1 : 0)
      }
      if (idx >= startIdx && idx <= endIdx) {
        return dist + 1
      }
      return dist
    }
    if (idx <= num - 1 && dist === 0) {
      return 1
    }
    return dist
  }).filter(v => v !== 0)
  const getReduceIndex = (numList: number[], idx: number) => {
    return numList.reduce((pre, cur, index) => {
      if (idx > index) {
        return pre + cur
      }
      return pre
    }, 0)
  }
  console.log(finalDistribution, '< --- finalDistribution')
  const distributeIndex = finalDistribution.map((n, idx) => {
    const index = getReduceIndex(finalDistribution, idx)
    return range(index, index + n)
  })
  return distributeIndex
}

