import { ColorUtils, avgSplit, nToSH, sToN } from "@tuya/tuya-panel-lamp-sdk/lib/utils"
import { ColorList } from "components/ColorList"
import { chunk } from 'lodash'

interface Common {
  version: number
  adjustCode: number
  daubType: number,
  effect: number,
  num: number
}

export interface ColorTool extends Common {
  h: number
  s: number
  v: number
  part?: number
  selected?: string[]
}

export interface WhiteTool extends Common {
  bright: number
  temp: number
}

export interface Combination extends Common {
  colors: string [],
  colorDiskActiveKey: number
}

export const getDefaultDrawTool = () =>{
  return {
    version: 0, 
    daubType: 0, 
    effect: 0,
    adjustCode: 1,
    h: 0,
    s: 100,
    v: 100
  }
}

export function drawToolParse(hex: string) {
  if(!hex) return getDefaultDrawTool()
  const version = parseInt(hex.substring(0, 2), 16)
  const adjustCode = parseInt(hex.substring(2, 4), 16)
  const effect = parseInt(hex.substring(2, 4), 16)
  const num = parseInt(hex.substring(6, 8), 16)
  const daubType = parseInt(hex.substring(8, 10), 16)
  const common = {
    version,
    adjustCode,
    effect,
    num,
    daubType
  }
  if(adjustCode === 0){
    const bright = parseInt(hex.substring(10, 14), 16)
    const temp = parseInt(hex.substring(14, 18), 16)
    return {
      ...common,
      bright,
      temp
    }
  }else if(adjustCode === 3){
    const colorListHex = ColorList.map(colors =>{
      return colors.map(c => {
        const color = ColorUtils.hex2hsv(c)
        return `${nToSH(Math.round(color[0]),4)}${nToSH(Math.round(color[1] * 10),4)}${nToSH(Math.round(color[2] * 10),4)}`
      }).join('')
    })
    const colorHex = hex.substring(10, hex.length)
    const colorDiskActiveKey = colorListHex.findIndex(h => h === colorHex)
    const colors = avgSplit(colorHex, 12).map(colorStr =>{
      const colorHex = avgSplit(colorStr, 4)
      const color = ColorUtils.hsv2hex(sToN(colorHex[0]), sToN(colorHex[1]) / 10, sToN(colorHex[2]) / 10)
      return color
    })
    return {
      ...common,
      colors,
      colorDiskActiveKey
    }
  }else{
    const h = parseInt(hex.substring(10, 14), 16)
    const s = parseInt(hex.substring(14, 18), 16)
    const v = parseInt(hex.substring(18, 22), 16)
    if (daubType === 0) {
      return {
        ...common,
        h,
        s,
        v
      }
    } else {
      const part = parseInt(hex.substring(22, 24), 16)
      const [selected] = chunk(hex.substring(24, hex.length), 2)
      return {
        ...common,
        h,
        s,
        v,
        part,
        selected
      }
    }
  }
}
// version = 0,  // 版本
// adjustCode = 1, // 调节模式  00 白光, 01 彩光, 02 色卡, 03 组合 
// effect = 0, // 调节效果 00 静态, 01 过度, 02 闪烁, 03 呼吸, 04 闪耀
// daubType = 0, // 涂抹动作  00 全选, 01 单选, 02 擦除  白光模式下只有 00
export function drawToolFormat(data: ColorTool | WhiteTool | Combination) {
  const { version = 0, daubType = 0, effect = 0, num, adjustCode = 1 } = data
  const commonStr = `${nToSH(version)}${nToSH(adjustCode)}${nToSH(effect)}${nToSH(num)}`
  let colorStr = '' // 彩光,色卡
  let whiteStr = '' // 白光模式
  let combinationStr = '' // 组合模式
  if(adjustCode === 0){
    const whiteData = data as WhiteTool
    whiteStr = `${nToSH(daubType)}${nToSH(whiteData.bright * 10, 4)}${nToSH(whiteData.temp * 10, 4)}`
  }else if(adjustCode === 3){
    const combinationData = data as Combination
    combinationStr = '00' + combinationData.colors.reduce((pre, cur) =>{
      const color = ColorUtils.hex2hsv(cur)
      const colorStr = `${nToSH(Math.round(color[0]),4)}${nToSH(Math.round(color[1] * 10),4)}${nToSH(Math.round(color[2] * 10),4)}`
      return pre += colorStr
    }, '')
  }else{
    const colorData = data as ColorTool
    const selectedStr = colorData?.selected && colorData?.selected.reduce((pre, cur) => {
      pre += nToSH(Number(cur) + 1)
      return pre
    }, '')
    const v = adjustCode === 0 ? '' : colorData.selected ? (parseInt('80', 16) + colorData.selected.length).toString(16) + selectedStr : ''
    colorStr = `${nToSH(daubType)}${nToSH(colorData?.h || 0, 4)}${nToSH(colorData?.s * 10 || 0, 4)}${nToSH(colorData?.v * 10 || 0, 4)}${v}`
  }
  const extraStr = adjustCode === 0 ? whiteStr : adjustCode === 3 ? combinationStr : colorStr
  return commonStr + extraStr
}