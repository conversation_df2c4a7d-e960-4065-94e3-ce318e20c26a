# React Navigation TS Outside Template

English | [简体中文](./README-zh_CN.md)

for docs, please visit [tuya docs](https://docs.tuya.com)

for users outside Chinese mainland, please remove `.npmrc` file.

## Download manually

```bash
$ curl https://codeload.github.com/tuya/tuya-panel-demo/tar.gz/master | tar -xz --strip=2 tuya-panel-demo-master/examples/basic-ts-navigation
$ mv basic tuya-panel-basic-ts-navigation-example
$ cd tuya-panel-basic-ts-navigation-example
```

## Introduction

The React Navigation TS template is a Tuya basic template based on the three-party library of react-navigation. The template shows part of the animation effects of react-navigation, which solves the problem of page jams caused by a large amount of list data.

You can scan the following QR code through the Tuya app to preview.

![reactNavigation](https://images.tuyacn.com/rms-static/5a4e6770-7b2b-11eb-b60c-35c3dc2e2583-1614671169383.png?tyName=react_navigation_ts.png)

## Running

```bash
$ npm install && npm run start
# or
$ yarn && yarn start
```

## License

Copyright © 2020
