import { useDeviceId, useDp, useTimeSchedule } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Result } from "@ledvance/base/src/models/modules/Result";
import { getGlobalParamsDp } from "@ledvance/base/src/utils/common";
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard';
import { useCallback, useEffect, useMemo, useState } from "react";
import I18n from "@ledvance/base/src/i18n";
import { RouterKey } from "navigation/Router";
import { parseJSON, SupportUtils } from "@tuya/tuya-panel-lamp-sdk/lib/utils";
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import { createParams } from "@ledvance/base/src/hooks/Hooks";
import { ClassicModePageParams } from '../pages/classicMode/ClassicModePage'
import { TimeSchedulePageParams } from "../pages/timeSchedule/TimeSchedulePage";
import { DeviceData, DeviceStateType, DeviceType } from "../pages/timeSchedule/Interface";
import { ApplyForItem, WorkMode } from "@ledvance/base/src/utils/interface";
import { getFeature, NativeApi, putFeature } from "@ledvance/base/src/api/native";
import { dp2Obj, ModeNodeInfo, obj2Dp, useClassicMode } from "pages/classicMode/ClassicModeActions";
import { useReactive, useUpdateEffect } from "ahooks";
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'

export function useSwitch() {
  return useDp<boolean, any>(getGlobalParamsDp('switch_led'));
}

export function useCountDown() {
  return useDp<number, any>(getGlobalParamsDp('countdown'));
}

export function useWorkMode(): [WorkMode, (v: WorkMode) => Promise<Result<any>>] {
  const [workMode, setWorkMode] = useDp<WorkMode, any>(getGlobalParamsDp('work_mode'));
  const [mode, setMode] = useState<WorkMode>(workMode);
  const devId = useDeviceId()
  useEffect(() =>{
    getFeature(devId, 'work_mode').then(res => {
      if (res.result && res.data){
        setMode(res.data)
      }
    })
  }, [])

  useUpdateEffect(() =>{
    if (workMode === WorkMode.Scene || workMode === WorkMode.Music){
      if (workMode === WorkMode.Scene){
        MusicManager.close()
      }
      setMode(workMode)
      putFeature(devId, 'work_mode', workMode).then()
    }
  }, [workMode])
  return [mode, setWorkMode]
}

export function useIsWhiteMode(): [boolean, (isWhiteMode: boolean) => Promise<Result<any>>] {
    const defaultWhiteMood = "00001e032003e801f403e8";
    const [classicDp, setClassicDp] = useDp<string, any>(getGlobalParamsDp('classicmode'));
    // 添加设备后默认为白光模式 dp值为00001e032003e801f403e8，并且只有默认的白光的dp值为长度22，所以通过长度22判断白光模式
    const isWhiteMode = classicDp.length === 22;
    const setIsWhiteMode = (isWhiteMode: boolean) => {
        if (isWhiteMode) {
            return setClassicDp(defaultWhiteMood);
        }
        return new Promise<any>((resolve) => {
            resolve("")
        });
    }
    return [isWhiteMode, setIsWhiteMode]
}

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright] = useDp<number, any>(getGlobalParamsDp('bright_value'));
  const setBrightFn = (v: number) => {
    return setBright(v * 10);
  };
  return [Math.round((bright ?? 10) / 10), setBrightFn];
}

const defColourData = {
  h: 0,
  s: 100,
  v: 100
}

export function useColourData(): [ModeNodeInfo, (v: ModeNodeInfo) => Promise<Result<any>>] {
  const devId = useDeviceId()
  const [modeInfo, setModeInfo] = useClassicMode()
  const [colourData, setColourData] = useState(modeInfo.nodes[0] ?? defColourData)

  useEffect(() =>{
    getFeature(devId, 'colour_data').then(res =>{
      if (res.result && res.data){
        setColourData(parseJSON(res.data))
      }
    })
  }, [])

  const setColourDataFn = async (v: ModeNodeInfo) =>{
    putFeature(devId, 'colour_data', JSON.stringify(v)).then()
    return setModeInfo({
      mode: 0,
      speed: 100,
      bright: v.v,
      nodes: [{...v, s: mapRange(v.s), v: 100}]
    })
  }
  return [colourData, setColourDataFn]
}

export function mapRange(value: number, isNormalize?: boolean) {
  let inMin = 1
  let outMin = 50
  if (isNormalize){
    inMin = 50
    outMin = 1
  }
  return Math.floor((value - inMin) * (100 - outMin) / (100 - inMin) + outMin)
}

export function isSupportTimer(){
  return SupportUtils.isSupportDp('countdown')
}

export function isSupportMood() {
  return SupportUtils.isSupportDp('classicmode')
}

export function isSupportMusic() {
  return SupportUtils.isSupportDp('music_data')
}

export function useAdvancedData(sceneStatusId: number, setSceneStatusId: (v: number) => Promise<any>, workMode: WorkMode): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const [countdown] = useCountDown();
  const [switchLed] = useSwitch();
  const deviceId = useDeviceId();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: true,
        },
      },
      isManual: dps[getGlobalParamsDp('work_mode')] === WorkMode.Colour,
      mood: undefined,
    };
    const switchLed = getGlobalParamsDp('switch_led')
    if (switchLed){
      const mood = dp2Obj(dps[getGlobalParamsDp('classicmode')])
      if (deviceState.isManual){
        deviceState.deviceData.deviceData.h = mood.nodes[0]?.h ?? 0
        deviceState.deviceData.deviceData.s = mood.nodes[0]?.s ?? 100
        deviceState.deviceData.deviceData.v = mood.bright ?? 100
      }else{
        mood.nodes = mood.nodes.filter(n => n.s !== 0)
        deviceState.mood = mood
      }
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('switch_led')] = true;
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
        manualDps[getGlobalParamsDp('classicmode')] = obj2Dp(mood)
      } else {
        const device = deviceData.deviceData as DeviceData
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
        });
        if (manualDps[getGlobalParamsDp('switch_led')]) {
          manualDps[getGlobalParamsDp('classicmode')] = obj2Dp({
            mode: 0,
            bright: device.v,
            speed: 100,
            nodes: [{h: device.h, s: device.s, v: 100}]
          })
        }
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Colour
      }
      return manualDps;
    },
    []
  );

  const moodParams = createParams<ClassicModePageParams>({
    classicModeDp: getGlobalParamsDp('classicmode'),
    switchLedDp: getGlobalParamsDp('switch_led'),
    isSupportColor: true,
    isSupportSceneStatus: true,
    sceneStatusId,
    setSceneStatusId,
  });

  advanceData.push({
    title: I18n.getLang('mesh_device_detail_mode'),
    statusColor: getAdvancedStatusColor(
      sceneStatusId !== -1 && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
    ),
    dp: {key: 'classicmode', code: getGlobalParamsDp('classicmode')},
    router: {
      key: RouterKey.classic_mode,
      params: moodParams,
    },
  });

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.time_schedule,
      params: {
        applyForList: [
          {
            type: 'light',
            name: I18n.getLang('Onoff_button_socket'),
            key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
            dp: getGlobalParamsDp('switch_led'),
            enable: true,
          },
        ],
        applyForDisabled: true,
        isSupportMood: isSupportMood(),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: true,
        isSupportBrightness: false,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    },
  });

  if (isSupportMusic()) {
    const params = createParams({
      switch_led: getGlobalParamsDp('switch_led'),
      work_mode: getGlobalParamsDp('work_mode'),
      music_data: getGlobalParamsDp('music_data'),
      isMixRGBWLamp: false,
      isFeatureMode: true
    });

    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(
        workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: {key: 'music_data', code: getGlobalParamsDp('music_data')},
      router: {
        key: RouterKey.ui_biz_music,
        params,
      },
    });
  }

  if (isSupportTimer()) {
    const params = useMemo(() =>{
      return {
        dps: [
          {
            label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
            value: 'lighting',
            dpId: getGlobalParamsDp('countdown'),
            enableDp: getGlobalParamsDp('switch_led'),
            cloudKey: 'lightingInfo',
            stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
            stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
          },
        ],
      }
    }, [])
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles:
        countdown > 0
          ? [
            I18n.formatValue(
              switchLed
                ? 'ceiling_fan_feature_2_light_text_min_off'
                : 'ceiling_fan_feature_2_light_text_min_on',
              timeFormat(countdown, true)
            ),
          ]
          : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: {key: 'countdown', code: getGlobalParamsDp('countdown')},
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  return advanceData
}
