import { NavigationRoute } from 'tuya-panel-kit'
import { ui_biz_router<PERSON>ey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import HomePage from '../pages/home/<USER>'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import SelectPageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'
import ClassicModePage from 'pages/classicMode/ClassicModePage'
import AddClassicModePage from 'pages/classicMode/AddClassicModePage'
import ClassicModeEdit from 'pages/classicMode/ClassicModeEdit'
import TimeSchedulePage from 'pages/timeSchedule/TimeSchedulePage'
import TimeScheduleDetailPage from 'pages/timeSchedule/TimeScheduleDetailPage'
import MusicPageRouters from '@ledvance/ui-biz-bundle/src/modules/music/Router'

export const RouterKey = {
  main: 'main',
  classic_mode: 'classic_mode',
  classic_mode_add: 'classic_mode_add',
  classic_mode_edit: 'classic_mode_edit',
  time_schedule: 'time_schedule',
  time_schedule_edit: 'time_schedule_edit',
  ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.classic_mode,
    component: ClassicModePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.classic_mode_add,
    component: AddClassicModePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.classic_mode_edit,
    component: ClassicModeEdit,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.time_schedule,
    component: TimeSchedulePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.time_schedule_edit,
    component: TimeScheduleDetailPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...TimerPageRouters,
  ...SelectPageRouters,
  ...MusicPageRouters,
]