import Strings from '@ledvance/base/src/i18n';
import Page from '@ledvance/base/src/components/Page';
import React, { useCallback } from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import { Utils } from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import { useReactive } from 'ahooks';
import { useNavigation } from '@react-navigation/native';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { difference, head, range } from 'lodash';
import { MoodUIInfo } from './ClassicModeActions'
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import RecommendModeItem from './RecommendModeItem';
import { ClassicModePageParams } from './ClassicModePage'
import { getRecommendModes, RecommendModes } from './ClassicModeInfo';
import { RouterKey } from 'navigation/Router';
const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export interface AddClassicModePageParams {
  isStatic: boolean;
  modeIds: number[];
  moduleParams: ClassicModePageParams;
  modDeleteMood: (mode: 'add' | 'edit' | 'del', currentMood: MoodUIInfo) => Promise<Result<any>>;
}

interface AddMoodPageState {
  data: RecommendModes[];
}

const AddClassicModePage = (props: { theme?: any }) => {
  const navigation = useNavigation();
  const params = useParams<AddClassicModePageParams>()
  const state = useReactive<AddMoodPageState>({
    data: getRecommendModes(params.isStatic),
  });


  const onMoodItemClick = useCallback(
    (moodItem: RecommendModes) => {
      const idRange = range(0, 256);
      const mainId: number = head(difference(idRange, params.modeIds)) ?? 0;
      const currentMode = {...moodItem, id: mainId, name: moodItem.nodes ? moodItem.name : '', nodes: moodItem.nodes ?? [{h: 0,s: 100,v: 100}]}
      navigation.navigate(RouterKey.classic_mode_edit, {
        ...params,
        mode: 'add',
        currentMode
      });
    },
    [JSON.stringify(params)]
  );

  const styles = StyleSheet.create({
    root: {
      flex: 1,
      flexDirection: 'column',
    },
    desc: {
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      marginHorizontal: cx(24),
      marginTop: cx(12),
    },
  });

  return (
    <Page
      backText={Strings.getLang('add_new_static_mood_system_back')}
      headlineText={Strings.getLang(
        params.isStatic
          ? 'add_new_static_mood_headline_text'
          : 'add_new_dynamic_mood_headline_text'
      )}
    >
      <View style={styles.root}>
        <Text style={styles.desc}>
          {Strings.getLang(
            params.isStatic
              ? 'add_new_static_mood_description_text'
              : 'add_new_dynamic_mood_description_text'
          )}
        </Text>
        <FlatList
          data={state.data}
          renderItem={({ item }) => {
            return (
              <RecommendModeItem
                title={item.name}
                mood={item}
                onPress={() => {
                  onMoodItemClick(item);
                }}
              />
            );
          }}
          ItemSeparatorComponent={() => <Spacer />}
          ListHeaderComponent={() => <Spacer />}
          ListFooterComponent={() => <Spacer />}
          keyExtractor={item => item.name}
        />
      </View>
    </Page>
  );
};

export default withTheme(AddClassicModePage);

