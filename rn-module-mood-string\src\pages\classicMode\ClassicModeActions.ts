import { NativeApi } from "@ledvance/base/src/api/native"
import I18n from "@ledvance/base/src/i18n"
import { useDps } from "@ledvance/base/src/models/modules/NativePropsSlice"
import { getGlobalParamsDp, hex2Int, spliceByStep } from "@ledvance/base/src/utils/common"
import { parseJSON, to16 } from "@tuya/tuya-panel-lamp-sdk/lib/utils"
import { defModeList } from "./ClassicModeInfo"
import { Result } from "@ledvance/base/src/models/modules/Result"
import { useMemo } from "react"
import { mapRange } from "hooks/FeatureHooks"

export enum MoodNodeTransitionMode {
  Static,
  Shimmer,
  Fluorescence,
  Flash,
  RandomFlash,
  Chase,
  Jump,
  Sparkle,
  Fade,
  SlowFade,
  LightShow
}

export const ClassicDynamicMode = {
  '1': { title: I18n.getLang('mood_string_mode_shimmer'), mode: 1 },
  '2': { title: I18n.getLang('mood_string_mode_fluorescence'), mode: 2 },
  '3': { title: I18n.getLang('strip_lights_modes_flash_text'), mode: 3 },
  '4': { title: I18n.getLang('mood_string_mode_random_flash'), mode: 4 },
  '5': { title: I18n.getLang('string_lights_modes_chase_text'), mode: 5 },
  '6': { title: I18n.getLang('other_lights_modes_jump_text'), mode: 6 },
  '7': { title: I18n.getLang('mood_string_mode_sparkle'), mode: 7 },
  '8': { title: I18n.getLang('mood_string_mode_fade'), mode: 8 },
  '9': { title: I18n.getLang('mood_string_mode_slow_fade'), mode: 9 },
}

export interface ModeInfo {
  mode: number 
  speed: number 
  bright: number 
  nodes: ModeNodeInfo[]
}

export interface MoodUIInfo extends ModeInfo{
  name: string 
  id: number 
}

export interface ModeNodeInfo {
  h: number 
  s: number 
  v: number 
}

const ClassicModeListPutJsonId = 'ClassicModeList'

export const getRemoteModeList = async (devId: string, isRefresh?: boolean) =>{
  if (isRefresh){
    const res = await NativeApi.putJson(devId, ClassicModeListPutJsonId, JSON.stringify(defModeList))
    if (res.success){
      return {
        success: true,
        data: defModeList
      }
    }
  }
  const res = await NativeApi.getJson(devId, ClassicModeListPutJsonId)
  const isNormalData = Array.isArray(parseJSON(res?.data))
  if (res.success && isNormalData) {
    return {
      success: true,
      data: JSON.parse(res.data)
    }
  } else {
    if (res.msg?.includes('资源未找到') || !isNormalData) {
      const res = await NativeApi.putJson(devId, ClassicModeListPutJsonId, JSON.stringify(defModeList))
      if (res.success) {
        return {
          success: true,
          data: defModeList
        }
      }
      return { success: false }
    }
    return { success: false }
  }
}

export const saveRemoteModeList = (devId: string, modeList: MoodUIInfo[]) =>{
  return NativeApi.putJson(devId, ClassicModeListPutJsonId, JSON.stringify(modeList))
}

export const useClassicMode = (): [ModeInfo, (v: ModeInfo) => Promise<Result<any>>] =>{
  const [dps, setDps] = useDps()
  const classicDp = dps[getGlobalParamsDp('classicmode')]
  const classicMode = useMemo(() =>{
    return dp2Obj(classicDp)
  }, [])
  const setClassicDpFn = async (mood: ModeInfo) =>{
    const dp = obj2Dp(mood)
    const dps: any = {
      [getGlobalParamsDp('classicmode')] : dp,
    }
    if (!dps[getGlobalParamsDp('switch_led')]){
      dps[getGlobalParamsDp('switch_led')] = true 
    }
    return setDps(dps)
  }
  return [classicMode ,setClassicDpFn]
}

export function obj2Dp(mood: ModeInfo): string {
  const modeHex = to16(mood.mode)
  const speedHex = to16(Math.round(mood.speed * 10), 4)
  const brightHex = to16(Math.round(mood.bright * 10), 4)
  let nodeHex = ''
  for(let i = 0; i < 6; i ++){
    const node = mood.nodes[i]
    nodeHex += node ? to16(Math.round(node.h), 4) + to16(Math.round(mapRange(node.s) * 10), 4) + to16(1000, 4) : '000000000000'
  }
  return modeHex + nodeHex + brightHex + speedHex
}

export function dp2Obj(dp: string): ModeInfo{
  if (!dp) return defModeList[0]
  const modeHex = dp.substring(0, 2); // 前2个字符
  const speedBrightHex = dp.substring(dp.length - 8); // 最后8个字符
  const nodeHex = dp.substring(2, dp.length - 8); // 中间部分
  const mode = hex2Int(modeHex)
  const bright = Math.round(hex2Int(speedBrightHex.slice(0,4))/ 10)
  const speed = Math.round(hex2Int(speedBrightHex.slice(4, 8)) / 10)
  const nodes = spliceByStep(nodeHex, 12).map((hex) =>{
    return {
      h: Math.round(hex2Int(hex.slice(0, 4))),
      s: Math.round(hex2Int(hex.slice(4, 8)) / 10),
      v: 100
    }
  })
  return {
    mode,
    speed,
    nodes,
    bright
  }
}
