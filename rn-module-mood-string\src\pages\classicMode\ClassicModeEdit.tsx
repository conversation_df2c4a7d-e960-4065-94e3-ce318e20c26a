import React, { useCallback, useMemo } from "react";
import { ScrollView, StyleSheet, View, Text, Image, TouchableOpacity, FlatList } from "react-native";
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { Utils } from "tuya-panel-kit";
import { useReactive } from "ahooks";
import Card from "@ledvance/base/src/components/Card";
import TextButton from "@ledvance/base/src/components/TextButton";
import { showDialog } from "@ledvance/base/src/utils/common";
import TextField from "@ledvance/base/src/components/TextField";
import { useParams } from "@ledvance/base/src/hooks/Hooks";
import { ClassicDynamicMode, ModeNodeInfo, MoodUIInfo } from "./ClassicModeActions";
import res from "@ledvance/base/src/res";
import Spacer from "@ledvance/base/src/components/Spacer";
import { RouterKey } from "navigation/Router";
import { useNavigation } from '@react-navigation/native';
import { Result } from "@ledvance/base/src/models/modules/Result";
import { hsv2Hex, mapFloatToRange } from "@ledvance/base/src/utils";
import ColorAdjustView from "@ledvance/base/src/components/ColorAdjustView";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import { SelectPageParams } from "@ledvance/ui-biz-bundle/src/newModules/select/SelectPage";
import { cloneDeep, isEqual } from "lodash";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface StaticClassicModeEditParams {
  isStatic: boolean
  mode: 'add' | 'edit'
  currentMode: MoodUIInfo
  nameRepeat: (mood: MoodUIInfo) => boolean
  modeDeleteMode: (mode: 'add' | 'edit' | 'del', currentMood: MoodUIInfo) => Promise<Result<any>>;
}

const ClassicModeEdit = (props: { theme?: any }) => {
  const params = useParams<StaticClassicModeEditParams>()
  const navigation = useNavigation();
  const currentMode = cloneDeep(params.currentMode)
  const node = currentMode.nodes.length ? currentMode.nodes[currentMode.nodes.length - 1] : undefined as any
  const state = useReactive({
    mood: currentMode,
    node,
    mainBucketSelected: false,
    loading: false
  })

  const getColorBlockColor = useCallback((node: ModeNodeInfo) => {
    const s = Math.round(mapFloatToRange(node.s / 100, 30, 100));
    return hsv2Hex(node.h, s, 100);
  }, []);

  const createSelectModeData = useCallback(
    (mode: number) => {
      return Object.values(ClassicDynamicMode).map(scene => {
        return {
          text: scene.title,
          selected: scene.mode === mode,
          value: scene.mode,
        };
      });
    },
    []
  );

  const nameRepeat = useMemo(() => {
    return params.nameRepeat(state.mood)
  }, [state.mood.name, params.nameRepeat])


  const checkMoodChanged = useMemo(() => {
    return isEqual(state.mood, params.currentMode)
  }, [JSON.stringify(state.mood), params.currentMode])

  const canSaveMoodData = useMemo(() => {
    return state.mood.name.length > 0 && state.mood.name.length < 33 && !nameRepeat && (params.mode === 'add' || !checkMoodChanged)
  }, [nameRepeat, state.mood.name, checkMoodChanged, params.mode])

  const styles = StyleSheet.create({
    titleText: {
      color: props.theme.global.fontColor,
      fontSize: cx(18),
      fontFamily: 'helvetica_neue_lt_std_bd'
    },
    preview: {
      width: cx(20),
      height: cx(20),
      marginStart: cx(12),
      borderRadius: cx(4),
    },
    deleteBtn: {
      width: '100%',
      height: cx(50),
      backgroundColor: props.theme.button.delete,
      borderRadius: cx(8),
    },
    deleteBtnText: {
      color: props.theme.button.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    nodesAdjust: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    adjustButtons: {
      width: cx(44),
      marginStart: cx(16),
    },
    adjustButton: {
      width: cx(44),
      height: cx(44),
    },
    nodeList: {
      flex: 1,
      marginHorizontal: cx(16),
    },
    nodeItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    nodeDeleteBtn: {
      width: cx(24),
      height: cx(30),
      justifyContent: 'center',
      alignItems: 'center',
    },
    nodeBlock: {
      flex: 1,
      height: cx(40),
      borderRadius: cx(8),
    },
    nodeDeleteIcon: {
      width: cx(16),
      height: cx(16),
    },
    nodeAddBtn: {
      height: cx(40),
      justifyContent: 'center',
      alignItems: 'center',
      marginEnd: cx(26),
      borderRadius: cx(8),
      borderWidth: cx(1),
      borderStyle: 'dashed',
      borderColor: props.theme.addNode.border,
      backgroundColor: props.theme.addNode.background,
    },
  })
  return (
    <Page
      backText={I18n.getLang('mesh_device_detail_mode')}
      showBackDialog={!checkMoodChanged}
      backDialogTitle={I18n.getLang(
        params.mode === 'add'
          ? 'string_light_pp_dialog_sm_add_headline_c'
          : 'manage_user_unsaved_changes_dialog_headline'
      )}
      backDialogContent={I18n.getLang(
        params.mode === 'add'
          ? 'strip_light_static_mood_add_step_2_dialog_text'
          : 'strip_light_static_mood_editor_step_2_dialog_text'
      )}
      headlineText={I18n.getLang(params.mode === 'add' ? (params.isStatic ? 'add_new_static_mood_headline_text' : 'add_new_dynamic_mood_headline_text') : (params.isStatic ? 'edit_static_mood_headline_text' : 'edit_static_mood_headline_text'))}
      rightButtonIcon={node ? (canSaveMoodData ? res.ic_check : res.ic_uncheck) : undefined}
      rightButtonIconClick={async () => {
        if (state.loading || !canSaveMoodData) return
        state.loading = true
        const res = await params.modeDeleteMode(params.mode, state.mood)
        state.loading = false
        if (res.success) {
          navigation.navigate(RouterKey.classic_mode)
        }
      }}
      loading={state.loading}
    >
      <ScrollView style={{ flex: 1 }} nestedScrollEnabled={true}>
        <TextField
          editable={!!node}
          style={{ marginHorizontal: cx(24) }}
          value={state.mood.name}
          placeholder={I18n.getLang('edit_static_mood_inputfield_topic_text')}
          onChangeText={text => {
            state.mood.name = text;
          }}
          maxLength={33}
          tipColor={nameRepeat ? props.theme?.global.error : undefined}
          tipIcon={nameRepeat ? { uri: res.ic_text_field_input_error } : undefined}
          showError={state.mood.name.length > 32 || nameRepeat}
          errorText={I18n.getLang(
            nameRepeat ? 'string_light_pp_field_sm_add_error1' : 'add_new_dynamic_mood_alert_text'
          )}
        />
        <Spacer height={cx(12)} />
        {!!node && <Card style={{ marginHorizontal: cx(24) }}>
          <Spacer height={cx(12)} />
          <Text style={[styles.titleText, { marginHorizontal: cx(24) }]}>{I18n.getLang('light_sources_tile_tw_lighting_headline')}</Text>
          <Spacer height={cx(16)} />
          {!params.isStatic && <>
            <TextFieldStyleButton
              style={{ marginHorizontal: cx(16) }}
              text={ClassicDynamicMode[state.mood.mode]?.title}
              placeholder={I18n.getLang('add_new_dynamic_mood_color_changing_mode_headline')}
              onPress={() => {
                const paramsSelect: SelectPageParams<number> = {
                  title: I18n.getLang('add_new_dynamic_mood_color_changing_mode_headline'),
                  data: createSelectModeData(state.mood.mode),
                  onSelect: selectPageData => {
                    state.mood.mode = selectPageData.value;
                  },
                };
                navigation.navigate(RouterKey.ui_biz_select_page, paramsSelect);
              }}
            />
            <Spacer height={cx(10)} />
            <LdvSlider
              title={I18n.getLang('add_new_dynamic_mood_lights_field_speed_topic_text')}
              value={state.mood.speed}
              onValueChange={() => { }}
              onSlidingComplete={value => {
                state.mood.speed = value;
              }}
            />
            <Spacer height={cx(16)} />
          </>}
          <View style={styles.nodesAdjust}>
            <View style={styles.adjustButtons}>
              <TouchableOpacity
                onPress={() => {
                  state.mainBucketSelected = true;
                }}
              >
                <Image
                  style={[
                    styles.adjustButton,
                    { tintColor: state.mainBucketSelected ? props.theme.icon.primary : props.theme.icon.normal },
                  ]}
                  source={{ uri: res.ic_paint_bucket}}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  state.mainBucketSelected = false;
                }}
              >
                <Image
                  style={[
                    styles.adjustButton,
                    { tintColor: state.mainBucketSelected ? props.theme?.icon.normal : props.theme?.icon.primary },
                  ]}
                  source={{ uri: res.ic_colorize}}
                />
              </TouchableOpacity>
            </View>
            <FlatList
              data={state.mood.nodes}
              style={styles.nodeList}
              renderItem={({ item, index }) => {
                return (
                  <View style={styles.nodeItem}>
                    <TouchableOpacity
                      style={[
                        styles.nodeBlock,
                        {
                          backgroundColor: getColorBlockColor(item),
                        },
                      ]}
                      onPress={() => {
                        state.node = item;
                      }}
                    />
                    <TouchableOpacity
                      style={styles.nodeDeleteBtn}
                      disabled={state.mood.nodes.length < 2}
                      onPress={() => {
                        state.mood.nodes.splice(index, 1);
                        state.node = state.mood.nodes[state.mood.nodes.length - 1];
                      }}
                    >
                      <Image
                        style={[
                          styles.nodeDeleteIcon,
                          {
                            tintColor: state.mood.nodes.length < 2 ? props.theme.icon.disable : props.theme.icon.normal,
                          },
                        ]}
                        source={{ uri: res.ic_mood_del}}
                      />
                    </TouchableOpacity>
                  </View>
                );
              }}
              keyExtractor={(_, index) => `${index}`}
              ItemSeparatorComponent={() => <Spacer height={cx(12)} />}
              ListFooterComponent={() => {
                if (state.mood.nodes.length >= 10) {
                  return <></>;
                }
                return (
                  <View>
                    <Spacer height={cx(12)} />
                    <TouchableOpacity
                      style={styles.nodeAddBtn}
                      onPress={() => {
                        const node = {
                          ...state.node,
                        };
                        state.mood.nodes.push(node);
                        state.node = node;
                      }}
                    >
                      <Image
                        style={{
                          width: cx(18),
                          height: cx(18),
                          tintColor: props.theme.global.fontColor,
                        }}
                        source={{ uri: res.add }}
                      />
                    </TouchableOpacity>
                  </View>
                );
              }}
            />
          </View>
          <Spacer />
          <View style={{ flexDirection: 'row', marginHorizontal: cx(16), alignItems: 'center' }}>
            <Text style={styles.titleText}>
              {I18n.getLang('add_new_dynamic_mood_lights_field_headline2_text')}
            </Text>
            <View
              style={[styles.preview, { backgroundColor: getColorBlockColor(state.node) }]}
            />
          </View>
          <Spacer />
          <ColorAdjustView
            reserveSV={true}
            h={state.node.h}
            s={state.node.s}
            v={state.mood.bright}
            onHSVChange={(h, s, v) => {
              if (state.mainBucketSelected) {
                state.mood.nodes.forEach(node => {
                  node.h = h
                  node.s = s
                  state.mood.bright = v
                })
              } else {
                state.node.h = h
                state.node.s = s
                state.mood.bright = v
              }
            }}
            onHSVChangeComplete={(h, s, v) => {
              if (state.mainBucketSelected) {
                state.mood.nodes.forEach(node => {
                  node.h = h
                  node.s = s
                  state.mood.bright = v
                })
              } else {
                state.node.h = h
                state.node.s = s
                state.mood.bright = v
              }
            }} />
          <Spacer />
        </Card>}
        {params.mode === 'edit' && (
          <View style={{ marginTop: cx(20), marginHorizontal: cx(24) }}>
            <TextButton
              style={styles.deleteBtn}
              textStyle={styles.deleteBtnText}
              text={I18n.getLang('edit_static_mood_button_delete_text')}
              onPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('string_light_pp_dialog_sm_ed_headline_d'),
                  subTitle: I18n.getLang(`strip_light_static_mood_edit_dialog_text`),
                  onConfirm: async (_, { close }) => {
                    close();
                    state.loading = true;
                    const res = await params.modeDeleteMode('del', state.mood);
                    state.loading = false;
                    if (res.success) {
                      navigation.navigate(RouterKey.classic_mode);
                    }
                  }
                })
              }}
            />
          </View>
        )}
        <Spacer />
      </ScrollView>
    </Page>
  )
}

export default withTheme(ClassicModeEdit)
