import I18n from '@ledvance/base/src/i18n';
import { ModeNodeInfo } from './ClassicModeActions';

export interface RecommendModes {
  name: string;
  mode: number;
  speed: number;
  bright: number;
  nodes?: ModeNodeInfo[];
}

export function getRecommendModes(isStatic: boolean): RecommendModes[] {
  if (isStatic) {
    return [
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text1'),
        mode: 0,
        speed: 100,
        bright: 100,
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text2'),
        mode: 0,
        speed: 100,
        bright: 100,
        nodes: [{ h: 125, s: 100, v: 100 }],
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text3'),
        mode: 0,
        speed: 100,
        bright: 100,
        nodes: [{ h: 0, s: 100, v: 100 }],
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text4'),
        mode: 0,
        speed: 100,
        bright: 100,
        nodes: [{ h: 36, s: 100, v: 100 }],
      },
    ];
  } else {
    return [
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text1'),
        mode: 6,
        speed: 100,
        bright: 100,
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text2'),
        mode: 1,
        speed: 14,
        bright: 100,
        nodes: [
          { h: 75, s: 100, v: 48 },
          { h: 200, s: 100, v: 86 },
          { h: 75, s: 100, v: 80 },
        ],
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text4'),
        mode: 2,
        speed: 14,
        bright: 100,
        nodes: [
          { h: 40, s: 100, v: 100 },
          { h: 24, s: 100, v: 100 },
          { h: 0, s: 100, v: 100 },
        ],
      },
      {
        name: I18n.getLang('add_new_dynamic_mood_field_headline_text3'),
        mode: 3,
        speed: 14,
        bright: 100,
        nodes: [
          { h: 300, s: 100, v: 59 },
          { h: 183, s: 100, v: 82 },
          { h: 40, s: 100, v: 100 },
        ],
      },
    ];
  }
}

export const defModeList = [
  {
    id: 1,
    name: I18n.getLang('dynamicmoods_forest'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 120, s: 76, v: 100 },
      { h: 51, s: 100, v: 100 },
      { h: 120, s: 100, v: 100 },
    ],
  },
  {
    id: 2,
    name: I18n.getLang('dynamicmoods_desert'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 34, s: 33, v: 100 },
      { h: 16, s: 69, v: 100 },
      { h: 16, s: 100, v: 100 },
    ],
  },
  {
    id: 3,
    name: I18n.getLang('dynamicmoods_dreams'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 331, s: 20, v: 100 },
      { h: 180, s: 26, v: 100 },
      { h: 28, s: 27, v: 100 },
    ],
  },
  {
    id: 4,
    name: I18n.getLang('dynamicmoods_breeze'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 43, s: 85, v: 100 },
      { h: 0, s: 81, v: 100 },
      { h: 39, s: 100, v: 100 },
    ],
  },
  {
    id: 5,
    name: I18n.getLang('dynamicmoods_waters'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 195, s: 100, v: 100 },
      { h: 160, s: 50, v: 100 },
      { h: 240, s: 78, v: 100 },
    ],
  },
  {
    id: 6,
    name: I18n.getLang('dynamicmoods_splash'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 60, s: 100, v: 100 },
      { h: 39, s: 100, v: 100 },
      { h: 16, s: 100, v: 100 },
    ],
  },
  {
    id: 7,
    name: I18n.getLang('dynamicmoods_sunrise'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 9, s: 72, v: 100 },
      { h: 38, s: 29, v: 100 },
      { h: 51, s: 100, v: 100 },
    ],
  },
  {
    id: 8,
    name: I18n.getLang('dynamicmoods_wonderland'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 187, s: 23, v: 100 },
      { h: 0, s: 2, v: 100 },
      { h: 0, s: 0, v: 100 },
    ],
  },
  {
    id: 9,
    name: I18n.getLang('dynamicmoods_Twilight'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 330, s: 59, v: 100 },
      { h: 39, s: 100, v: 100 },
      { h: 180, s: 100, v: 100 },
    ],
  },
  {
    id: 10,
    name: I18n.getLang('dynamicmoods_symphony'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 210, s: 88, v: 100 },
      { h: 0, s: 0, v: 100 },
      { h: 51, s: 100, v: 100 },
    ],
  },
  {
    id: 11,
    name: I18n.getLang('dynamicmoods_sorbet'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 350, s: 25, v: 100 },
      { h: 17, s: 52, v: 100 },
      { h: 60, s: 100, v: 100 },
    ],
  },
  {
    id: 12,
    name: I18n.getLang('dynamicmoods_mansion'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 300, s: 100, v: 100 },
      { h: 180, s: 100, v: 100 },
      { h: 39, s: 100, v: 100 },
    ],
  },
  {
    id: 13,
    name: I18n.getLang('dynamicmoods_hour'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 51, s: 100, v: 100 },
      { h: 33, s: 100, v: 100 },
      { h: 38, s: 29, v: 100 },
    ],
  },
  {
    id: 14,
    name: I18n.getLang('dynamicmoods_isle'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 120, s: 100, v: 100 },
      { h: 146, s: 67, v: 100 },
      { h: 90, s: 100, v: 100 },
    ],
  },
  {
    id: 15,
    name: I18n.getLang('dynamicmoods_Beat'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 180, s: 100, v: 100 },
      { h: 300, s: 100, v: 100 },
      { h: 60, s: 100, v: 100 },
    ],
  },
  {
    id: 16,
    name: I18n.getLang('dynamicmoods_glamour'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 51, s: 100, v: 100 },
      { h: 300, s: 100, v: 100 },
      { h: 350, s: 25, v: 100 },
    ],
  },
  {
    id: 17,
    name: I18n.getLang('dynamicmoods_edreams'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 300, s: 100, v: 100 },
      { h: 180, s: 100, v: 100 },
      { h: 39, s: 100, v: 100 },
    ],
  },
  {
    id: 18,
    name: I18n.getLang('dynamicmoods_sunset'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 16, s: 100, v: 100 },
      { h: 29, s: 69, v: 100 },
      { h: 43, s: 85, v: 100 },
    ],
  },
  {
    id: 19,
    name: I18n.getLang('dynamicmoods_carnival'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 300, s: 100, v: 100 },
      { h: 39, s: 100, v: 100 },
      { h: 60, s: 100, v: 100 },
    ],
  },
  {
    id: 20,
    name: I18n.getLang('dynamicmoods_serenade'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 240, s: 78, v: 100 },
      { h: 180, s: 41, v: 100 },
      { h: 240, s: 8, v: 100 },
    ],
  },
  {
    id: 21,
    name: I18n.getLang('dynamicmoods_glow'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 30, s: 100, v: 100 },
      { h: 48, s: 100, v: 100 },
      { h: 0, s: 80, v: 100 },
    ],
  },
  {
    id: 22,
    name: I18n.getLang('dynamicmoods_Canopy'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 120, s: 100, v: 100 },
      { h: 120, s: 67, v: 100 },
      { h: 30, s: 67, v: 100 },
    ],
  },
  {
    id: 23,
    name: I18n.getLang('dynamicmoods_Burst'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 0, s: 100, v: 100 },
      { h: 39, s: 100, v: 100 },
      { h: 60, s: 100, v: 100 },
      { h: 120, s: 100, v: 100 },
      { h: 210, s: 100, v: 100 },
      { h: 273, s: 100, v: 100 },
    ],
  },
  {
    id: 24,
    name: I18n.getLang('dynamicmoods_Lights'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 300, s: 100, v: 100 },
      { h: 60, s: 100, v: 100 },
      { h: 180, s: 100, v: 100 },
      { h: 24, s: 100, v: 100 },
    ],
  },
  {
    id: 25,
    name: I18n.getLang('dynamicmoods_Dreamscape'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 300, s: 20, v: 100 },
      { h: 240, s: 20, v: 100 },
      { h: 60, s: 20, v: 100 },
      { h: 180, s: 20, v: 100 },
    ],
  },
  {
    id: 26,
    name: I18n.getLang('dynamicmoods_NLights'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 150, s: 100, v: 100 },
      { h: 210, s: 100, v: 100 },
      { h: 270, s: 100, v: 100 },
    ],
  },
  {
    id: 27,
    name: I18n.getLang('dynamicmoods_paradise'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 320, s: 60, v: 100 },
      { h: 30, s: 80, v: 100 },
      { h: 60, s: 60, v: 100 },
    ],
  },
  {
    id: 28,
    name: I18n.getLang('dynamicmoods_ice'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 24, s: 100, v: 100 },
      { h: 0, s: 0, v: 100 },
      { h: 216, s: 100, v: 100 },
    ],
  },
  {
    id: 29,
    name: I18n.getLang('dynamicmoods_voyage'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 240, s: 100, v: 100 },
      { h: 270, s: 100, v: 100 },
      { h: 0, s: 0, v: 100 },
    ],
  },
  {
    id: 30,
    name: I18n.getLang('dynamicmoods_wave'),
    mode: 6,
    speed: 75,
    bright: 100,
    nodes: [
      { h: 210, s: 100, v: 100 },
      { h: 180, s: 100, v: 100 },
      { h: 150, s: 100, v: 100 },
    ],
  },
  {
    id: 31,
    name: I18n.getLang('mood_string_mode_light_show'),
    mode: 10,
    speed: 100,
    bright: 100,
    nodes: [],
  },
];
