import React, { useCallback, useEffect, useMemo } from "react";
import { FlatList, StyleSheet, View, Platform, TouchableOpacity, Image } from 'react-native';
import Tag from '@ledvance/base/src/components/Tag';
import Spacer from '@ledvance/base/src/components/Spacer';
import InfoText from '@ledvance/base/src/components/InfoText';
import CustomListDialog from '@ledvance/base/src/components/CustomListDialog';
import { Utils } from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { useParams } from "@ledvance/base/src/hooks/Hooks";
import { useDeviceId, useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { useNavigation } from '@react-navigation/core';
import res from "@ledvance/base/src/res";
import { useReactive } from "ahooks";
import { showDialog } from "@ledvance/base/src/utils/common";
import ModeItem from "./ModeItem";
import { getRemoteModeList, MoodUIInfo, saveRemoteModeList, useClassicMode } from "./ClassicModeActions";
import { RouterKey } from "navigation/Router";
import { cloneDeep, map } from "lodash";
import { useSwitch } from "hooks/FeatureHooks";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface ClassicModePageParams {
  switchLedDp: string
  classicModeDp: string
  sceneStatusId: number
  isSupportSceneStatus?: boolean
  isSupportColor?: boolean,
  isSupportBrightness?: boolean,
  isSupportTemperature?: boolean,
  setSceneStatusId: (v: number) => Promise<any>
}

interface ClassicModeState {
  originMoods: MoodUIInfo[]
  filterMoods: MoodUIInfo[]
  showAddMoodPopover: boolean
  loading: boolean
  staticTagChecked: boolean
  dynamicTagChecked: boolean
  sceneStatusId: number
}

const MAX_MOOD_COUNT = 255

const ClassicModePage = (props: { theme?: ThemeType }) =>{
  const params = useParams<ClassicModePageParams>();
  const deviceInfo = useDeviceInfo();
  const devId = useDeviceId();
  const navigation = useNavigation();
  const [, setClassicMode] = useClassicMode()
  const [switchLed] = useSwitch()
  const state = useReactive<ClassicModeState>({
    originMoods: [],
    filterMoods: [],
    showAddMoodPopover: false,
    loading: false,
    staticTagChecked: true,
    dynamicTagChecked: true,
    sceneStatusId: params.sceneStatusId
  })

  useEffect(() =>{
    getModeList()
  }, [])

  useEffect(() => {
    state.filterMoods = state.originMoods.filter(item => {
      return (
        (state.staticTagChecked && state.dynamicTagChecked) ||
        (!state.staticTagChecked && !state.dynamicTagChecked) ||
        (state.staticTagChecked && item.mode === 0) ||
        (state.dynamicTagChecked && item.mode !== 0)
      );
    });
  }, [state.staticTagChecked, state.dynamicTagChecked, JSON.stringify(state.originMoods)]);

  const getModeList = (isRefresh?: boolean) =>{
    state.loading = true
    getRemoteModeList(devId, isRefresh).then(res =>{
      state.loading = false
      if (res.success && Array.isArray(res.data)){
        state.originMoods = cloneDeep(res.data)
      }
    }).catch(() => {
      state.loading = false
    })
  }

  const modeIds = useMemo(() => {
    const ids: number[] = map(state.originMoods, 'id')
    return ids
  }, [JSON.stringify(state.originMoods)])

  const nameRepeat = useCallback((mood: MoodUIInfo) =>{
    return !!state.originMoods.filter(m => m.id !== mood.id).find(n => n.name === mood.name)
  }, [JSON.stringify(state.originMoods)])

  const onAddMoodDialogItemClick = (isStatic: boolean, _:number) =>{
    navigationRoute(isStatic, 'add')
    state.showAddMoodPopover = false
  }

  const navigationRoute = (isStatic: boolean, mode: 'add' | 'edit', currentMode?: MoodUIInfo) =>{
    const path = mode === 'add' ? RouterKey.classic_mode_add : RouterKey.classic_mode_edit
    navigation.navigate(path, {
      mode,
      isStatic,
      currentMode,
      modeIds,
      moduleParams: params,
      nameRepeat,
      modeDeleteMode
    })
  }

  const modeDeleteMode = async (mode: 'add' | 'edit' | 'del' | 'set', currentMode: MoodUIInfo) =>{
    let newScene: MoodUIInfo[] = [];
    if (mode === 'add'){
      newScene = [currentMode, ...state.originMoods]
    }
    if (mode === 'del'){
      newScene = state.originMoods.filter(item => item.id !== currentMode.id)
    }
    if (mode === 'set'){
      await params.setSceneStatusId(currentMode.id)
      state.sceneStatusId = currentMode.id
      return setClassicMode(currentMode)
    }
    if (mode === 'edit'){
      newScene = state.originMoods.map(item => {
        if (item.id === currentMode.id){
          return currentMode
        }
        return item
      })
    }
    const mood = mode === 'del' ? (newScene.length === 0 ? undefined : newScene[0]) : currentMode
    const res = await saveRemoteModeList(devId, newScene)
    if (res.success){
      state.originMoods = cloneDeep(newScene)
      if (mood){
        if (mode === 'del'){
          if (state.sceneStatusId !== -1 && (state.sceneStatusId === currentMode.id)){
            await params.setSceneStatusId(mood.id)
            state.sceneStatusId = mood.id
            return setClassicMode(mood)
          }
          return {success: true}
        }else{
          await params.setSceneStatusId(mood.id)
          state.sceneStatusId = mood.id
          return setClassicMode(mood)
        }
      }else{
        await params.setSceneStatusId(-1)
        state.sceneStatusId = -1
        return {
          success: true
        }
      }
    }
    return {success: false}
  }

  const styles = StyleSheet.create({
    tagLine: {
      flexDirection: 'row',
      marginHorizontal: cx(24),
    },
    addMoodPopover: {
      position: 'absolute',
      right: cx(60),
      top: Platform.OS === 'android' ? cx(90) : cx(130),
      maxWidth: cx(200),
      backgroundColor: props.theme?.global.background,
    },
    popoverItem: {
      padding: cx(5),
      alignItems: 'flex-start',
      alignSelf: 'flex-start'
    },
  })

  return (
    <>
      <Page
        backText={deviceInfo.name}
        headlineText={I18n.getLang('mood_overview_headline_text')}
        headlineIcon={state.originMoods.length < MAX_MOOD_COUNT ? res.add : undefined}
        onHeadlineIconClick={() => {
          state.showAddMoodPopover = !state.showAddMoodPopover;
        }}
        loading={state.loading}
      >
        {<View style={styles.tagLine}>
          <Tag
            checked={state.staticTagChecked}
            text={I18n.getLang('mood_overview_filter_name_text1')}
            onCheckedChange={checked => {
              state.staticTagChecked = checked;
            }}
          />
          <Spacer width={cx(8)} height={0} />
          <Tag
            checked={state.dynamicTagChecked}
            text={I18n.getLang('mood_overview_filter_name_text2')}
            onCheckedChange={checked => {
              state.dynamicTagChecked = checked;
            }}
          />
        </View>}
        <TouchableOpacity style={{ alignItems: 'flex-end',paddingRight: cx(24) }}
          onPress={() => {
            showDialog({
              method: 'confirm',
              title: I18n.getLang('mood_resetbutton'),
              subTitle: I18n.getLang('reset_mooddescription'),
              onConfirm: (_, { close }) => {
                close()
                getModeList(true)
              }
            })
          }}
        >
          <Image source={{ uri: res.ic_refresh}} style={{ width: cx(24), height: cx(24), tintColor:  props.theme?.global.fontColor}} />
        </TouchableOpacity>
        <Spacer height={cx(10)} />
        {state.originMoods.length >= MAX_MOOD_COUNT && (
          <View style={{ marginHorizontal: cx(24)}}>
            <Spacer height={cx(10)} />
            <InfoText
              icon={res.ic_warning_amber}
              text={I18n.getLang('mood_overview_warning_max_number_text')}
              contentColor={'#ff9500'}
            />
            <Spacer height={cx(6)} />
          </View>
        )}
        <FlatList
          data={state.filterMoods}
          renderItem={({ item }) => {
            return (
              <ModeItem
                enable={state.sceneStatusId === item.id && switchLed}
                mood={item}
                onPress={() => {
                  navigationRoute(item.mode === 0, 'edit', item);
                }}
                onSwitch={async _ => {
                  state.loading = true;
                  await modeDeleteMode('set', item);
                  state.loading = false;
                }}
              />
            );
          }}
          ListHeaderComponent={() => <Spacer height={cx(10)} />}
          ItemSeparatorComponent={() => <Spacer />}
          ListFooterComponent={() => <Spacer />}
          keyExtractor={item => `${item.id}`}
        />
      </Page>
      <CustomListDialog
        show={state.showAddMoodPopover}
        style={styles.addMoodPopover}
        itemStyle={styles.popoverItem}
        onDismiss={() => {
          state.showAddMoodPopover = false;
        }}
        data={[
          {
            text: I18n.getLang('mood_overview_add_mood_text'),
            value: true,
          },
          {
            text: I18n.getLang('mood_overview_add_mood_text2'),
            value: false,
          },
        ]}
        onItemPress={onAddMoodDialogItemClick}
      />
    </>
  )
}

export default withTheme(ClassicModePage)
