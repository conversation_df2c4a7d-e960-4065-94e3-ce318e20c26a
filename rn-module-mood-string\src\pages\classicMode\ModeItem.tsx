import React, { useCallback } from 'react';
import { StyleSheet, Text, View, ViewProps, ViewStyle } from 'react-native';
import { SwitchButton, Utils } from 'tuya-panel-kit';
import { hsv2Hex } from '@ledvance/base/src/utils';
import { mapFloatToRange } from '@ledvance/base/src/utils';
import Card from '@ledvance/base/src/components/Card';
import Spacer from '@ledvance/base/src/components/Spacer';
import MoodColorsLine from '@ledvance/base/src/components/MoodColorsLine';
import { MoodUIInfo } from './ClassicModeActions'
import I18n from '@ledvance/base/src/i18n';

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils;

interface MoodItemProps extends ViewProps {
  theme?: any
  enable: boolean;
  mood: MoodUIInfo;
  style?: ViewStyle;
  onPress?: () => void;
  onSwitch: (enable: boolean) => void;
}

const MoodItem = (props: MoodItemProps) => {
  const { mood } = props;

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
    },
    headline: {
      flexDirection: 'row',
      marginHorizontal: cx(16),
    },
    headText: {
      flex: 1,
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
      lineHeight: cx(20),
    },
    gradientItem: {
      flexDirection: 'row',
    },
    gradientItemIconView: {
      width: cx(24),
      height: cx(24),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#aaa',
      borderRadius: cx(8),
    },
    gradientItemIcon: {
      width: cx(16),
      height: cx(16),
      tintColor: props.theme.global.background,
    },
    gradient: {
      borderRadius: cx(8),
    },
    moodTypeItem: {
      flexDirection: 'row',
    },
    moodTypeLabel: {
      marginStart: cx(16),
      paddingHorizontal: cx(12.5),
      backgroundColor: props.theme.tag.background,
      borderRadius: cx(8),
    },
    moodTypeLabelText: {
      height: cx(16),
      color: '#000000DD',
      fontSize: cx(10),
      textAlignVertical: 'center',
      fontFamily: 'helvetica_neue_lt_std_roman',
      lineHeight: cx(16),
    },
  });

  const mixMoodColorsLine = useCallback((props: { mood: MoodUIInfo, type: 'gradient' | 'separate' }) => {
    const { mood } = props;
    const lightColors = mood.nodes.map(n => {
      const s = Math.round(mapFloatToRange(n.s / 100, 30, 100));
      return hsv2Hex(n.h, s, Math.round(mapFloatToRange(n.v / 100, 50, 100)));
    })

    return (
      <View style={styles.gradientItem}>
        <Spacer height={0} width={cx(16)} />
        <MoodColorsLine
          nodeStyle={{ borderColor: '#ccc', borderWidth: 1 }}
          width={undefined}
          type={props.type}
          colors={lightColors}
        />
      </View>
    );
  }, [])

  return (
    <Card style={[styles.card, props.style]} onPress={props.onPress}>
      <View>
        <Spacer height={cx(16)} />
        <View style={styles.headline}>
          <Text style={styles.headText}>{mood.name}</Text>
          <SwitchButton
            thumbStyle={{ elevation: 0 }}
            value={props.enable}
            onValueChange={props.onSwitch}
          />
        </View>
        <Spacer />
        {!!mood.nodes.length && <>
          {mixMoodColorsLine({ mood, type: 'separate' })}
          <Spacer height={cx(12)} />
        </>}
        <View style={styles.moodTypeItem}>
          <View style={styles.moodTypeLabel}>
            <Text style={styles.moodTypeLabelText}>
              {I18n.getLang(
                mood.mode !== 0 ? 'mood_overview_field_chip_2' : 'mood_overview_field_chip_text'
              )}
            </Text>
          </View>
        </View>
        <Spacer height={cx(16)} />
      </View>
    </Card>
  );
};

export default withTheme(MoodItem);

