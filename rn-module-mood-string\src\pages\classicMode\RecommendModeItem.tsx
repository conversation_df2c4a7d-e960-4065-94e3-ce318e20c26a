import React, { useCallback } from 'react';
import { Utils } from 'tuya-panel-kit';
import Card from '@ledvance/base/src/components/Card';
import { CellContent } from '@ledvance/base/src/components/Cell';
import { StyleSheet, View } from 'react-native';
import Spacer from '@ledvance/base/src/components/Spacer';
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils';
import MoodColorsLine from '@ledvance/base/src/components/MoodColorsLine';
import { RecommendModes } from './ClassicModeInfo';

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface RecommendMixMoodItemProps {
  theme?: any
  title: string;
  mood: RecommendModes;
  onPress: () => void;
}

const RecommendMixMoodItem = (props: RecommendMixMoodItemProps) => {
  const { mood } = props;

  const mixMoodColorsLine = useCallback(({ mood, type }) => {
    const lightColors = mood.nodes.map(n => {
      return hsv2Hex(n.h, Math.round(n.s), Math.round(mapFloatToRange(n.v / 100, 50, 100)));
    })

    return (
      <View style={{ flexDirection: 'row' }}>
        <Spacer height={0} width={cx(16)} />
        <MoodColorsLine
          nodeStyle={{ borderColor: props.theme.icon.disable, borderWidth: 1 }}
          width={undefined}
          type={type}
          colors={lightColors}
        />
      </View>
    );
  }, [])

  const styles = StyleSheet.create({
    root: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: cx(24),
    },
    content: {
      height: cx(56),
      marginHorizontal: cx(16),
      width: cx(295)
    },
    title: {
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    lineStyle: {
      alignItems: 'center',
    },
  });

  return (
    <Card style={styles.root} onPress={props.onPress}>
      <CellContent
        title={props.title}
        value={''}
        style={styles.content}
        titleStyle={styles.title}
        iconStyle={{
          color: props.theme.global.fontColor,
          size: cx(16),
        }}
      />
      {!!(mood.nodes) && (
        <>
          <View style={styles.lineStyle}>
            {mixMoodColorsLine({ mood, type: 'gradient' })}
          </View>
          <Spacer height={cx(24)} />
        </>
      )}
    </Card>
  );
};

export default withTheme(RecommendMixMoodItem);
