import { ScrollView } from 'react-native';
import React, {useCallback, useEffect} from 'react';
import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice';
import { Utils } from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import res from '@ledvance/base/src/res';
import {
  useAdvancedData,
  useBrightness,
  useColourData,
  useIsWhiteMode,
  useSwitch,
  useWorkMode
} from 'hooks/FeatureHooks';
import Page from '@ledvance/base/src/components/Page';
import { NativeApi } from '@ledvance/base/src/api/native';
import Card from '@ledvance/base/src/components/Card';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import I18n from '@ledvance/base/src/i18n';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import {useReactive, useThrottleFn, useUpdateEffect} from 'ahooks';
import { cloneDeep } from 'lodash';
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils';
import { useSceneStatusId } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodActions';
import { SceneStatusType, WorkMode } from '@ledvance/base/src/utils/interface';
import LampAdjustView from "@ledvance/base/src/components/LampAdjustView";
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

function HomePage() {
  const deviceInfo = useDeviceInfo();
  const [sceneStatusId, setSceneStatusId] = useSceneStatusId({isSupportSceneStatus: true, sceneStatusType: SceneStatusType.Mood})
  const [workMode, setWorkMode] = useWorkMode()
  const [isWhiteMode, setIsWhiteMode] = useIsWhiteMode()
  const advancedData = useAdvancedData(sceneStatusId, setSceneStatusId, workMode);
  const [switchLed, setSwitchLed] = useSwitch()
  const [colourData, setColourData] = useColourData()
  const [brightness, setBrightness] = useBrightness()
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')

  const state = useReactive({
    colourData: cloneDeep(colourData),
    brightness: brightness,
    isWhiteMode: isWhiteMode,
    flag: Symbol()
  })

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useUpdateEffect(() =>{
    state.colourData = cloneDeep(colourData)
  }, [JSON.stringify(colourData)])


  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = !state.isWhiteMode
    if (!isFocused || !isColorMode || gestureHue === undefined) {
      return
    }
    state.colourData.h = gestureHue
    runColour()
  }, [isFocused, state.isWhiteMode, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = !state.isWhiteMode
    if (isColorMode) {
      state.colourData.v = gestureBrightness
      runColour()
    } else {
      state.brightness = gestureBrightness
      runBrightness()
    }
  }, [isFocused, state.isWhiteMode, gestureBrightness])

  const { run: runColour } = useThrottleFn(() => {
    if (switchLed) {
      setColourData(state.colourData).then()
      setWorkMode(WorkMode.Scene).then()
      if (sceneStatusId !== -1){
        setSceneStatusId(-1).then()
      }
    }
  }, { wait: 500 })

  const { run: runBrightness } = useThrottleFn(() => {
    if (switchLed) {
      setBrightness(state.brightness).then()
    }
  }, { wait: 500 })

  useUpdateEffect(() =>{
    setColourData(state.colourData).then()
    setWorkMode(WorkMode.Scene).then()
    if (sceneStatusId !== -1){
      setSceneStatusId(-1).then()
    }
  }, [state.flag])

  const getBlockColor = useCallback(() => {
    if (state.isWhiteMode){
      return '#00000000'
    }
    const s = Math.round(mapFloatToRange(state.colourData.s / 100, 30, 100));
    return hsv2Hex(state.colourData.h, s, 100);
  }, [JSON.stringify(state.colourData),state.isWhiteMode])

  useUpdateEffect(() => {
    state.isWhiteMode = isWhiteMode;
  }, [isWhiteMode]);

  return (
    <Page
      backText={useFamilyName()}
      onBackClick={NativeApi.back}
      headlineText={deviceInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(deviceInfo.devId);
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer height={cx(16)} />
        <Card style={{ marginHorizontal: cx(24) }}>
          <LdvSwitch
            title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
            color={getBlockColor()}
            colorAlpha={1}
            enable={switchLed}
            setEnable={async (enable: boolean) => {
              await setSwitchLed(enable)
            }}
          />
          {switchLed && (
            <>
              <LampAdjustView
                isColorMode={!state.isWhiteMode}
                setIsColorMode={async (isColorMode) => {
                  await setIsWhiteMode(!isColorMode)
                  if (isColorMode) {
                    state.flag = Symbol()
                  }
                }}
                isSupportColor={true}
                isSupportTemperature={false}
                isSupportBrightness={true}
                brightness={state.brightness}
                onBrightnessChange={(brightness)=>{
                  state.brightness=brightness;
                }}
                onBrightnessChangeComplete={async (brightness) => {
                  state.brightness = brightness;
                  await setBrightness(brightness);
                }}
                h={state.colourData.h}
                s={state.colourData.s}
                v={state.colourData.v}
                reserveSV={true}
                minBrightness={1}
                minSaturation={1}
                onHSVChange={(h, s, v) => {
                  state.colourData.h = h
                  state.colourData.s = s
                  state.colourData.v = v
                }}
                onHSVChangeComplete={(h, s, v) => {
                  state.colourData.h = h
                  state.colourData.s = s
                  state.colourData.v = v
                  state.flag = Symbol()
                }} />
              <Spacer />
            </>
          )}
        </Card>
        <Spacer />
        <AdvanceList advanceData={advancedData} />
      </ScrollView>
    </Page>
  );
}

export default withTheme(HomePage)
