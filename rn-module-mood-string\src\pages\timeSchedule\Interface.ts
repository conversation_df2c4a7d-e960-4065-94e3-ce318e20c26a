import I18n from "@ledvance/base/src/i18n";
import { ModeInfo, MoodUIInfo } from "pages/classicMode/ClassicModeActions";

export interface Timer {
  enable: boolean;
  loops: string;
  time: string;
  id: number;
  notification: boolean;
  dps: Record<string, any>;
  name: string;
}

export type TimerActions = 'add' | 'delete' | 'update'

export enum UVCFanMode {
  Nature = 'nature',
  Normal = 'normal',
}

export interface HSV {
  h: number;
  s: number;
  v: number;
}

export type Category = 'light' | 'socket' | 'fan' | 'mainLight' | 'secondaryLight'

export interface ApplyForItem {
  name?: string 
  index?: number 
  key: string;
  dp: string;
  type: Category;
  enable: boolean;
}

interface judgmentSupport {
  isSupportColor: boolean;
  isSupportBrightness: boolean;
  isSupportTemperature: boolean;
  isCeilingLight?: boolean;
  isStripLight?: boolean;
  isStringLight?: boolean;
  isMixLight?: boolean;
  isFanLight?: boolean;
  isUVCFan?: boolean;
}

export interface ManualSettingProps extends judgmentSupport {
  theme?: any
  applyForList: ApplyForItem[];
  dps: Record<string, any>;
  manualData: ComponentConfig;
  onManualChange?: (manualData: PlugData | DeviceData | MixLightData | StripLightData) => void
  onApplyChange?: (applyForList: ApplyForItem[]) => void
}

export enum DeviceType {
  Plug = 'Plug',
  PowerStrip = 'PowerStrip',
  LightSource = 'lightSource',
  MixLight = 'mixLight',
  StripLight = 'stripLight',
  CeilingLight = 'ceilingLight',
  FanLight = 'fanLight'
}
// export type DeviceType = 'LightSource' | 'CeilingLight' | 'StringLight' | 'StripLight' | 'MixLight';

export interface PlugData {
  enable: boolean
}

export interface DeviceData {
  h: number;
  s: number;
  v: number;
  brightness: number;
  temperature: number;
  isColorMode: boolean;
}

export interface MixLightData extends DeviceData {
  colorLightSwitch: boolean;
  whiteLightSwitch: boolean;
  mixRgbcwEnabled: boolean
}

export interface StripLightData extends DeviceData {
  colors: string[];
  activeKey: number;
  colorDiskActiveKey: number
}

export interface FanLightData extends DeviceData {
  fanSpeed: number 
  direction: 'forward' | 'reverse'
  mode: 'nature' | 'normal'
  disinfect: boolean
}

export type ComponentConfig =
  | { type: DeviceType.LightSource; deviceData: DeviceData }
  | { type: DeviceType.MixLight; deviceData: MixLightData }
  | { type: DeviceType.StripLight; deviceData: StripLightData }
  | { type: DeviceType.CeilingLight; deviceData: StripLightData }
  | { type: DeviceType.FanLight; deviceData: FanLightData }
  | { type: DeviceType.PowerStrip; deviceData: DeviceData}

export interface TimeScheduleDetailState {
  timeSchedule: Timer;
  dps: Record<string, any>;
  isManual: boolean;
  initSelectedSkill: ApplyForItem[],
  selectedSkill: ApplyForItem[];
  unSelectedSkill: ApplyForItem[];
  loading: boolean;
  moodLoading: boolean;
  manualData: ComponentConfig;
  mood?: ModeInfo;
  moods: MoodUIInfo[];
  filterMoods: MoodUIInfo[];
  staticTagChecked: boolean;
  dynamicTagChecked: boolean;
  timerId: any;
  moodName: string;
}
export interface DeviceStateType {
  deviceData: ComponentConfig
  mood?: ModeInfo
  isManual: boolean
}

export const directOptions = [
  {label: I18n.getLang('ceiling_fan_tile_uvc_fan_direction_opt_1'), value: 'forward'},
  {label: I18n.getLang('ceiling_fan_tile_uvc_fan_direction_opt_2'), value: 'reverse'}
]

export const modeOptions = [
  {label: I18n.getLang('ceiling_fan_tile_uvc_fan_mode_opt_1'), value: 'normal'},
  {label: I18n.getLang('ceiling_fan_tile_uvc_fan_mode_opt_2'), value: 'nature'},
]
