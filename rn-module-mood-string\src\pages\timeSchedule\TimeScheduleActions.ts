import { NativeApi } from '@ledvance/base/src/api/native';
import { Timer, TimerActions } from './Interface';
import { parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import { ColorList } from '@ledvance/base/src/components/StripAdjustView';

export const defDeviceData = {
  h: 0,
  s: 100,
  v: 100,
  brightness: 100,
  temperature: 0,
};

export const defMixDeviceData = {
  ...defDeviceData,
  colorEnable: true,
  whiteEnable: true,
};

export const defStripDeviceData = {
  ...defDeviceData,
  activeKey: 1,
  colors: ColorList[0],
  colorDiskActiveKey: 0
};

export const defFanLightDeviceData = {
  ...defDeviceData,
  fanSpeed: 1,
  direction: 'forward',
  mode: 'normal',
  disinfect: false
}

export const getTimeSchedule = async (deviceId: string): Promise<Timer[]> => {
  const res = await NativeApi.getAllTaskTimer(deviceId);
  if (res.success && Array.isArray(res.data)) {
    return res.data.map(item => ({
      id: item?.mTimerId,
      name: item?.remark,
      loops: item?.mLoops,
      time: item?.mTime,
      dps: parseJSON(item?.mValue),
      enable: !!Number(item?.mStatus),
      notification: item?.isAppPush,
    }));
  }
  return [];
};

export const manageTimeSchedule = async (deviceId: string, schedule: Timer, mode: TimerActions) => {
  const res = await NativeApi.manageTimer(
    deviceId,
    {
      ...schedule,
      aliasName: schedule.name,
      status: schedule.enable,
    },
    mode
  );
  return res;
};
