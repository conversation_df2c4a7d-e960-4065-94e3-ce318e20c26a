import React, { useCallback, useEffect, useMemo } from 'react';
import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/core';
import Page from '@ledvance/base/src/components/Page';
import I18n from '@ledvance/base/src/i18n';
import TextField from '@ledvance/base/src/components/TextField';
import { cloneDeep, isEqual } from 'lodash';
import { useReactive } from 'ahooks';
import { SwitchButton, TimerPicker, Utils } from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import LdvWeekView from '@ledvance/base/src/components/weekSelect';
import { convertTo12HourFormat, loopText, showDialog } from '@ledvance/base/src/utils/common';
import {
  ApplyForItem,
  ComponentConfig,
  DeviceType,
  TimeScheduleDetailState,
  Timer,
  TimerActions,
} from './Interface';
import res from '@ledvance/base/src/res';
import {
  useDeviceId,
  useMoods,
  useSystemTimeFormate,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import { TimeSchedulePageParams } from './TimeSchedulePage';
import { Result } from '@ledvance/base/src/models/modules/Result';
import DeleteButton from '@ledvance/base/src/components/DeleteButton';
import InfoText from '@ledvance/base/src/components/InfoText';
import SegmentControl from '@ledvance/base/src/components/segmentControl';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import ManualSettings from './components/ManuaSettings';
import { defDeviceData } from './TimeScheduleActions';
import ModeItem from 'pages/classicMode/ModeItem';
import { getRemoteModeList, ModeInfo } from 'pages/classicMode/ClassicModeActions';
import { MoodUIInfo } from 'pages/classicMode/ClassicModeActions';
import Summary from '@ledvance/base/src/components/Summary'
import ThemeType from '@ledvance/base/src/config/themeType'
import Tag from "@ledvance/base/src/components/Tag";

const { convertX: cx } = Utils.RatioUtils;
const { toFixedString } = Utils.NumberUtils;
const { withTheme } = Utils.ThemeUtils

interface TimeScheduleDetailPageParams extends TimeSchedulePageParams {
  mode: 'add' | 'update';
  timeSchedule: Timer;
  modDeleteTimeSchedule: (mode: TimerActions, timeSchedule: Timer) => Promise<Result<any>>;
  refreshFn: () => void;
}

const TimeScheduleDetailPage = (props: { theme?: ThemeType }) => {
  const is24HourClock = useSystemTimeFormate();
  const params = useParams<TimeScheduleDetailPageParams>();
  const navigation = useNavigation();
  const devId = useDeviceId();
  const [moods, setMoods] = useMoods();
  const state = useReactive<TimeScheduleDetailState>({
    timeSchedule: params.mode === 'add' ? newTimeSchedule() : cloneDeep(params.timeSchedule),
    dps: params.mode === 'add' ? {} : params.timeSchedule.dps,
    isManual: true, // manual ,mood
    initSelectedSkill: [] as ApplyForItem[],
    selectedSkill: [] as ApplyForItem[],
    unSelectedSkill: [] as ApplyForItem[],
    loading: false,
    moodLoading: false,
    manualData:
      params.mode === 'add'
        ? getDefaultManual(params)
        : params.manualDataDp2Obj(params.timeSchedule.dps).deviceData,
    mood: params.mode === 'add' ? undefined : params.manualDataDp2Obj(params.timeSchedule.dps)?.mood,
    moods: cloneDeep(moods),
    filterMoods: cloneDeep(moods),
    staticTagChecked: true,
    dynamicTagChecked: true,
    timerId: undefined,
    moodName: '',
  });

  useEffect(() => {
    const { applyForDisabled, timeSchedule, applyForList, mode } = params;
    const cannotChoose = applyForDisabled || applyForList.length === 1;
    const cloneApplyList =
      mode === 'add'
        ? cloneDeep(applyForList)
        : cloneDeep(applyForList).map(item => {
          if (timeSchedule?.dps?.hasOwnProperty(item.dp)) {
            return {
              ...item,
              enable: timeSchedule?.dps[item.dp],
            };
          }
          return item;
        });

    if (cannotChoose) {
      state.selectedSkill = cloneApplyList;
      state.unSelectedSkill = [];
    } else {
      const selectedList = cloneApplyList.filter(item =>
        Object.keys(timeSchedule?.dps || {}).includes(item.dp)
      );
      const unSelectedList = cloneApplyList.filter(
        item => !Object.keys(timeSchedule?.dps || {}).includes(item.dp)
      );
      state.selectedSkill = mode === 'add' ? [] : selectedList;
      state.unSelectedSkill = mode === 'add' ? cloneApplyList : unSelectedList;
    }
    if (params.mode === 'update' && params.manualDataDp2Obj) {
      const { isManual, mood } = params.manualDataDp2Obj(params.timeSchedule.dps);
      state.isManual = isManual;
      state.mood = mood;
    }

    state.initSelectedSkill = cloneDeep(state.selectedSkill)

    if (!(Array.isArray(moods) && moods.length)) {
      state.timerId = setTimeout(() => {
        getRemoteModeList(devId).then(res => {
          if (res.success && Array.isArray(res.data)) {
            state.moods = cloneDeep(res.data);
            setMoods(cloneDeep(res.data));
            if (!state.mood) state.mood = cloneDeep(res.data[0]);
          }
        });
      }, 250);
      return () => {
        if (state.timerId) clearTimeout(state.timerId);
      };
    } else {
      if (!state.mood) state.mood = cloneDeep(state.moods[0]);
    }
  }, []);

  const getModeNode = (mood?: ModeInfo) =>{
    if (!mood) return {}
    return {
      mode: mood.mode,
      speed: mood.speed,
      bright: mood.bright,
      nodes: mood.nodes
    }
  }

  // mood 回显
  useEffect(() => {
    if (state.moods?.length) {
      state.moodName = state.moods.find(m => {
        return JSON.stringify(getModeNode(m)) === JSON.stringify(getModeNode(state.mood))
      })?.name || ''
    }
  }, [state.mood, state.moods]);

  useEffect(() => {
    state.filterMoods = state.moods.filter(item => {
      return (
          (state.staticTagChecked && state.dynamicTagChecked) ||
          (!state.staticTagChecked && !state.dynamicTagChecked) ||
          (state.staticTagChecked && item.mode === 0) ||
          (state.dynamicTagChecked && item.mode !== 0)
      );
    });
  }, [state.staticTagChecked, state.dynamicTagChecked, state.moods]);


  const getFormateTime = useCallback((time: number | string) => {
    if (typeof time === 'number') {
      return `${toFixedString(Math.trunc(time / 60), 2)}:${toFixedString(time % 60, 2)}`;
    }
    const t = time.split(':');
    return Number(t[0]) * 60 + Number(t[1]);
  }, []);

  const isModify = useMemo(() => {
    const schedule = params.mode === 'add' ? newTimeSchedule() : params.timeSchedule;
    const manual = params.mode === 'add' ? getDefaultManual(params) : params.manualDataDp2Obj(params.timeSchedule?.dps).deviceData;
    const before = {
      ...schedule,
      selectedSkill: state.initSelectedSkill,
      dps: manual,
      isManual: params.mode === 'add' || params.manualDataDp2Obj(params.timeSchedule.dps).isManual,
      mood: params.mode === 'add' ? undefined : params.manualDataDp2Obj(params.timeSchedule?.dps).mood,
      id: 1,
    };

    const now = {
      ...state.timeSchedule,
      selectedSkill: state.selectedSkill,
      dps: state.manualData,
      isManual: state.isManual,
      mood: state.isManual ? undefined : state.mood,
      id: 1,
    };
    return isEqual(before, now);
  }, [JSON.stringify(state.timeSchedule), state.manualData, state.isManual, state.mood, JSON.stringify(state.selectedSkill)]);



  const allowSubmit = useMemo(() => {
    return (
      !!(state.timeSchedule.name.length && state.timeSchedule.name.length < 33) &&
      !!state.selectedSkill.length &&
      (state.isManual || !!state.mood) &&
      (!isModify || params.mode === 'add')
    );
  }, [state.timeSchedule.name, state.selectedSkill, isModify, state.isManual, state.mood]);

  const showSelectedIcon = useMemo(() => {
    return params.applyForList.length !== 1 && !params.applyForDisabled;
  }, [params.applyForList.length, params.applyForDisabled]);

  const getMoodItemEnable = useCallback((item: MoodUIInfo) => {
    return JSON.stringify(getModeNode(item)) === JSON.stringify(getModeNode(state.mood))
  }, [JSON.stringify(state.mood)])


  const styles = StyleSheet.create({
    cardContainer: {
      marginHorizontal: cx(24),
    },
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    applyContent: {
      backgroundColor: props.theme?.container.background,
      borderRadius: 4,
      minHeight: cx(55),
      flex: 1,
      justifyContent: 'center',
      paddingTop: cx(10),
      paddingHorizontal: cx(10),
    },
    applyItem: {
      paddingLeft: cx(5),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: props.theme?.global.background,
      height: cx(35),
    },
    moodScrollView: {
      maxHeight: cx(500),
    },
    summaryContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      marginBottom: cx(10),
      marginHorizontal: cx(24),
    },
    summaryImg: {
      height: cx(12),
      width: cx(12),
      tintColor: props.theme?.global.fontColor,
    },
    summaryLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      minWidth: cx(90),
    },
    leftTitle: {
      fontSize: cx(14),
      color: props.theme?.global.fontColor,
      marginTop: 0,
    },
    summaryRight: {
      flexDirection: 'column',
      backgroundColor: props.theme?.tag.background,
      borderRadius: cx(16),
      alignItems: 'center'
    },

    rightItem: {
      paddingHorizontal: cx(12),
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    },
    switchButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: cx(10),
    },
    text: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    },
    tagLine: {
      flexDirection: 'row',
      marginHorizontal: cx(24),
    }
  });

  return (
    <Page
      backText={I18n.getLang('motion_detection_add_time_schedule_system_back_text')}
      headlineText={I18n.getLang(
        params.mode === 'add'
          ? 'motion_detection_add_time_schedule_headline_text'
          : 'edit_timeschedule_headline_text'
      )}
      rightButtonIcon={allowSubmit ? res.ic_check : res.ic_uncheck}
      rightButtonIconClick={async () => {
        if (!allowSubmit || state.loading) return;
        state.loading = true;
        const res = await params.modDeleteTimeSchedule(params.mode, {
          ...state.timeSchedule,
          enable: true,
          dps: params.manualDataObj2Dp(
            {
              deviceData: state.manualData,
              isManual: state.isManual,
              mood: state.mood,
            },
            state.selectedSkill
          ),
        });
        state.loading = false;
        if (res.success) {
          navigation.goBack();
        }
      }}
      backDialogTitle={I18n.getLang('cancel_dialog_leave_unsaved_titel')}
      backDialogContent={I18n.getLang('cancel_dialog_leave_unsaved_timeschedule_note')}
      showBackDialog={!isModify}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <TextField
          style={styles.cardContainer}
          value={state.timeSchedule.name}
          showError={state.timeSchedule.name?.length > 32}
          maxLength={33}
          errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}
          placeholder={I18n.getLang('add_new_trigger_time_inputfield_value_text')}
          onChangeText={(t: string) => {
            state.timeSchedule.name = t;
          }}
        />

        {/* pick */}
        <TimerPicker
          itemTextColor="#aeadb5"
          style={{ paddingVertical: cx(0), marginVertical: cx(0), backgroundColor: props.theme?.global.background }}
          pickerFontColor={props.theme?.global.fontColor}
          is12Hours={!is24HourClock}
          singlePicker={true}
          amText={I18n.getLang('manage_user_calendar_label_am')}
          pmText={I18n.getLang('manage_user_calendar_label_pm')}
          startTime={getFormateTime(state.timeSchedule.time) as number}
          symbol={''}
          onTimerChange={startTime => {
            state.timeSchedule.time = getFormateTime(startTime) as string;
          }}
        />

        <LdvWeekView
          value={state.timeSchedule.loops.split('').map(Number)}
          style={styles.cardContainer}
          onSelect={(index: number) => {
            const rawIndex = index - 1;
            const weeks = state.timeSchedule.loops.split('');
            weeks[rawIndex] = weeks[rawIndex] === '1' ? '0' : '1';
            state.timeSchedule.loops = weeks.join('');
          }}
        />
        <Spacer />
        <Text style={{ ...styles.cardContainer, color: props.theme?.global.fontColor, fontSize: cx(14) }}>{loopText(state.timeSchedule.loops.split(''))}</Text>
        <Spacer height={cx(30)} />

        {/* Apply for */}
        <View style={styles.cardContainer}>
          <Text style={styles.itemTitle}>
            {I18n.getLang('timeschedule_add_schedule_subheadline_text')}
          </Text>
          <Spacer height={cx(10)} />
          <View style={[styles.applyContent, { paddingTop: state.selectedSkill.length ? cx(10) : 0 }]}>
            {state.selectedSkill.length === 0 ? (
              <Text style={{color: props.theme?.global.fontColor}}>{I18n.getLang('timer_ceiling_fan_selectionfield_no_components_text')}</Text>
            ) : (
              state.selectedSkill.map(skill => (
                <View
                  style={[styles.applyItem, { marginBottom: cx(10), borderRadius: 4 }]}
                  key={skill.dp}
                >
                  <Text style={{ color: props.theme?.global.fontColor, fontSize: cx(12) }}>{skill.key}</Text>
                  {showSelectedIcon && (
                    <TouchableOpacity
                      onPress={() => {
                        state.selectedSkill = state.selectedSkill.filter(s => skill.dp !== s.dp);
                        state.unSelectedSkill = [...state.unSelectedSkill, skill];
                      }}
                      style={{ paddingHorizontal: cx(5) }}
                    >
                      <Image
                        style={{ width: cx(16), height: cx(16), tintColor: props.theme?.global.fontColor }}
                        source={{ uri: res.ic_arrows_nav_clear}}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              ))
            )}
          </View>
          {state.unSelectedSkill.map((item: ApplyForItem) => {
            return (
              <TouchableOpacity
                style={styles.applyItem}
                key={item.dp}
                onPress={() => {
                  state.selectedSkill = [...state.selectedSkill, item];
                  state.unSelectedSkill = state.unSelectedSkill.filter(s => item.dp !== s.dp);
                }}
              >
                <Text style={{ color: props.theme?.global.fontColor, fontSize: cx(12) }}>{item.key}</Text>
                <Image
                  style={{ width: cx(16), height: cx(16), tintColor: props.theme?.global.fontColor }}
                  source={{ uri: res.device_panel_timer_add}}
                />
              </TouchableOpacity>
            );
          })}
          <Spacer />
        </View>

        {/* device state */}
        <Text style={[styles.itemTitle, styles.cardContainer]}>
          {I18n.getLang('timeschedule_add_schedule_subheadline2_text')}
        </Text>
        <Spacer height={cx(10)} />
        {params.isSupportMood && (
          <>
            <SegmentControl
              title1={I18n.getLang('timeschedule_add_schedule_switch_tab_manual_text')}
              title2={I18n.getLang('timeschedule_add_schedule_switch_tab_mood_text')}
              isFirst={state.isManual}
              setIsFirst={(v: boolean) => (state.isManual = v)}
            />
            <Spacer height={cx(16)} />
          </>
        )}
        <Spacer height={cx(10)} />
        {!state.selectedSkill.length && <InfoText
            style={{ marginHorizontal: cx(24) }}
            icon={res.ic_warning_amber}
            contentColor="#FF9500"
            text={I18n.getLang('timeschedule_add_schedule_no_device_warning_text')}
            textStyle={{fontSize: cx(12)}}
        />}
        {state.isManual ? (
          <ManualSettings
            dps={state.dps}
            applyForList={state.selectedSkill}
            isSupportColor={params.isSupportColor}
            isSupportBrightness={params.isSupportBrightness}
            isSupportTemperature={params.isSupportTemperature}
            manualData={state.manualData}
            onManualChange={manual => {
              state.manualData = {
                ...state.manualData,
                // @ts-ignore
                deviceData: cloneDeep(manual),
              };
            }}
            onApplyChange={apply => {
              state.selectedSkill = cloneDeep(apply);
            }}
          />
        ) : (
            <View>
              <View style={styles.tagLine}>
                <Tag
                    checked={state.staticTagChecked}
                    text={I18n.getLang('mood_overview_filter_name_text1')}
                    onCheckedChange={checked => {
                      state.staticTagChecked = checked;
                    }}
                />
                <Spacer width={cx(8)} height={0}/>
                <Tag
                    checked={state.dynamicTagChecked}
                    text={I18n.getLang('mood_overview_filter_name_text2')}
                    onCheckedChange={checked => {
                      state.dynamicTagChecked = checked;
                    }}
                />
              </View>
              <FlatList
                  data={state.filterMoods}
                  renderItem={({item}) => {
                    return (
                        <ModeItem
                            enable={getMoodItemEnable(item)}
                            mood={item}
                            onSwitch={_ => {
                              state.mood = cloneDeep(item);
                            }}
                        />
                    );
                  }}
                  ListHeaderComponent={() => <Spacer height={cx(10)}/>}
                  ItemSeparatorComponent={() => <Spacer/>}
                  ListFooterComponent={() => <Spacer/>}
                  keyExtractor={item => `${item.name}`}
              />
            </View>
        )}
        <Spacer />

        {/* settings */}
        <Text style={[styles.itemTitle, styles.cardContainer]}>
          {I18n.getLang('timeschedule_add_schedule_subheadline4_text')}
        </Text>
        <View style={[styles.switchButton, styles.cardContainer]}>
          <Text style={styles.text}>{I18n.getLang('timeschedule_add_schedule_text2')}</Text>
          <SwitchButton
            value={state.timeSchedule.notification}
            onValueChange={value => {
              state.timeSchedule.notification = value;
            }}
          />
        </View>
        <Spacer />

        {/* summary */}
        <Summary
          frequency={loopText(state.timeSchedule.loops.split(''), state.timeSchedule.time)}
          time={is24HourClock
            ? state.timeSchedule.time
            : convertTo12HourFormat(state.timeSchedule.time)}
          actions={<View style={{ flex: 1 }}>
            {state.isManual
              ? !!state.selectedSkill.length && (
              <>
                {!!state.selectedSkill.filter(skill => skill.enable).length && (
                  <>
                    <Text style={{ fontSize: cx(14), color: props.theme?.global.fontColor }}>
                      {I18n.getLang('feature_summary_action_txt_1')}
                    </Text>
                    <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                      {state.selectedSkill
                        .filter(skill => skill.enable)
                        .map(item => (
                          <View
                            style={[
                              styles.summaryRight,
                              { marginRight: cx(5), marginBottom: cx(5) },
                            ]}
                            key={item.dp}
                          >
                            <Text style={[styles.rightItem]}>{item.key}</Text>
                          </View>
                        ))}
                    </View>
                  </>
                )}

                {!!state.selectedSkill.filter(skill => !skill.enable).length && (
                  <>
                    <Text style={{ fontSize: cx(14), color: props.theme?.global.fontColor }}>
                      {I18n.getLang('feature_summary_action_txt_2')}
                    </Text>
                    <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                      {state.selectedSkill
                        .filter(skill => !skill.enable)
                        .map((item) => (
                          <View
                            style={[
                              styles.summaryRight,
                              { marginRight: cx(5), marginBottom: cx(5) },
                            ]}
                            key={item.dp}
                          >
                            <Text style={[styles.rightItem]}>{item.key}</Text>
                          </View>
                        ))}
                    </View>
                  </>
                )}
              </>
            )
              : !!state.moodName && (
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                <View style={[styles.summaryRight, { marginLeft: cx(5) }]}>
                  <Text style={styles.rightItem}>{state.moodName}</Text>
                </View>
              </View>
            )}
          </View>}
        />
        <Spacer height={cx(30)} />

        {/* delete */}
        {params.mode === 'update' && (
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              text={I18n.getLang('edit_timeschedule_bttn_text')}
              onPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('cancel_dialog_delete_item_timeschedule_titel'),
                  subTitle: I18n.getLang('cancel_dialog_delete_item_timeschedule_description'),
                  onConfirm: async (_, { close }) => {
                    state.loading = true;
                    close();
                    const res = await params.modDeleteTimeSchedule('delete', state.timeSchedule);
                    state.loading = false;
                    if (res.success) {
                      navigation.goBack();
                    }
                  },
                });
              }}
            />
            <Spacer />
          </View>
        )}
      </ScrollView>
    </Page>
  );
};

const newTimeSchedule = () => {
  return {
    enable: true,
    loops: '0000000',
    notification: false,
    name: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    time: `${toFixedString(new Date().getHours(), 2)}:${toFixedString(new Date().getMinutes(), 2)}`,
    dps: {},
    id: -1,
  };
};

const getDefaultManual = (props: TimeScheduleDetailPageParams): ComponentConfig => {
  return {
    type: DeviceType.LightSource,
    deviceData: {
      ...defDeviceData,
      isColorMode: props.isSupportColor,
    },
  };
};

export default withTheme(TimeScheduleDetailPage)
