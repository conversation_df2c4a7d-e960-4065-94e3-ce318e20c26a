import React, { useCallback, useEffect, useMemo } from 'react';
import { ScrollView, Text, StyleSheet, FlatList, View, Image } from 'react-native';
import { useNavigation } from '@react-navigation/core';
import { Utils } from 'tuya-panel-kit';
import Page from '@ledvance/base/src/components/Page';
import I18n from '@ledvance/base/src/i18n';
import {
  useDeviceId,
  useDeviceInfo,
  useTimeSchedule,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import Spacer from '@ledvance/base/src/components/Spacer';
import InfoText from '@ledvance/base/src/components/InfoText';
import DeleteButton from '@ledvance/base/src/components/DeleteButton';
import { useReactive, useUpdateEffect } from 'ahooks';
import { ApplyForItem, DeviceStateType, Timer, TimerActions } from './Interface';
import { getTimeSchedule, manageTimeSchedule } from './TimeScheduleActions';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import ScheduleCard from './components/ScheduleCard';
import { RouterKey } from '../../navigation/Router';
import { cloneDeep } from 'lodash';
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils
const MAX_NUM = 30

export interface TimeSchedulePageParams {
  isSupportColor: boolean;
  isSupportTemperature: boolean;
  isSupportBrightness: boolean;
  isSupportMood?: boolean;
  applyForList: ApplyForItem[];
  applyForDisabled?: boolean; // 是否可以选择apply for
  manualDataDp2Obj: (dps: Record<string, any>) => DeviceStateType;
  manualDataObj2Dp: (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => Record<string, any>;
}

const TimeSchedulePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo();
  const devId = useDeviceId();
  const navigation = useNavigation();
  const [timeScheduleStatus, setTimeScheduleStatus] = useTimeSchedule();
  const params = useParams<TimeSchedulePageParams>();
  const state = useReactive({
    timeScheduleList: [] as Timer[],
    originList: [] as Timer[],
    flag: Symbol(),
    loading: false,
    checkTags: params.applyForList.map(v => ({ ...v, enable: false }))
  });

  const isMaxSchedule = useMemo(() => state.originList.length >= MAX_NUM, [
    state.originList,
  ]);

  useUpdateEffect(() => {
    const status = state.originList.some(item => item.enable);
    if (status !== timeScheduleStatus) {
      setTimeScheduleStatus(status);
    }
  }, [JSON.stringify(state.originList)]);

  useEffect(() => {
    getTimeSchedule(devId).then(res => {
      console.log(res, '< --- timeSchedule --- >')
      state.originList = cloneDeep(res)
    });
  }, [state.flag]);

  useUpdateEffect(() => {
    const isAll = state.checkTags.every(tag => tag.enable) || state.checkTags.every(tag => !tag.enable)
    const checkedList = state.originList.filter(item => {
      const checked = state.checkTags.find(tag => tag.enable && item.dps.hasOwnProperty(tag.dp))
      return isAll || !!checked
    })
    state.timeScheduleList = cloneDeep(checkedList)
  }, [JSON.stringify(state.checkTags), JSON.stringify(state.originList)])

  const modDeleteTimeSchedule = async (mode: TimerActions, timeSchedule: Timer) => {
    const res = await manageTimeSchedule(devId, timeSchedule, mode);
    if (res.success) {
      if (mode === 'add') {
        state.flag = Symbol();
      } else {
        state.originList =
          mode === 'update'
            ? state.originList.map(item => {
              if (item.id === timeSchedule.id) {
                return timeSchedule;
              }
              return item;
            })
            : state.originList.filter(item => item.id !== timeSchedule.id);
      }
      return {
        success: true,
      };
    }
    return res;
  };

  const navigateToEdit = useCallback((mode: 'add' | 'update', timeSchedule?: Timer) => {
    const path = RouterKey.time_schedule_edit;
    navigation.navigate(path, {
      mode,
      name: path,
      timeSchedule,
      modDeleteTimeSchedule,
      refreshFn: () => {
        state.flag = Symbol();
      },
      ...params,
    });
  }, []);

  const styles = StyleSheet.create({
    overviewDescription: {
      color: props.theme?.global.fontColor,
      marginHorizontal: cx(24),
    },
    emptyImage: {
      width: cx(225),
      height: cx(198),
    },
    emptyContainer: {
      marginHorizontal: cx(24),
      alignItems: 'center',
    },
    addBtn: {
      height: cx(40),
      width: 'auto',
      minWidth: cx(150),
      paddingHorizontal: cx(16),
      backgroundColor: props.theme?.button.primary,
    },
    categoryList: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginHorizontal: cx(24),
      alignSelf: 'flex-start'
    },
  });

  return (
    <Page
      backText={deviceInfo.name}
      onBackClick={navigation.goBack}
      headlineText={I18n.getLang('timeschedule_overview_headline_text')}
      headlineIcon={isMaxSchedule ? undefined : res.device_panel_schedule_add}
      onHeadlineIconClick={() => {
        navigateToEdit('add');
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Text style={styles.overviewDescription}>
          {I18n.getLang('timeschedule_overview_description_text')}
        </Text>
        <Spacer height={cx(10)} />
        {isMaxSchedule && (
          <InfoText
            text={I18n.getLang('motion_detection_time_schedule_notifications_warning_text')}
            icon={res.ic_warning_amber}
            style={{ marginHorizontal: cx(24) }}
            textStyle={{ color: props.theme?.global.fontColor }}
            iconStyle={{ tintColor: props.theme?.global.warning }}
          />
        )}
        {state.timeScheduleList.length > 0 ? <FlatList
          data={state.timeScheduleList}
          renderItem={({ item }) => (
            <ScheduleCard
              item={item}
              tags={params.applyForList}
              showTag={params.applyForList.length > 1}
              onEnableChange={async enable => {
                state.loading = true;
                await modDeleteTimeSchedule('update', {
                  ...item,
                  enable,
                });
                state.loading = false;
              }}
              onPress={() => {
                navigateToEdit('update', item);
              }}
            />
          )}
          keyExtractor={item => item.id.toString()}
          ListEmptyComponent={<Spacer />}
          ListHeaderComponent={() => <Spacer height={cx(10)} />}
          ItemSeparatorComponent={() => <Spacer />}
          ListFooterComponent={() => <Spacer height={cx(30)} />}
        /> :
          <View style={styles.emptyContainer}>
            <Spacer height={cx(60)} />
            <Image
              style={styles.emptyImage}
              source={{ uri: res.ldv_timer_empty }}
              resizeMode="contain"
            />
            <InfoText
              icon={res.device_panel_schedule_alert}
              text={I18n.getLang(!state.originList.length ? 'timeschedule_overview_empty_information_text' : 'sleepwakeschedule_empty_filtering_information_text')}
              style={{ width: 'auto', alignItems: 'center' }}
              textStyle={{ color: props.theme?.global.fontColor, flex: undefined }}
              iconStyle={{ tintColor: props.theme?.global.fontColor }}
            />
            <Spacer height={cx(16)} />
            {!state.originList.length && <DeleteButton
              style={styles.addBtn}
              text={`${I18n.getLang('timeschedule_overview_empty_button_add_text')}`}
              textStyle={{ fontSize: cx(12) }}
              onPress={() => {
                navigateToEdit('add');
              }}
            />}
          </View>}
      </ScrollView>
    </Page>
  );
};

export default withTheme(TimeSchedulePage)
