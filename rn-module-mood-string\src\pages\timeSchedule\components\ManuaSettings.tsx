import React, { memo, useMemo } from 'react';
import { directOptions, FanLightData, ManualSettingProps, modeOptions } from '../Interface';
import { View } from 'react-native';
import Card from '@ledvance/base/src/components/Card';
import LampAdjustView from '@ledvance/base/src/components/LampAdjustView';
import { Utils } from 'tuya-panel-kit';
import { useReactive, useUpdateEffect } from 'ahooks';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import Spacer from '@ledvance/base/src/components/Spacer';
import { FanAdjustViewContent } from '@ledvance/base/src/components/FanAdjustView';
import {cctToColor} from "@ledvance/base/src/utils/cctUtils";
import {hsv2Hex, mapFloatToRange} from "@ledvance/base/src/utils";
const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

function ManualSettings(props: ManualSettingProps) {
  const state = useReactive({
    deviceData: props.manualData.deviceData,
    applyForList: props.applyForList,
    applyFlag: Symbol(),
    manualFlag: Symbol()
  });

  useUpdateEffect(() => {
    state.applyForList = props.applyForList;
    state.deviceData = props.manualData.deviceData;
  }, [props.applyForList, props.manualData]);

  useUpdateEffect(() => {
    props.onApplyChange && props.onApplyChange(state.applyForList)
  }, [state.applyFlag])

  useUpdateEffect(() => {
    props.onManualChange && props.onManualChange(state.deviceData)
  }, [state.manualFlag])


  const lightSourceCard = useMemo(() => {
    const { deviceData } = state;
    const getBlockColor = () => {
      if (!deviceData.isColorMode) return cctToColor(deviceData.temperature)
      if (deviceData.isColorMode) {
        const s = Math.round(mapFloatToRange(deviceData.s / 100, 30, 100))
        return hsv2Hex(deviceData.h, s, 100)
      }
      return props.theme?.card?.background
    };
    return (
      <View>
        {state.applyForList.map((item, idx) => (
          <View key={item.dp}>
            <Card style={{ marginHorizontal: cx(24) }}>
              <LdvSwitch
                title={item.name || item.key}
                color={getBlockColor()}
                colorAlpha={1}
                enable={item.enable}
                setEnable={(enable: boolean) => {
                  state.applyForList = state.applyForList.map((apply, index) => {
                    if (idx === index) {
                      return {
                        ...apply,
                        enable,
                      };
                    }
                    return apply;
                  });
                  state.applyFlag = Symbol()
                }}
              />
              {item.enable && (item.type !== 'socket' && item.type !== 'fan') && (
                <LampAdjustView
                  isSupportColor={props.isSupportColor}
                  isSupportBrightness={props.isSupportBrightness}
                  isSupportTemperature={props.isSupportTemperature}
                  isColorMode={deviceData.isColorMode}
                  setIsColorMode={mode => {
                    state.deviceData = {
                      ...state.deviceData,
                      isColorMode: mode,
                    };
                    state.manualFlag = Symbol()
                  }}
                  reserveSV={true}
                  h={deviceData.h}
                  s={deviceData.s}
                  v={deviceData.v}
                  brightness={deviceData.brightness}
                  colorTemp={deviceData.temperature}
                  onHSVChangeComplete={(h, s, v) => {
                    state.deviceData = {
                      ...state.deviceData,
                      h,
                      s,
                      v,
                    };
                    state.manualFlag = Symbol()
                  }}
                  onBrightnessChangeComplete={bright => {
                    state.deviceData = {
                      ...state.deviceData,
                      brightness: bright
                    }
                    state.manualFlag = Symbol()
                  }}
                  onCCTChangeComplete={cct => {
                    state.deviceData = {
                      ...state.deviceData,
                      temperature: cct
                    }
                    state.manualFlag = Symbol()
                  }}
                />
              )}

              {item.enable && item.type === 'fan' && (
                <FanAdjustViewContent
                  theme={props.theme}
                  hideEnable={true}
                  fanEnable={item.enable}
                  maxFanSpeed={props.isUVCFan ? 20 : 3}
                  isSupportDirection={props.isUVCFan}
                  isSupportDisinfect={props.isUVCFan}
                  isSupportMode={props.isUVCFan}
                  directValue={(state.deviceData as FanLightData).direction}
                  disinfect={(state.deviceData as FanLightData).disinfect}
                  modeValue={(state.deviceData as FanLightData).mode}
                  directOptions={directOptions}
                  modeOptions={modeOptions}
                  directChange={(direction: any) => {
                    state.deviceData = {
                      ...state.deviceData,
                      direction
                    }
                    state.manualFlag = Symbol()
                  }}
                  modeChange={(mode: any) => {
                    state.deviceData = {
                      ...state.deviceData,
                      mode
                    }
                    state.manualFlag = Symbol()
                  }}
                  disinfectChange={(disinfect: any) => {
                    state.deviceData = {
                      ...state.deviceData,
                      disinfect
                    }
                    state.manualFlag = Symbol()
                  }}
                  // @ts-ignore
                  fanSpeed={deviceData.fanSpeed}
                  onFanSwitch={() => { }}
                  onFanSpeedChangeComplete={(fanSpeed) => {
                    state.deviceData = {
                      ...state.deviceData,
                      fanSpeed
                    }
                    state.manualFlag = Symbol()
                  }}
                />
              )}
            </Card>
            <Spacer />
          </View>
        ))}
      </View>
    );
  }, [state.deviceData, state.applyForList, props.theme?.type]);

  return <View>{lightSourceCard}</View>;
}

export default memo(withTheme(ManualSettings))
