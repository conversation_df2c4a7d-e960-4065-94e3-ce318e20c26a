import React, { useMemo } from "react";
import { ViewStyle, View, Text, StyleSheet } from "react-native";
import Card from "@ledvance/base/src/components/Card";
import { SwitchButton, Utils } from 'tuya-panel-kit';
import { convertTo12HourFormat, loopText } from '@ledvance/base/src/utils/common';
import { ApplyForItem, Timer } from "../Interface";
import { useSystemTimeFormate } from "@ledvance/base/src/models/modules/NativePropsSlice";
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface ScheduleCardProps {
  theme?: ThemeType
  item: Timer
  style?: ViewStyle
  showTag?: boolean
  tags: ApplyForItem[]
  onEnableChange: (enable: boolean) => void
  onPress: (item: any) => void
}

const ScheduleCard = (props: ScheduleCardProps) => {
  const { item, style, showTag, tags, onEnableChange, onPress } = props;
  const is24HourClock = useSystemTimeFormate()
  const showTags = useMemo(() => {
    return tags.filter(tag => item.dps.hasOwnProperty(tag.dp))
  }, [tags, item.dps])

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      borderRadius: cx(8),
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    infoContainer: {
      flex: 1,
      marginTop: cx(16),
      marginBottom: cx(16),
      flexDirection: 'column',
      marginLeft: cx(16),
    },
    time: {
      color: props.theme?.global.fontColor,
      marginBottom: cx(5),
      fontSize: 16,
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    loop: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginTop: cx(8),
    },
    name: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginTop: cx(8),
    },
    switchContainer: {
      marginRight: cx(16),
      // backgroundColor: 'red',
      marginTop: cx(16),
    },
    switch: {},
    typeContainer: {
      flexDirection: 'row',
      marginTop: cx(8),
    },
    tag: {
      borderRadius: cx(16),
      height: cx(16),
      backgroundColor: '#E6E7E8',
      marginRight: cx(10),
      paddingHorizontal: cx(12)
    },
    tagTitle: {
      fontSize: cx(10),
      textAlign: 'center',
      fontFamily: 'PingFangSC-Medium',
      color: '#000'
    },
  });
  return (
    <Card
      style={styles.card}
      containerStyle={[styles.container, style]}
      onPress={() => {
        onPress(item);
      }}
    >
      <View style={styles.infoContainer}>
        <Text style={styles.time}>{is24HourClock ? item.time : convertTo12HourFormat(item.time)}</Text>
        <Text style={styles.loop}>
          {loopText(item.loops.split('').map(loop => parseInt(loop)), item.time)}
        </Text>
        <Text style={styles.name}>{item.name}</Text>
        {showTag && <View style={styles.typeContainer}>
          {showTags.map(tag => (
            <View style={styles.tag} key={tag.dp}>
              <Text style={styles.tagTitle}>
                {tag.key}
              </Text>
            </View>
          ))}
        </View>}
      </View>
      <View style={styles.switchContainer}>
        <SwitchButton
          value={item.enable}
          thumbStyle={{ elevation: 0 }}
          onValueChange={() => {
            onEnableChange(!item.enable);
          }}
        />
      </View>
    </Card>
  )

}

export default withTheme(ScheduleCard)
