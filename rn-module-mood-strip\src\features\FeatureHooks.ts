import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from '@ledvance/base/src/components/AdvanceCard'
import {useDeviceId, useDp, useTimeSchedule} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {getGlobalParamsDp, isSupportFunctions} from '@ledvance/base/src/utils/common'
import {AdjustType, ApplyForItem, DiySceneInfo, HSV, WorkMode} from "@ledvance/base/src/utils/interface";
import {getHexByHSV, getHSVByHex} from "@ledvance/base/src/utils";
import {Result} from '@ledvance/base/src/models/modules/Result';
import I18n from "@ledvance/base/src/i18n";
import {createParams} from "@ledvance/base/src/hooks/Hooks";
import {timeFormat} from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'
import {TimeSchedulePageParams} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage'
import {useReactive, useUpdateEffect} from "ahooks";
import {useCallback, useEffect} from 'react';
import {
  DeviceStateType,
  DeviceType,
  MoodStripData
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import {ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers";
import {
  decodeDiyScene,
  decodeScene,
  encodeDiyScene,
  encodeScene
} from '@ledvance/ui-biz-bundle/src/newModules/diyScene/DiySceneActions';
import {NativeApi} from "@ledvance/base/src/api/native";
import {Buffer} from "buffer";


export function useSwitchLed() {
  return useDp<boolean, any>(getGlobalParamsDp('switch_led'));
}

export function useWorkMode() {
  return useDp<WorkMode, any>(getGlobalParamsDp('work_mode'))
}

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(getGlobalParamsDp('bright_value'))
  const setBrightFn = (v: number) => {
    return setBright(v * 10)
  }
  return [Math.round(bright / 10), setBrightFn]
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [temp, setTemp]: [number, (v: number) => Promise<Result<any>>] = useDp(getGlobalParamsDp('temp_value'))
  const setTempFn = (v: number) => {
    return setTemp(v * 10)
  }
  return [Math.round(temp / 10), setTempFn]
}

export function usePaintColour(): [AdjustType, HSV, HSV, (adjustType: AdjustType, topColor: HSV, bottomColor: HSV) => Promise<Result<any>>] {
  const [dpValue, setDpValue] = useDp<string, any>(getGlobalParamsDp('paint_colour_data'))
  const setPaintColorFn = (adjustType: AdjustType, topColor: HSV, bottomColor: HSV) => {
    const topHex = getHexByHSV(topColor)
    const bottomHex = getHexByHSV(bottomColor)
    return setDpValue(`${adjustType}${topHex}${bottomHex}`);
  };
  const newDpValue = dpValue ?? '01000103e803e8000103e803e8'
  const {adjustType, topColor, bottomColor} = parsePaintColor(newDpValue);
  return [adjustType, topColor, bottomColor, setPaintColorFn]
}

function parsePaintColor(dpValue: string) {
  const adjustType = dpValue.substring(0, 2) as AdjustType
  const topDp = dpValue.substring(2, 14)
  const bottomDp = dpValue.substring(14, 26)
  const topColor: HSV = getHSVByHex(topDp)
  const bottomColor: HSV = getHSVByHex(bottomDp)
  return {adjustType, topColor, bottomColor}
}

export function useCountdown() {
  return useDp<number, any>(getGlobalParamsDp('countdown'))
}

export function useAdvanceData(): AdvancedData[] {
  const deviceId = useDeviceId()
  const [switchLed] = useSwitchLed()
  const [workMode] = useWorkMode()
  const [countdown] = useCountdown()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const advanceData = [] as AdvancedData[]
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.MoodStrip,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: true,
          adjustType: AdjustType.COLOUR
        },
      },
      isManual: !(dps.hasOwnProperty(getGlobalParamsDp('scene_data')) || dps.hasOwnProperty(getGlobalParamsDp('diy_scene_data'))),
      mood: undefined,
    };
    if (dps.hasOwnProperty(getGlobalParamsDp('paint_colour_data'))){
      const dpValue = Buffer.from(dps[getGlobalParamsDp('paint_colour_data')], 'base64').toString('hex')
      const { adjustType, topColor }: any = parsePaintColor(dpValue)
      const isColorMode = dps[getGlobalParamsDp('work_mode')] === WorkMode.Colour
      deviceState.deviceData.deviceData = {
        ...deviceState.deviceData.deviceData,
        adjustType: adjustType as AdjustType,
        h: isColorMode ? Math.trunc(topColor.h) : 0,
        s: isColorMode ? Math.trunc(topColor.s / 10) : 100,
        v: isColorMode ? Math.trunc(topColor.v / 10) : 100,
        brightness: isColorMode ? 100 : Math.trunc(dps[getGlobalParamsDp('bright_value')] / 10),
        temperature: isColorMode ? 0 : Math.trunc(dps[getGlobalParamsDp('temp_value')] / 10),
        isColorMode
      }
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('scene_data'))){
      deviceState.mood = decodeScene(dps[getGlobalParamsDp('scene_data')])
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('diy_scene_data'))) {
      deviceState.mood = decodeDiyScene(Buffer.from(dps[getGlobalParamsDp('diy_scene_data')], 'base64').toString('hex'))
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      const diyScene = mood as DiySceneInfo
      if (!isManual && diyScene) {
        if (diyScene.type === 'DIY') {
          manualDps[getGlobalParamsDp('diy_scene_data')] = Buffer.from(encodeDiyScene(diyScene), 'hex').toString('base64')
        } else {
          manualDps[getGlobalParamsDp('scene_data')] = encodeScene(diyScene)
        }
        manualDps[getGlobalParamsDp('switch_led')] = true
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
      } else {
        const device = deviceData.deviceData as MoodStripData
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
        });
        if (manualDps[getGlobalParamsDp('switch_led')]) {
          const topHex = getHexByHSV({h: device.h, s: device.s * 10, v: device.v * 10})
          const bottomHex = getHexByHSV({h: device.h, s: device.s * 10, v: device.v * 10})
          manualDps[getGlobalParamsDp('paint_colour_data')] = Buffer.from(`${device.adjustType}${topHex}${bottomHex}`, 'hex').toString('base64')
          if (device.adjustType === AdjustType.WHITE) {
            manualDps[getGlobalParamsDp('temp_value')] = device.temperature * 10
            manualDps[getGlobalParamsDp('bright_value')] = device.brightness * 10
          }
          manualDps[getGlobalParamsDp('work_mode')] = device.isColorMode ? WorkMode.Colour : WorkMode.White
        }
      }
      return manualDps;
    },
    []
  );

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    router: {
      key: ui_biz_routerKey.ui_biz_time_schedule_new,
      params: {
        applyForList: [{
          type: 'light',
          name: I18n.getLang('Onoff_button_socket'),
          key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
          dp: getGlobalParamsDp('switch_led'),
          enable: true,
        }],
        isSupportBrightness: true,
        isSupportColor: true,
        isSupportTemperature: true,
        isSupportMood: true,
        isMoodStrip: true,
        featureId: 'MixSceneList',
        manualDataDp2Obj,
        manualDataObj2Dp
      } as TimeSchedulePageParams
    }
  })

  if (isSupportFunctions('diy_scene_data', 'scene_data')) {
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(switchLed && workMode === WorkMode.Scene ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: {key: 'diy_scene_data', code: getGlobalParamsDp('diy_scene_data')},
      router: {
        key: ui_biz_routerKey.ui_biz_diy_scene_page,
        params: {},
      },
    })
  }

  if (isSupportFunctions('music_data')) {
    const params = createParams({
      switch_led: getGlobalParamsDp('switch_led'),
      work_mode: getGlobalParamsDp('work_mode'),
      music_data: getGlobalParamsDp('music_data'),
      mix_rgbcw: undefined,
      mix_light_scene: undefined,
      isMixRGBWLamp: false,
    });

    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(
        workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: {code: getGlobalParamsDp('music_data'), key: 'music_data'},
      router: {
        key: ui_biz_routerKey.ui_biz_music,
        params,
      },
    });
  }

  if (isSupportFunctions('countdown')) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { code: getGlobalParamsDp('countdown'), key: 'countdown'},
      router: {
        key: ui_biz_routerKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: getGlobalParamsDp('countdown'),
              enableDp: getGlobalParamsDp('switch_led'),
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }

  return advanceData;
}
