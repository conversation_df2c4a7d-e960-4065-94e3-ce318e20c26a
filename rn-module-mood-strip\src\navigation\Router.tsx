import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from "@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router"
import MusicPageRouters from "@ledvance/ui-biz-bundle/src/modules/music/Router"
import DiyScenePageRouters from "@ledvance/ui-biz-bundle/src/newModules/diyScene/Router"
import SelectPageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'

export const RouterKey = {
    main: 'main',
    ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
  ...TimeSchedulePageRouters,
  ...DiyScenePageRouters,
  ...TimerPageRouters,
  ...MusicPageRouters,
  ...SelectPageRouters
]
