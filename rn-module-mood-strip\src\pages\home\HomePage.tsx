import React, {useEffect, useMemo} from 'react'
import Page from '@ledvance/base/src/components/Page'
import {useDeviceId, useDeviceInfo, useFamilyName, useMoods} from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import {Utils} from 'tuya-panel-kit'
import Card from "@ledvance/base/src/components/Card";
import {ScrollView, View} from "react-native";
import {NativeApi} from "@ledvance/base/src/api/native";
import {useCreation, useReactive, useThrottleFn, useUpdateEffect} from "ahooks";
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import I18n from "@ledvance/base/src/i18n";
import BallDirectionView, {PaintMode} from "@ledvance/base/src/components/BallDirectionView";
import {
  useAdvanceData,
  useBrightness,
  usePaintColour,
  useSwitchLed,
  useTemperature,
  useWorkMode
} from "../../features/FeatureHooks";
import {AdjustType, DiySceneInfo, HSV, SceneStatusType, WorkMode} from "@ledvance/base/src/utils/interface";
import MoodStripAdjustView from "@ledvance/base/src/components/MoodStripAdjustView";
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import {
  getMixSceneList,
  useLoveScenes,
  useSceneData,
  useSceneStatus
} from '@ledvance/ui-biz-bundle/src/newModules/diyScene/DiySceneActions'
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'

const cx = Utils.RatioUtils.convertX
const {withTheme} = Utils.ThemeUtils

const HomePage = () => {
  const devInfo = useDeviceInfo()
  const devId = useDeviceId();
  const familyName = useFamilyName()
  const [switchLed, setSwitchLed] = useSwitchLed()
  const [workMode, setWorkMode] = useWorkMode()
  const [tempValue, setTempValue] = useTemperature()
  const [brightValue, setBrightValue] = useBrightness()
  const [loveScenes, setLoveScene] = useLoveScenes()
  const [scenes, setScenes] = useMoods()
  const [setSceneData] = useSceneData()
  const [, setSceneStatus] = useSceneStatus()
  const [adjustType, topColor, bottomColor, setPaintColour] = usePaintColour()
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const advancedData = useAdvanceData();

  const tabList = useCreation(() => {
    return [
      {key: AdjustType.COLOUR, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_color_text')},
      {key: AdjustType.WHITE, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_white_text')},
      {key: AdjustType.LOVE, title: I18n.getLang('mood_strip_mode_favorite')},
    ]
  }, [])

  const state = useReactive({
    flag: Symbol(),
    adjustType: AdjustType.COLOUR,
    paintMode: PaintMode.ALL,
    h: 359,
    s: 100,
    v: 100,
    cct: 100,
    brightness: 100,
    topColor: {h: 359, s: 1000, v: 1000} as HSV,
    bottomColor: {h: 359, s: 1000, v: 1000} as HSV
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  const favoriteScenes = useMemo(() => {
    const res = scenes.filter(scene => loveScenes.some(it => it === scene.id))
    res.unshift({id: -1})
    return res
  }, [JSON.stringify(loveScenes), JSON.stringify(scenes)])

  useEffect(() => {
    state.adjustType = adjustType
    state.topColor = topColor
    state.bottomColor = bottomColor
    state.h = topColor.h
    state.s = Math.round(topColor.s / 10)
    state.v = Math.round((topColor.v || 1000) / 10)
  }, [adjustType, JSON.stringify(topColor), JSON.stringify(bottomColor)]);

  useEffect(() => {
    state.cct = tempValue
    state.brightness = brightValue
  }, [tempValue, brightValue]);

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = state.adjustType === AdjustType.COLOUR
    if (!isFocused || !isColorMode || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    const halfBallColor: HSV = {
      h: gestureHue,
      s: state.s * 10,
      v: state.v * 10
    }
    switch (state.paintMode) {
      case PaintMode.Top:
        state.topColor = halfBallColor
        break
      case PaintMode.Bottom:
        state.bottomColor = halfBallColor
        break
      default:
        state.topColor = halfBallColor
        state.bottomColor = halfBallColor
    }
    run()
  }, [isFocused, state.adjustType, state.paintMode, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = state.adjustType === AdjustType.COLOUR
    if (isColorMode) {
      state.v = gestureBrightness
      const halfBallColor: HSV = {
        h: state.h,
        s: state.s * 10,
        v: gestureBrightness * 10
      }
      switch (state.paintMode) {
        case PaintMode.Top:
          state.topColor = halfBallColor
          break
        case PaintMode.Bottom:
          state.bottomColor = halfBallColor
          break
        default:
          state.topColor = halfBallColor
          state.bottomColor = halfBallColor
      }
    } else {
      state.brightness = gestureBrightness
    }
    run()
  }, [isFocused, state.adjustType, state.paintMode, gestureBrightness])

  const { run } = useThrottleFn(() => state.flag = Symbol(), { wait: 500 })

  useEffect(() => {
    const timerId = setTimeout(() => {
      getMixSceneList(devId).then(res => {
        if (res.success && Array.isArray(res.data)) {
          setScenes(res.data)
        }
      })
    }, 250);

    return () => {
      clearTimeout(timerId);
    };
  }, []);

  useEffect(() => {
    if (workMode !== WorkMode.Music) {
      MusicManager.close().then()
    }
  }, [workMode]);

  useUpdateEffect(() => {
    if (switchLed) {
      setWorkMode(getWorkMode(state.adjustType)).then()
      setPaintColour(state.adjustType, state.topColor, state.bottomColor).then()
      if (state.adjustType === AdjustType.WHITE) {
        setTempValue(state.cct).then()
        setBrightValue(state.brightness).then()
      }
    }
  }, [state.flag]);

  const getWorkMode = (key: AdjustType) => {
    switch (key) {
      case AdjustType.COLOUR:
        return WorkMode.Colour
      case AdjustType.WHITE:
        return WorkMode.White
      case AdjustType.LOVE:
        return WorkMode.LoveScene
    }
  }

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={{marginVertical: cx(24)}}>
          <Card style={{marginHorizontal: cx(24)}}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
              color={''}
              colorAlpha={1}
              enable={switchLed}
              setEnable={async (enable: boolean) => {
                await setSwitchLed(enable);
              }}
            />
            {switchLed &&
                <View style={{flexDirection: 'column', alignItems: 'center', width: '100%'}}>
                    <BallDirectionView
                        adjustType={state.adjustType}
                        paintMode={state.paintMode}
                        onPaintModeChanged={(paintMode: PaintMode) => {
                          state.paintMode = paintMode;
                        }}
                        topColor={state.topColor}
                        bottomColor={state.bottomColor}
                        colorTemp={state.cct}
                        brightness={state.brightness}
                    />
                    <MoodStripAdjustView
                        lampTabs={tabList}
                        activeKey={state.adjustType}
                        onActiveKeyChange={(key => {
                          state.adjustType = key
                          if (key !== AdjustType.COLOUR) {
                            state.paintMode = PaintMode.ALL
                          }
                        })}
                        isSupportTemperature={true}
                        isSupportBrightness={true}
                        h={state.h} s={state.s} v={state.v}
                        onHSVChange={() => { }}
                        onHSVChangeComplete={async (h, s, v) => {
                          state.h = h
                          state.s = s
                          state.v = v
                          const halfBallColor: HSV = {
                            h: h,
                            s: s * 10,
                            v: v * 10
                          }
                          switch (state.paintMode) {
                            case PaintMode.Top:
                              state.topColor = halfBallColor
                              break
                            case PaintMode.Bottom:
                              state.bottomColor = halfBallColor
                              break
                            default:
                              state.topColor = halfBallColor
                              state.bottomColor = halfBallColor
                          }
                          state.flag = Symbol()
                        }}
                        colorTemp={state.cct}
                        brightness={state.brightness}
                        onCCTChange={() => { }}
                        onCCTChangeComplete={(temperature) => {
                          state.cct = temperature
                          state.flag = Symbol()
                        }}
                        onBrightnessChange={() => { }}
                        onBrightnessChangeComplete={brightness => {
                          state.brightness = brightness
                          state.flag = Symbol()
                        }}
                        favoriteScenes={favoriteScenes}
                        onFavoriteSceneChange={async (sceneInfo: DiySceneInfo) => {
                          console.log('favorite scene', sceneInfo)
                          if (sceneInfo.id === -1) {
                            await setWorkMode(WorkMode.LoveScene)
                          } else {
                            await setWorkMode(WorkMode.Scene)
                            await setSceneData(sceneInfo)
                            await setSceneStatus(SceneStatusType.Scene, sceneInfo.id)
                          }
                        }}
                        onFavoriteSceneRemove={async (sceneInfo: DiySceneInfo) => {
                          const newLoveScenes = loveScenes.filter(it => it !== sceneInfo.id)
                          await setLoveScene(newLoveScenes)
                        }}
                    />
                </View>}
          </Card>
        </View>
        <AdvanceList advanceData={advancedData} />
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
