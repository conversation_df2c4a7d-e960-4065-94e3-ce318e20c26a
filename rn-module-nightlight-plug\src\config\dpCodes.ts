import I18n from "@ledvance/base/src/i18n"
export default {
    switch_1: '1',
    add_ele: '3',
    cur_current: '4',
    cur_voltage: '5',
    cur_power: '6',
    test_bit: '7',
    voltage_coe: '8',
    countdown_1: '9',
    power_coe: '10',
    electricity_coe: '11',
    switch_led: '27',
    work_mode: '28',
    bright_value: '29',
    // scene_data: '32',
    countdown: '33',
    control_data: '34',
    relay_status: '38',
    fault: '39',
    electric_coe: '40',
}

export const dpNames = {
    switch_1: 'switch_1',
    add_ele: 'add_ele',
    cur_current: 'cur_current',
    cur_voltage: 'cur_voltage',
    cur_power: 'cur_power',
    test_bit: 'test_bit',
    voltage_coe: 'voltage_coe',
    countdown_1: 'countdown_1',
    power_coe: 'power_coe',
    electricity_coe: 'electricity_coe',
    switch_led: 'switch_led',
    work_mode: 'work_mode',
    bright_value: 'bright_value',
    // scene_data: 'scene_data',
    countdown: 'countdown',
    control_data: 'control_data',
    relay_status: 'relay_status',
    fault: 'fault',
    electric_coe: 'electric_coe',
}

export const dpTypes = {
    '1': I18n.getLang('manual_search_button_socket'),
    '27': I18n.getLang('light_sources_tile_tw_lighting_headline')
}
