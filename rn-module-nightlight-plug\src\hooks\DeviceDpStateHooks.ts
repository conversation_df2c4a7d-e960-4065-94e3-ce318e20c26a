import {useDeviceId, useDp, useTimeSchedule} from '@ledvance/base/src/models/modules/NativePropsSlice';
import {Result} from '@ledvance/base/src/models/modules/Result';
import dpCodes from '../config/dpCodes';
import I18n, {I18nKey} from '@ledvance/base/src/i18n';
import {RouterKey} from '../navigation/Router';
import {isSupportCountdown1} from '../device/Device';
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from "@ledvance/base/src/components/AdvanceCard";
import {cloneDeep} from "lodash";
import {TimeSchedulePageParams} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import {ApplyForItem} from '@ledvance/base/src/utils/interface';
import {useCallback, useEffect, useMemo} from 'react';
import {DeviceStateType, DeviceType} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import {getGlobalParamsDp, isSupportFunctions} from '@ledvance/base/src/utils/common';
import {
  PowerBehaviorPageParams,
  usePowerBehavior
} from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions';
import {useReactive, useUpdateEffect} from 'ahooks';
import {NativeApi} from "@ledvance/base/src/api/native";
import {createParams} from "@ledvance/base/src/hooks/Hooks";
import {timeFormatToRealTime, useCountdowns} from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import { SwitchHistoryPageRouteParams } from '@ledvance/ui-biz-bundle/src/modules/history/HistoryPage';

function useElectricCurrent(): number {
  return useDp<number, any>(dpCodes.cur_current)[0] || 0;
}

function useCurrent(): number {
  return useDp<number, any>(dpCodes.cur_current)[0] || 0;
}

function useVoltage(): number {
  return useDp<number, any>(dpCodes.cur_voltage)[0] / 10 || 0;
}

function usePower(): number {
  return useDp<number, any>(dpCodes.cur_power)[0] / 10 || 0;
}

function useSwitch1(): [boolean, (enable: boolean) => Promise<Result<any>>] {
  return useDp(dpCodes.switch_1);
}

function useSwitchLed(): [boolean, (enable: boolean) => Promise<Result<any>>] {
  return useDp(dpCodes.switch_led);
}

function useBrightValue(): [number, (brightValue: number) => Promise<Result<any>>] {
  return useDp(dpCodes.bright_value);
}

function useCountdown1(): [number, (countdown1: number) => Promise<Result<any>>] {
  return useDp(dpCodes.countdown_1);
}

function useCountdown(): [number, (countdown1: number) => Promise<Result<any>>] {
  return useDp(dpCodes.countdown);
}

function useRelayStatus(): [string, (relayStatus: string) => Promise<Result<any>>] {
  return useDp(dpCodes.relay_status);
}

export function useAdvancedData(): AdvancedData[] {
  const res: AdvancedData[] = []
  const deviceId = useDeviceId();
  const [powerBehaviors] = usePowerBehavior(['relay_status'])
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [switchLed] = useSwitchLed()
  const [switch1] = useSwitch1()
  const state = useReactive({
    timeScheduleStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const plugApplyFor: ApplyForItem[] = [
    {
      type: 'socket',
      key: I18n.getLang('manual_search_button_socket'),
      name: I18n.getLang('Onoff_button_socket'),
      dp: getGlobalParamsDp('switch_1'),
      enable: true,
    },
    {
      type: 'light',
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch_led'),
      enable: true,
    },
  ];

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    console.log('dps', dps)
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: dps[getGlobalParamsDp('bright_value')] || 100,
          temperature: 0,
          isColorMode: false,
        },
      },
      isManual: true,
      mood: undefined,
    };
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      console.log('deviceState', deviceState)
      const manualDps = {};
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
      })
      if (manualDps[getGlobalParamsDp('switch_led')]) {
        manualDps[getGlobalParamsDp('bright_value')] = deviceState.deviceData.deviceData.brightness;
      }
      return manualDps;
    },
    []
  );

  res.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(plugApplyFor),
        applyForDisabled: false,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: true,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    }
  })

  if (isSupportCountdown1()) {
    const params = useMemo(() => createParams({
      dps: [
        {
          label: I18n.getLang('manual_search_button_socket'),
          value: 'socket',
          cloudKey: 'socketInfo',
          dpId: getGlobalParamsDp('countdown_1'),
          enableDp: getGlobalParamsDp('switch_1'),
          stringOn: 'timer_nightplug_active_timer_field_description_on_text',
          stringOff: 'timer_nightplug_active_timer_field_description_off_text',
        },
        {
          label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
          value: 'lighting',
          dpId: getGlobalParamsDp('countdown'),
          enableDp: getGlobalParamsDp('switch_led'),
          cloudKey: 'lightingInfo',
          stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
          stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
        },
      ],
    }), [])

    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() =>{
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let enable = false
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown'):
            enable = switchLed
            break
          case getGlobalParamsDp('countdown_1'):
            enable = switch1
            break
        }
        const L = 'timer_ceiling_fan_lighting_switched_'
        const F = 'timer_nightplug_active_timer_field_description_'
        const isLight = timer.dpId === getGlobalParamsDp('countdown')
        const key: I18nKey = `${isLight ? L : F}${enable ? 'off': 'on'}_text`
        return I18n.formatValue(key, timeFormatToRealTime(timer.countdown[0]))
      })
    }, [switchLed, switch1, JSON.stringify(tasks)])

    res.push({
      title: I18n.getLang('timer_nightplug_headline_text'),
      subtitles: timerTask,
      statusColor:  getAdvancedStatusColor(timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'countdown_1', code: getGlobalParamsDp('countdown_1') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      }
    })
  }


  if (isSupportFunctions('relay_status')) {
    const params = createParams<PowerBehaviorPageParams>({
      powerBehaviorKeys: ['relay_status']
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_relay_status'),
      statusColor: getAdvancedStatusColor(powerBehaviors.some(item => !!item) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'relay_status', code: getGlobalParamsDp('relay_status') },
      router: {
        key: RouterKey.ui_biz_power_behavior_plug,
        params
      },
    })
  }

  const historyParams = createParams<SwitchHistoryPageRouteParams>({
    dpIds: [getGlobalParamsDp('switch_1')],
    getActionsText: (dpData:any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
  })

  res.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return res;
}

export {
  useElectricCurrent,
  useCurrent,
  useVoltage,
  usePower,
  useSwitch1,
  useSwitchLed,
  useBrightValue,
  useCountdown1,
  useCountdown,
  useRelayStatus,
};
