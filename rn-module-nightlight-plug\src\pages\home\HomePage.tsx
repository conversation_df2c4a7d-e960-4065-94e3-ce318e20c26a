import React, { useCallback, useEffect } from 'react'
import {ScrollView, StyleSheet, Text, View} from 'react-native'
import res from '@ledvance/base/src/res'
import {
  useDeviceId,
  useDeviceInfo,
  useFamilyName
} from '@ledvance/base/src/models/modules/NativePropsSlice'
import Page from '@ledvance/base/src/components/Page'
import {NativeApi, queryDpIds} from '@ledvance/base/src/api/native'
import Card from '@ledvance/base/src/components/Card'
import {Utils} from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import {getGlobalParamsDp} from '@ledvance/base/src/utils/common'
import {
  useElectricCurrent,
  usePower,
  useSwitch1,
  useVoltage,
  useAdvancedData,
  useSwitchLed,
  useBrightValue
} from '../../hooks/DeviceDpStateHooks'
import {useReactive, useInterval} from 'ahooks'
import {useNavigation, useIsFocused} from '@react-navigation/core'
import {ui_biz_routerKey} from "@ledvance/ui-biz-bundle/src/navigation/Routers";
import {
  EnergyConsumptionPageProps
} from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionPage'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import ThemeType from '@ledvance/base/src/config/themeType'
import { getEnergyGenerationValue } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionActions';

const {convertX: cx} = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

const HomePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo()
  const devId = useDeviceId()
  const familyName = useFamilyName()
  const navigation = useNavigation()
  const isFocused = useIsFocused()
  const [switch1, setSwitch1] = useSwitch1()
  const [switchLed, setSwitchLed] = useSwitchLed()
  const [brightValue, setBrightValue] = useBrightValue()
  const power = usePower()
  const electricCurrent = useElectricCurrent()
  const voltage = useVoltage()
  const advanceData = useAdvancedData()
  const state = useReactive({
    loading: false,
    isGeneration: false,
    brightValue: 10,
    advancedDataList: []
  })

  useInterval(() => {
      if (isFocused) {
        const jsonData = JSON.stringify([getGlobalParamsDp('cur_current'), getGlobalParamsDp('cur_power'), getGlobalParamsDp('cur_voltage')])
        queryDpIds(jsonData, devId).then()
      }
    },
    3000,
    {immediate: true}
  )

  useEffect(() => {
    getEnergyGenerationValue(devId).then(data => {
      state.isGeneration = !!data?.generationMode
    })
  }, [isFocused])

  useEffect(() => {
    state.brightValue = Math.round((brightValue || 10) / 10)
  }, [brightValue])

  const setBrightStateValue = useCallback((brightness: number) => {
    state.brightValue = brightness
  }, [])

  const setRemoteBrightValue = useCallback(async (brightness: number) => {
    setBrightStateValue(brightness)
    await setBrightValue(state.brightValue * 10)
  }, [])

  const unitDivision = (str) => {
    if (!str) {
      return ['', '']
    }
    const strIndex = str.indexOf('(') || str.indexOf('（')
    const unit = str.substring(strIndex)
    const name = str.split(unit)[0]
    return [name, unit]
  }

  const styles = StyleSheet.create({
    consumedEnergyCard: {
      marginHorizontal: cx(24),
    },
    consumedEnergyCardTitle: {
      marginHorizontal: cx(16),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    consumedEnergyContent: {
      flexDirection: 'row',
    },
    subContent: {
      flex: 1,
      alignItems: 'center',
      marginBottom: cx(9)
    },
    valueText: {
      fontSize: cx(24),
      fontWeight: 'bold',
      color: props.theme?.global.secondBrand,
    },
    titleText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme?.global.secondFontColor,
      textAlign: 'center',
    },
    lightCard: {
      marginHorizontal: cx(24),
    },
  })

  const ConsumedEnergyItem = (props: { value: number, unit: string }) => {
    return (
      <View style={styles.subContent}>
        <Text style={styles.valueText}>{(props.value) || 0}</Text>
        <Spacer height={cx(4)}/>
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[0]}
        </Text>
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[1]}
        </Text>
      </View>
    )
  }

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={deviceInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(deviceInfo.devId)
      }}>
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer/>
          <Card
            style={styles.consumedEnergyCard}
            onPress={() => navigation.navigate(ui_biz_routerKey.ui_biz_energy_consumption, {
              electricDpCode: getGlobalParamsDp('cur_current'),
              powerDpCode: getGlobalParamsDp('cur_power'),
              voltageDpCode: getGlobalParamsDp('cur_voltage'),
              addEleDpCode: getGlobalParamsDp('add_ele'),
            } as EnergyConsumptionPageProps)}>
            <Spacer height={cx(16)}/>
            <Text
              style={styles.consumedEnergyCardTitle}>{I18n.getLang(state.isGeneration ? 'sockets_headline_power' : 'sockets_ce')}</Text>
            <Spacer height={cx(18)}/>
            <View style={styles.consumedEnergyContent}>
              <ConsumedEnergyItem
                value={power}
                unit={I18n.getLang('consumption_data_field2_value_text1')}/>
              <ConsumedEnergyItem
                value={electricCurrent}
                unit={I18n.getLang('consumption_data_field2_value_text2')}/>
              <ConsumedEnergyItem
                value={voltage}
                unit={I18n.getLang('consumption_data_field2_value_text3')}/>
            </View>
            <Spacer height={cx(17)}/>
          </Card>
          <Spacer/>
          <Card style={{marginHorizontal: cx(24)}}>
            <LdvSwitch
              title={I18n.getLang('Onoff_button_socket')}
              color={props.theme?.card.background}
              colorAlpha={1}
              enable={switch1}
              setEnable={async (enable: boolean) => {
                state.loading = true
                await setSwitch1(enable)
                state.loading = false
              }}/>
          </Card>
          <Spacer/>
          <Card style={styles.lightCard}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
              color={'#F9D0A2'}
              colorAlpha={1}
              enable={switchLed}
              setEnable={setSwitchLed}/>
            {switchLed && <>
                <ColorTempAdjustView
                    colorTemp={0}
                    brightness={state.brightValue}
                    isSupportTemperature={false}
                    isSupportBrightness={true}
                    onCCTChangeComplete={_ => {
                    }}
                    onBrightnessChange={setBrightStateValue}
                    onBrightnessChangeComplete={setRemoteBrightValue}/>
                <Spacer height={cx(16)}/>
            </>}

          </Card>
          <Spacer height={cx(19)}/>
          <AdvanceList advanceData={advanceData}/>
          <Spacer height={cx(40)}/>
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
