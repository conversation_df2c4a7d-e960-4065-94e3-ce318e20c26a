import { LightBehaviorPageParams } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/LightBehaviorPage';
import { usePowerOffMemory } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions';
import { OsramFanLightData } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard';
import {
  useDeviceId,
  useDp,
  useTimeSchedule,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { ApplyForItem, WorkMode } from '@ledvance/base/src/utils/interface';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { getGlobalParamsDp, isSupportFunctions } from '@ledvance/base/src/utils/common';
import { useReactive, useUpdateEffect } from 'ahooks';
import { NativeApi } from '@ledvance/base/src/api/native';
import I18n, { I18nKey } from '@ledvance/base/src/i18n';
import {
  DeviceStateType,
  DeviceType,
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { RouterKey } from '../navigation/Router';
import { createParams } from '@ledvance/base/src/hooks/Hooks';
import { timeFormat, useCountdowns } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import { dpItem } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPage';

export function useSwitchLed(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_led'));
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  return useDp<WorkMode, any>(getGlobalParamsDp('work_mode'));
}

export function useBrightValue(): [number, (value: number) => Promise<Result<any>>] {
  const [dpValue, setDpValue] = useDp<number, any>(getGlobalParamsDp('bright_value'));
  const [brightValue, setBrightValue] = useState<number>(1);

  useEffect(() => {
    setBrightValue(Math.round(dpValue / 10));
  }, [dpValue]);

  const setBrightValueFun = useCallback((value: number) => {
    setBrightValue(value);
    return setDpValue(value * 10);
  }, []);

  return [brightValue, setBrightValueFun];
}

export function useTempValue(): [number, (value: number) => Promise<Result<any>>] {
  const [dpValue, setDpValue] = useDp<number, any>(getGlobalParamsDp('temp_value'));
  const [tempValue, setTempValue] = useState<number>(0);

  useEffect(() => {
    setTempValue(Math.round(dpValue / 10));
  }, [dpValue]);

  const setTempValueFun = useCallback((value: number) => {
    setTempValue(value);
    return setDpValue(value * 10);
  }, []);

  return [tempValue, setTempValueFun];
}

export function useFanSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('fan_switch'));
}

export function useFanSpeed(): [number, (value: number) => Promise<Result<any>>] {
  return useDp<number, any>(getGlobalParamsDp('fan_speed'));
}

export enum FanMode {
  Fresh = 'fresh',
  Nature = 'nature',
}

export function useFanMode(): [FanMode, (mode: FanMode) => Promise<Result<any>>] {
  return useDp<FanMode, any>(getGlobalParamsDp('fan_mode'));
}

export function useAdvanceData(): AdvancedData[] {
  const advanceData = [] as AdvancedData[];
  const deviceId = useDeviceId();
  const [switchLed] = useSwitchLed();
  const [fanSwitch] = useFanSwitch();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [powerMemory] = usePowerOffMemory(getGlobalParamsDp('power_memory'));
  const state = useReactive({
    rhythmModeStatus: AdvancedStatus.Disable,
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable,
  });
  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

    const fanLightApplyFor: ApplyForItem[] = useMemo(
    () => [
      {
        type: 'light',
        name: I18n.getLang('Onoff_button_socket'),
        key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
        dp: getGlobalParamsDp('switch_led'),
        enable: true,
      },
      {
        type: 'osramFan',
        key: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
        dp: getGlobalParamsDp('fan_switch'),
        enable: true,
      },
    ],
    []
  );

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      deviceData: {
        type: DeviceType.OsramFanLight,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
          fanSpeed: 1,
          fanMode: 'fresh',
        } as OsramFanLightData
      },
      isManual: !(dps.hasOwnProperty(getGlobalParamsDp('scene_data'))),
      mood: undefined
    }
    const deviceData = deviceState.deviceData.deviceData as OsramFanLightData
    if (dps.hasOwnProperty(getGlobalParamsDp('bright_value'))) {
      deviceData.brightness = Math.round(dps[getGlobalParamsDp('bright_value')] / 10);
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('temp_value'))) {
      deviceData.temperature = Math.round(dps[getGlobalParamsDp('temp_value')] / 10);
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('fan_speed'))) {
      deviceData.fanSpeed = dps[getGlobalParamsDp('fan_speed')];
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('fan_mode'))) {
      deviceData.fanMode = dps[getGlobalParamsDp('fan_mode')];
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback((deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
    const { deviceData } = deviceState;
    const manualDps = {};
    const device = deviceData.deviceData as OsramFanLightData
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
        if (apply.enable){
          if (apply.type === 'light'){
            manualDps[getGlobalParamsDp('bright_value')] = device.brightness * 10
            manualDps[getGlobalParamsDp('temp_value')] = device.temperature * 10
          }
          if (apply.type === 'osramFan'){
            manualDps[getGlobalParamsDp('fan_speed')] = device.fanSpeed
            manualDps[getGlobalParamsDp('fan_mode')] = device.fanMode
          }
        }
      });
    return manualDps;
  }, []);

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: fanLightApplyFor,
        isSupportMood: false,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: true,
        isSupportTemperature: true,
        isOsramFanLight: true
      } as TimeSchedulePageParams,
    },
  })

  if (isSupportFunctions('countdown', 'fan_countdown_left')) {
    const params = createParams({
      dps: [
        {
          label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
          value: 'lighting',
          dpId: getGlobalParamsDp('countdown'),
          enableDp: getGlobalParamsDp('switch_led'),
          cloudKey: 'lightingInfo',
          stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
          stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
        },
        {
          label: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
          value: 'fan',
          dpId: getGlobalParamsDp('fan_countdown_left'),
          enableDp: getGlobalParamsDp('fan_switch'),
          cloudKey: 'fanInfo',
          stringOn: 'timer_ceiling_fan_switched_on_text',
          stringOff: 'timer_ceiling_fan_switched_off_text',
          dpValueToDisplay: (value: number) => value * 60,
          displayToDpValue: (value: number) => Math.ceil(value / 60),
        } as dpItem,
      ],
    })
    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() =>{
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let enable = false
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown'):
            enable = switchLed
            break
          case getGlobalParamsDp('fan_countdown_left'):
            enable = fanSwitch
            break
        }
        const L = 'ceiling_fan_feature_2_light_text_min_'
        const F = 'ceiling_fan_feature_2_fan_text_min_'
        const isLight = timer.dpId === getGlobalParamsDp('countdown')
        const key: I18nKey = `${isLight ? L : F}${enable ? 'off': 'on'}`
        return I18n.formatValue(key, timeFormat(timer.countdown[0], true))
      })
    }, [switchLed, fanSwitch, JSON.stringify(tasks)])
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'countdown', code: getGlobalParamsDp('countdown') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  if (isSupportFunctions('power_memory')) {
      const params = createParams<LightBehaviorPageParams>({
        memoryDpCode: getGlobalParamsDp('power_memory'),
        disturbDpCode: getGlobalParamsDp('do_not_disturb'),
        isSupportColor: false,
        isSupportBrightness: true,
        isSupportTemperature: isSupportFunctions('temp_value'),
        isSupportDoNotDisturb: isSupportFunctions('do_not_disturb'),
      });
  
      advanceData.push({
        title: I18n.getLang('light_sources_specific_settings_power_off'),
        statusColor: getAdvancedStatusColor(
          powerMemory ? AdvancedStatus.Enable : AdvancedStatus.Disable
        ),
        dp: { key: 'power_memory', code: getGlobalParamsDp('power_memory')},
        router: {
          key: RouterKey.ui_biz_power_behavior,
          params,
        },
      });
    }

  return advanceData;
}
