import { useDeviceInfo, useDp, useFamilyName, useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, { useEffect, useMemo } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Utils } from 'tuya-panel-kit'
import { NativeApi, sendAppEvent } from '@ledvance/base/src/api/native'
import { View, ScrollView, StyleSheet } from "react-native"
import Card from '@ledvance/base/src/components/Card'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import { FanMode, useAdvanceData, useBrightValue, useFanMode, useFanSpeed, useFanSwitch, useSwitchLed, useTempValue } from 'features/FeatureHooks'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import { useDebounceFn, useReactive, useThrottleFn } from 'ahooks'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import { useIsFocused } from '@react-navigation/core'
import OsramFanAdjustView from '@ledvance/base/src/components/OsramFanAdjustView'
import { Formatter } from '@tuya/tuya-panel-lamp-sdk'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { useControlData } from '@ledvance/base/src/hooks/Hooks'

const { ControlDataFormatter } = Formatter
const control = new ControlDataFormatter()

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const HomePage = (_props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const [switchLed, setSwitchLed] = useSwitchLed()
  const [brightValue, setBrightness] = useBrightValue()
  const [tempValue, setTemperature] = useTempValue()
  const [fanSwitch, setFanSwitch] = useFanSwitch()
  const [fanSpeed, setFanSpeed] = useFanSpeed()
  const [fanMode, setFanMode] = useFanMode()
  const advanceData = useAdvanceData()
  const [, setControlDataDp] = useDp(getGlobalParamsDp('control_data'))
  const sendControlData = useControlData(control, setControlDataDp)
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureBrightness] = useGestureControl('brightness')

  const state = useReactive({
    switchLed: switchLed,
    temperature: tempValue,
    brightness: brightValue,
    fanSwitch: fanSwitch,
    fanSpeed: fanSpeed,
    fanMode: fanMode
  })

  const blockColor = useMemo(() => {
    return cctToColor(state.temperature)
  }, [state.temperature])

  const { run: putControlData } = useThrottleFn(() => {
    sendControlData({
      colorMode: false,
      hue: 0,
      saturation: 0,
      value: 0,
      brightness: state.brightness,
      temperature: state.temperature,
    }).then()
  }, { wait: 500 })

  const { run: putBrightnessData } = useDebounceFn(() => {
    setBrightness(state.brightness)
  }, { wait: 600 })

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useEffect(() => {
    state.switchLed
  }, [switchLed])

  useEffect(() => {
    state.temperature = tempValue
    state.brightness = brightValue
  }, [tempValue, brightValue])

  useEffect(() => {
    state.fanSwitch = fanSwitch
  }, [fanSwitch])

  useEffect(() => {
    state.fanSpeed = fanSpeed
    state.fanMode = fanMode
  }, [fanSpeed, fanMode])

  useEffect(() => {
      if (isFocused && gestureSwitch !== undefined) {
        state.switchLed = gestureSwitch
        setSwitchLed(gestureSwitch).then()
      }
    }, [isFocused, gestureSwitch])
  
    useEffect(() => {
      if (!isFocused || gestureBrightness === undefined || !state.switchLed) {
        return
      }
      state.brightness = gestureBrightness
      putControlData()
      putBrightnessData()
    }, [isFocused, gestureBrightness])

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24)
    }
  })

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          <Card style={styles.card}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
              color={blockColor}
              colorAlpha={1}
              enable={state.switchLed}
              setEnable={async (v: boolean) => {
                state.switchLed = v
                await setSwitchLed(v)
              }}
            />
            {
              state.switchLed && (
                <>
                  <ColorTempAdjustView
                    colorTemp={state.temperature}
                    brightness={state.brightness}
                    isSupportTemperature={true}
                    isSupportBrightness={true}
                    onCCTChange={async (v: number) => {
                      state.temperature = v
                      putControlData()
                    }}
                    onCCTChangeComplete={async (v: number) => {
                      state.temperature = v
                      await setTemperature(v)
                    }}
                    onBrightnessChange={async (v: number) => {
                      state.brightness = v
                      putControlData()
                    }}
                    onBrightnessChangeComplete={async (v: number) => {
                      state.brightness = v
                      await setBrightness(v)
                    }}
                  />
                  <Spacer />
                </>
              )
            }
          </Card>
          <Spacer />
          <Card style={styles.card}>
            <OsramFanAdjustView 
              fanSwitch={state.fanSwitch}
              setFanSwitch={async (v: boolean) => {
                state.fanSwitch = v
                await setFanSwitch(v)
              }}
              fanSpeed={state.fanSpeed}
              setFanSpeed={async (v: number) => {
                state.fanSpeed = v
                await setFanSpeed(v)
              }}
              fanMode={state.fanMode}
              setFanMode={async (v: FanMode) => {
                state.fanMode = v
                await setFanMode(v)
              }}
            />
          </Card>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
