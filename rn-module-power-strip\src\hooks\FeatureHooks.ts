import { SwitchInchingPageParams, useSwitchInching } from '@ledvance/ui-biz-bundle/src/newModules/swithInching/SwithInchingAction';
import { ChildLockPageParams, useChildLock } from '@ledvance/ui-biz-bundle/src/newModules/childLock/ChildLockPage';
import {useDeviceId, useDp, useTimeSchedule,} from '@ledvance/base/src/models/modules/NativePropsSlice';
import {NativeApi} from '@ledvance/base/src/api/native';
import {Result} from '@ledvance/base/src/models/modules/Result';
import {useReactive, useUpdateEffect} from 'ahooks';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {SupportUtils} from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import I18n, {I18nKey} from '@ledvance/base/src/i18n/index';
import {RouterKey} from '../navigation/Router';
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor,} from '@ledvance/base/src/components/AdvanceCard';
import {createParams} from '@ledvance/base/src/hooks/Hooks';
import {
  ApplyForItem,
  DeviceStateType,
  DeviceType,
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import {getGlobalParamsDp, isSupportFunctions, localeNumber} from '@ledvance/base/src/utils/common';
import {RandomTimePageParams} from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage';
import {FixedTimePageParams} from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimePage';
import {
  PowerBehaviorPageParams,
  usePowerBehavior
} from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions';
import {useRandomTime} from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions';
import {useFixedTime} from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimeActions';
import {TimeSchedulePageParams} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import {cloneDeep} from 'lodash';
import {timeFormatToRealTime, useCountdowns} from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import {SwitchHistoryPageRouteParams} from '@ledvance/ui-biz-bundle/src/modules/history/HistoryPage';


export const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  temp_value: { key: 'temp_value', code: '23' },
  colour_data: { key: 'colour_data', code: '24' },
  scene_data: { key: 'scene_data', code: '25' },
  countdown: { key: 'countdown', code: '26' },
  music_data: { key: 'music_data', code: '27' },
  control_data: { key: 'control_data', code: '28' },
  rhythm_mode: { key: 'rhythm_mode', code: '30' },
  sleep_mode: { key: 'sleep_mode', code: '31' },
  wakeup_mode: { key: 'wakeup_mode', code: '32' },
  power_memory: { key: 'power_memory', code: '33' },
  do_not_disturb: { key: 'do_not_disturb', code: '34' },
  mix_light_scene: { key: 'mix_light_scene', code: '36' },
  remote_switch: { key: 'remote_switch', code: '41' },
  mix_rgbcw: { key: 'mix_rgbcw', code: '51' },
  cycle_timing: { key: 'cycle_timing', code: '209' },
  random_timing: { key: 'random_timing', code: '210' },
};

export function useSwitch1(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_1'));
}

export function useSwitch2(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_2'));
}

export function useSwitch3(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_3'));
}

export function useSwitch4(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_4'));
}

export function useSwitch5(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_5'));
}

export function useElectricCurrent(): number {
  const current = useDp<number, any>(getGlobalParamsDp('cur_current'))[0] || 0
  return localeNumber(current, 1)
}

export function useVoltage(): number {
  const voltage = useDp<number, any>(getGlobalParamsDp('cur_voltage'))[0] / 10 || 0;
  return localeNumber(voltage, 1)
}

export function usePower(): number {
  const power = useDp<number, any>(getGlobalParamsDp('cur_power'))[0] / 10 || 0;
  return localeNumber(power, 1)
}

interface SwitchNames {
  title: string
  secondaryTitle: string
  key: number
}

export function isNewProduct() {
  return isSupportFunctions('switch_5', 'countdown_5')
}

function generateSwitchNames(description: string) {
  const baseKeys: I18nKey[] = ['power_strip_tile_socket_1_headline', 'power_strip_tile_socket_2_headline', 'power_strip_tile_socket_3_headline'];
  const baseTitles = baseKeys.map(key => I18n.getLang(key)); // 缓存 I18n 结果
  const switchNames: SwitchNames[] = [];

  // 添加基础项
  baseTitles.forEach((title, index) => {
    switchNames.push({
      title,
      secondaryTitle: `${title} ${description}`,
      key: index
    });
  });

  // 根据 isNewProduct 动态添加额外项
  if (isNewProduct()) {
    const title = I18n.getLang('power_strip_tile_socket_4_headline')
    switchNames.push({ title: title, secondaryTitle: `${title} ${description}`, key: switchNames.length })
  }
  const title = I18n.getLang('power_strip_tile_socket_usb_headline')
  switchNames.push({
    title: title,
    secondaryTitle: `${title} ${description}`,
    key: switchNames.length
  });
  return switchNames;
}

export function useSwitches(): [boolean[], ((value: boolean) => Promise<Result<any>>)[]] {
  const [switches, setSwitches] = useState<[boolean, (value: boolean) => Promise<Result<any>>][]>([]);

  // 获取固定开关
  const [switch1, setSwitch1] = useSwitch1();
  const [switch2, setSwitch2] = useSwitch2();
  const [switch3, setSwitch3] = useSwitch3();
  const [switch4, setSwitch4] = useSwitch4();
  // 获取动态开关
  const [switch5, setSwitch5] = isNewProduct()
    ? useSwitch5()
    : [false, () => Promise.resolve({ success: false })]; // 默认值
  useEffect(() => {
    const initialSwitches: [boolean, (value: boolean) => Promise<Result<any>>][] = [
      [switch1, setSwitch1],
      [switch2, setSwitch2],
      [switch3, setSwitch3],
      [switch4, setSwitch4],
    ];

    if (isNewProduct()) {
      initialSwitches.push([switch5, setSwitch5]);
    }

    setSwitches(initialSwitches);
  }, [switch1, switch2, switch3, switch4, switch5, isNewProduct()]);

  // 分离 values 和 setValues
  const values: boolean[] = switches.map(([value]) => value);
  const setValues: ((value: boolean) => Promise<Result<any>>)[] = switches.map(([, setValue]) => setValue);

  return [values, setValues];
}

function countdownDps() {
  const dps = [getGlobalParamsDp('countdown_1'), getGlobalParamsDp('countdown_2'), getGlobalParamsDp('countdown_3'), getGlobalParamsDp('countdown_4')]
  if (isNewProduct()) {
    dps.push(getGlobalParamsDp('countdown_5'))
  }
  return dps
}

export function useSwitchNames(): [SwitchNames[], (value: SwitchNames[]) => Promise<Result<any>>] {
  const devId = useDeviceId()
  const description = I18n.getLang('power_strip_specific_settings_desc_socket_1')?.split(' ')[2] || ''
  const state = useReactive({
    switchNames: generateSwitchNames(description)
  })
  useEffect(() => {
    NativeApi.getJson(devId, 'powerStripTitle')
      .then(res => {
        if (res.success && res.data && JSON.parse(res.data)?.length) {
          const data = JSON.parse(res.data)
          const newSwitchNames = [...state.switchNames]
          newSwitchNames.forEach(item => {
            item.secondaryTitle = data.find((it: SwitchNames) => it.key === item.key)?.secondaryTitle || item.secondaryTitle
          })
          state.switchNames = newSwitchNames
        }
      })
  }, [])
  const setRemoteSwitchNames = useCallback(async (value: SwitchNames[]) => {
    state.switchNames = value
    return await NativeApi.putJson(devId, 'powerStripTitle', JSON.stringify(value))
  }, [])

  return [state.switchNames, setRemoteSwitchNames]
}

function joinSocketName(title: string, name?: string) {
  const arr = [title]
  name && arr.push(name)
  return arr.join(' | ')
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp('countdown_1')
}

export function isSupportRandomTime() {
  return SupportUtils.isSupportDp('random_time');
}

export function isSupportFixedTime() {
  return SupportUtils.isSupportDp('cycle_time');
}

export function isSupportPowerBehavior() {
  return SupportUtils.isSupportDp('relay_status');
}


export function useAdvancedData(switchNames: SwitchNames[]): AdvancedData[] {
  const res: AdvancedData[] = [];
  const deviceId = useDeviceId();
  const [switches] = useSwitches()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [powerBehaviors] = usePowerBehavior(['relay_status'])
  const [randomTimeList] = useRandomTime(getGlobalParamsDp('random_time'), true, true);
  const [fixedTimeList] = useFixedTime(getGlobalParamsDp('cycle_time'), true, true);
  const [childLock] = useChildLock(getGlobalParamsDp('child_lock'))
  const [switchInching] = useSwitchInching(getGlobalParamsDp('switch_inching'))

  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    randomTimeStatus: AdvancedStatus.Disable,
    fixedTimeStatus: AdvancedStatus.Disable,
  });

  const plugApplyFor = useMemo(() => {
    return switchNames.map((item, index) => (
      {
        index: index,
        type: 'socket',
        key: item.title,
        name: item.secondaryTitle,
        dp: getGlobalParamsDp(`switch_${index + 1}`),
        enable: true,
      } as ApplyForItem
    ))
  }, [switchNames, isNewProduct()])

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  useEffect(() => {
    state.randomTimeStatus = randomTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(randomTimeList)]);

  useEffect(() => {
    state.fixedTimeStatus = fixedTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(fixedTimeList)]);

  const manualDataDp2Obj = useCallback(() => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.PowerStrip,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
        },
      },
      isManual: true,
      mood: undefined,
    };
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (_, applyForList: ApplyForItem[]) => {
      const manualDps = {};
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
      })
      return manualDps;
    },
    []
  );

  res.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(plugApplyFor),
        applyForDisabled: false,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: false,
        isSupportTemperature: false,
        isPowerStrip: true
      } as TimeSchedulePageParams,
    },
  });

  if (isSupportTimer()) {
    const params = useMemo(() => {
      const list = switchNames.map((item, index) => {
        const dp = index + 1
        const label = dp === switchNames.length ? 'usb' : `${dp}`
        return {
          label: joinSocketName(item.title, item.secondaryTitle),
          cloudKey: 'plugCountDownInfo',
          value: 'socket',
          dpId: getGlobalParamsDp(`countdown_${dp}`),
          enableDp: getGlobalParamsDp(`switch_${dp}`),
          stringOn: `power_strip_feature_2_socket_${label}_text_min_on`,
          stringOff: `power_strip_feature_2_socket_${label}_text_min_off`,
        }
      })
      return createParams({ dps: list })
    }, [JSON.stringify(switchNames)])
    // @ts-ignore
    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() => {
      const countdownDpIds = countdownDps()
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        const switchIndex = countdownDpIds.indexOf(timer.dpId)
        const switchLed = switches[switchIndex]
        const item = (switchIndex + 1) === switches.length ? 'usb' : (switchIndex + 1)
        // @ts-ignore
        const key: I18nKey = `socket${item}_active_timer_field_small_${switchLed ? 'off' : 'on'}_text`
        return I18n.formatValue(key, timeFormatToRealTime(timer.countdown[0]))
      })
    }, [JSON.stringify(switches), JSON.stringify(tasks)])
    NativeApi.log(JSON.stringify(timerTask))
    res.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'countdown_1', code: getGlobalParamsDp('countdown_1') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  if (isSupportFixedTime()) {
    const params = createParams<FixedTimePageParams>({
      fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_time'),
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
      showTags: true
    });

    res.push({
      title: I18n.getLang('fixedTimeCycle_socket_headline'),
      statusColor: getAdvancedStatusColor(state.fixedTimeStatus),
      dp: { key: 'cycle_time', code: getGlobalParamsDp('cycle_time') },
      router: {
        key: RouterKey.ui_biz_fixed_time_new,
        params,
      },
    });
  }

  if (isSupportRandomTime()) {
    const params = createParams<RandomTimePageParams>({
      randomTimeDpCode: getGlobalParamsDp('random_time'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_time')
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
      showTags: true
    });

    res.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      statusColor: getAdvancedStatusColor(state.randomTimeStatus),
      dp: { key: 'random_time', code: getGlobalParamsDp('random_time') },
      router: {
        key: RouterKey.ui_biz_random_time_new,
        params,
      },
    });
  }

  if (isSupportPowerBehavior()) {
    const params = createParams<PowerBehaviorPageParams>({
      powerBehaviorKeys: ['relay_status']
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_relay_status'),
      statusColor: getAdvancedStatusColor(powerBehaviors.some(item => !!item) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'relay_status', code: getGlobalParamsDp('relay_status') },
      router: {
        key: RouterKey.ui_biz_power_behavior_plug,
        params
      },
    })
  }

  if (isSupportFunctions('child_lock')){
    const params = createParams<ChildLockPageParams>({
      childLockCode: getGlobalParamsDp('child_lock')
    })
    res.push({
      title: I18n.getLang('sockets_specific_settings_child_lock'),
      statusColor: getAdvancedStatusColor(childLock ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'child_lock', code: getGlobalParamsDp('child_lock') },
      router: {
        key: RouterKey.ui_biz_child_lock,
        params
      },
    })
  }

  if (isSupportFunctions('switch_inching')){
      const params = createParams<SwitchInchingPageParams>({
        switchIngCode: getGlobalParamsDp('switch_inching'),
        channelConfig: [
          { channel: 0, channelTitle: I18n.getLang('power_strip_tile_socket_1_headline'), countdownCode: getGlobalParamsDp('countdown_1') },
          { channel: 1, channelTitle: I18n.getLang('power_strip_tile_socket_2_headline'), countdownCode: getGlobalParamsDp('countdown_2') },
          { channel: 2, channelTitle: I18n.getLang('power_strip_tile_socket_3_headline'), countdownCode: getGlobalParamsDp('countdown_3') },
          { channel: 3, channelTitle: I18n.getLang('power_strip_tile_socket_4_headline'), countdownCode: getGlobalParamsDp('countdown_4') },
          { channel: 4, channelTitle: I18n.getLang('power_strip_tile_socket_usb_headline'), countdownCode: getGlobalParamsDp('countdown_5') },
        ],
        conflictDps: {
          randomTimeDpCode: getGlobalParamsDp('random_time'),
          fixedTimeDpCode: getGlobalParamsDp('cycle_time')
        }
      })
      res.push({
        title: I18n.getLang('sockets_specific_settings_switch_inching'),
        statusColor: getAdvancedStatusColor(switchInching.some(item => item.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
        dp: { key: 'switch_inching', code: getGlobalParamsDp('switch_inching') },
        router: {
          key: RouterKey.ui_biz_switch_inching,
          params
        },
      })
    }

  const historyParams = useMemo(() => {
    const dpIds: string[] = []
    const tags = {}
    switchNames.forEach((item, index) => {
      const dpId = getGlobalParamsDp(`switch_${index + 1}`)
      dpIds.push(dpId)
      tags[dpId] = item.title
    })
    return createParams<SwitchHistoryPageRouteParams>({
      dpIds: dpIds,
      tags: tags,
      getActionsText: (dpData: any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
    })
  }, [switchNames])

  res.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return res;
}
