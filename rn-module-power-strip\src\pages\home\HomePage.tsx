import React, {Fragment, useEffect, useMemo} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import {useIsFocused, useNavigation} from '@react-navigation/core'
import {useInterval, useReactive} from 'ahooks';
import Page from '@ledvance/base/src/components/Page';
import {useDeviceId, useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import {NativeApi, queryDpIds} from '@ledvance/base/src/api/native';
import {
  isNewProduct,
  useAdvancedData,
  useElectricCurrent,
  usePower,
  useSwitches,
  useSwitchNames,
  useVoltage,
} from '../../hooks/FeatureHooks';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import I18n from '@ledvance/base/src/i18n';
import SocketItem from '@ledvance/base/src/components/SocketItem'
import Spacer from '@ledvance/base/src/components/Spacer';
import {cloneDeep} from 'lodash';
import {Utils} from 'tuya-panel-kit';
import Card from '@ledvance/base/src/components/Card';
import {
  EnergyConsumptionPageProps
} from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionPage'
import {RouterKey} from 'navigation/Router';
import {getGlobalParamsDp} from '@ledvance/base/src/utils/common';
import {
  getEnergyGenerationValue
} from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionActions';

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const HomePage = (props: { theme?: any }) => {
  const devInfo = useDeviceInfo();
  const devId = useDeviceId()
  const familyName = useFamilyName();
  const navigation = useNavigation()
  const isFocused = useIsFocused()
  const [switches, setSwitches] = useSwitches()
  const [switchNames, setSwitchNames] = useSwitchNames()
  const power = usePower()
  const electricCurrent = useElectricCurrent()
  const voltage = useVoltage()
  const advanceData = useAdvancedData(switchNames);
  const state = useReactive({
    loading: false,
    isGeneration: false
  });

  const icons = useMemo(() => {
    if (isNewProduct()) {
      return [res.new_switch_1, res.new_switch_2, res.new_switch_3, res.new_switch_4, res.new_switch_usb]
    } else {
      return [res.switch_1, res.switch_2, res.switch_3, res.switch_usb]
    }
  }, [isNewProduct()])

  useInterval(() => {
      if (isFocused) {
        const jsonData = JSON.stringify([getGlobalParamsDp('cur_current'), getGlobalParamsDp('cur_power'), getGlobalParamsDp('cur_voltage')])
        queryDpIds(jsonData, devId).then()
      }
    },
    3000,
    {immediate: true}
  )

  useEffect(() => {
    getEnergyGenerationValue(devId).then(data => {
      state.isGeneration = !!data?.generationMode
    })
  }, [isFocused])

  const unitDivision = (str) => {
    if (!str) { return ['', ''] }
    const strIndex = str.indexOf('(') || str.indexOf('（')
    const unit = str.substring(strIndex)
    const name = str.split(unit)[0]
    return [name, unit]
  }

  const styles = StyleSheet.create({
    consumedEnergyCard: {
      marginHorizontal: cx(24),
    },
    consumedEnergyCardTitle: {
      marginHorizontal: cx(16),
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    subContent: {
      flex: 1,
      alignItems: 'center',
      marginBottom: cx(9)
    },
    valueText: {
      fontSize: cx(24),
      fontWeight: 'bold',
      color: props.theme.global.secondBrand,
    },
    titleText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme.global.secondFontColor,
      textAlign: 'center',
    },
    unitText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme.global.secondFontColor,
    },
  })


  const ConsumedEnergyItem = (props: { value: number, unit: string }) => {
    return (
      <View style={styles.subContent}>
        <Text style={styles.valueText}>{(props.value) || 0}</Text>
        <Spacer height={cx(4)} />
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[0]}
        </Text>
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[1]}
        </Text>
      </View>
    )
  }

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer />
        <Card
          style={styles.consumedEnergyCard}
          onPress={() => navigation.navigate(RouterKey.ui_biz_energy_consumption, {
            electricDpCode: getGlobalParamsDp('cur_current'),
            powerDpCode: getGlobalParamsDp('cur_power'),
            voltageDpCode: getGlobalParamsDp('cur_voltage'),
            addEleDpCode: getGlobalParamsDp('add_ele'),
          } as EnergyConsumptionPageProps)}>
          <Spacer height={cx(16)} />
          <Text style={styles.consumedEnergyCardTitle}>{I18n.getLang(state.isGeneration ? 'sockets_headline_power' : 'sockets_ce')}</Text>
          <Spacer height={cx(18)} />
          <View style={{ flexDirection: 'row' }}>
            <ConsumedEnergyItem
              value={power}
              unit={I18n.getLang('consumption_data_field2_value_text1')} />
            <ConsumedEnergyItem
              value={electricCurrent}
              unit={I18n.getLang('consumption_data_field2_value_text2')} />
            <ConsumedEnergyItem
              value={voltage}
              unit={I18n.getLang('consumption_data_field2_value_text3')} />
          </View>
          <Spacer height={cx(17)} />
        </Card>
        <Spacer height={cx(30)}/>
        {
          switchNames.map((item, index) => {
            return (
              <Fragment key={`socket_${index}`}>
                <SocketItem
                  title={item.title}
                  name={item.secondaryTitle}
                  icon={{ uri: icons[index] }}
                  onNameChange={async newName => {
                    state.loading = true
                    const newSwitchNames = cloneDeep(switchNames)
                    newSwitchNames[index].secondaryTitle = newName
                    await setSwitchNames(newSwitchNames)
                    state.loading = false
                  }}
                  enabled={switches[index]}
                  onSwitchChange={async enable => {
                    state.loading = true
                    await setSwitches[index](enable)
                    state.loading = false
                  }}
                />
                <Spacer />
              </Fragment>
            )
          })
        }
        <AdvanceList advanceData={advanceData} />
      </ScrollView>
    </Page>
  );
};

export default withTheme(HomePage)
