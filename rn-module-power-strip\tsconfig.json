{
  "compilerOptions": {
    /* Basic Options */
    "target": "ES2017",                          /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017','ES2018' or 'ESNEXT'. */
    "module": "commonjs",                     /* Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', or 'ESNext'. */
    "jsx": "react",                        /* Specify JSX code generation: 'preserve', 'react-native', or 'react'. */

    /* Strict Type-Checking Options */
    /* "strict": true                         /* Enable all strict type-checking options. */
    "noImplicitAny": false,                    /* Raise error on expressions and declarations with an implied 'any' type. */
    "strictNullChecks": true,                 /* Enable strict null checks. */

    /* Additional Checks */
    "noUnusedLocals": true,                   /* Report errors on unused locals. */
    "noUnusedParameters": true,               /* Report errors on unused parameters. */

    /* Module Resolution Options */
    "moduleResolution": "node",               /* Specify module resolution strategy: 'node' (Node.js) or 'classic'  */
    "types": ["react", "react-native"],       /* Type declaration files to be included in compilation. */
    "typeRoots": ["@types/*.d.ts"],
    "allowSyntheticDefaultImports": true,     /* Allow default imports from modules with no default export. This does not affect code emit, just typechecking. */
    "esModuleInterop": true,                  /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */
    "baseUrl": "./src",
    "paths": {
      "@api": ["./api"],
      "@components": ["./components"],
      "@config": ["./config"],
      "@i18n": ["./i18n"],
      "@models": ["./models"],
      "@res": ["./res"],
      "@utils": ["./utils"]
    }
  },
  "exclude": [
    "node_modules"
  ]
}