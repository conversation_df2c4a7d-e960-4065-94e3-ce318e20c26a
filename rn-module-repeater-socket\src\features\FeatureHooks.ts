import {AdvancedData, AdvancedStatus, getAdvancedStatusColor} from '@ledvance/base/src/components/AdvanceCard'
import {useDeviceId, useDp, useTimeSchedule} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {Result} from '@ledvance/base/src/models/modules/Result';
import {getGlobalParamsDp, isSupportFunctions} from '@ledvance/base/src/utils/common';
import {getDeviceExtInfo, saveDeviceExtInfo} from '@ledvance/base/src/models/TuyaApi'
import I18n from "@ledvance/base/src/i18n";
import {useCallback, useEffect, useMemo, useState} from 'react';
import { RouterKey } from 'navigation/Router';
import {cloneDeep} from "lodash";
import { ApplyForItem } from '@ledvance/base/src/utils/interface';
import { DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { DeviceStateType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import {useReactive, useUpdateEffect} from "ahooks";
import {NativeApi} from "@ledvance/base/src/api/native";
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import {createParams} from "@ledvance/base/src/hooks/Hooks";
import {timeFormatToRealTime, useCountdowns} from "@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction";
import { SwitchHistoryPageRouteParams } from '@ledvance/ui-biz-bundle/src/modules/history/HistoryPage';

export enum SignalStrength {
  Bad = 'bad',
  Good = 'good',
  Great = 'great'
}
export interface Names {
  repeaterName: string
  switch1Name: string
  switch2Name: string
}

export function useSwitch1(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_1'));
}

export function useSwitch2(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_2'));
}

export function useSignalStrength() {
  return useDp<SignalStrength, any>(getGlobalParamsDp('rp_signal_strength'));
}

export function useIsPasswordSet(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('is_password_set'))
}

export function useSwitchNames(): [Names, (name: Names) => Promise<any>] {
  const devId = useDeviceId()
  const [names, setNames] = useState({
    repeaterName: '',
    switch1Name: '',
    switch2Name: ''
  })

  useEffect(() => {
    getSwitchNames(devId).then(names => {
      if (names) {
        setNames(names)
      }
    })
  }, [])

  const setSwitchNames = useCallback(async (names: Names) => {
    const res = await saveSwitchNames(devId, names)
    if (res) {
      setNames(names)
    }
  }, [devId])

  return [names, setSwitchNames]
}

export function useAdvanceData(): AdvancedData[] {
  const advanceData = [] as AdvancedData[]
  const deviceId = useDeviceId();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [switch1] = useSwitch1()
  const [switch2] = useSwitch2()
  const state = useReactive({
    timeScheduleStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const plugApplyFor = useMemo(() => {
    const applyFor: ApplyForItem[] = [{
      type: 'socket',
      key: I18n.getLang('switchmodule_switch1title'),
      name: I18n.getLang('switchmodule_switch1title'),
      dp: getGlobalParamsDp('switch_1'),
      enable: true,
    }, {
      type: 'socket',
      key: I18n.getLang('switchmodule_switch2title'),
      name: I18n.getLang('switchmodule_switch2title'),
      dp: getGlobalParamsDp('switch_2'),
      enable: true,
    }]
    return applyFor
  }, [])

  const manualDataDp2Obj = useCallback(() => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
        },
      },
      isManual: true,
      mood: undefined,
    };
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (_, applyForList: ApplyForItem[]) => {
      const manualDps = {};
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
      })
      return manualDps;
    },
    []
  );

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(plugApplyFor),
        applyForDisabled: false,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: false,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    }
  })

  if (isSupportFunctions('countdown_1')) {
    const params = useMemo(() => {
      const dps: any[] = []
      dps.push({
        label: I18n.getLang('switchmodule_switch1title'),
        cloudKey: 'socketInfo',
        dpId: getGlobalParamsDp('countdown_1'),
        enableDp: getGlobalParamsDp('switch_1'),
        stringOn: 'timer_switch1_active_timer_field_description_on_text',
        stringOff: 'timer_switch1_active_timer_field_description_off_text',
      }, {
        label: I18n.getLang('switchmodule_switch2title'),
        cloudKey: 'socketInfo',
        dpId: getGlobalParamsDp('countdown_2'),
        enableDp: getGlobalParamsDp('switch_2'),
        stringOn: 'timer_switch2_active_timer_field_description_on_text',
        stringOff: 'timer_switch2_active_timer_field_description_off_text',
      })

      return createParams({ dps })
    }, [])

    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() => {
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let switchLed = false
        let item = ''
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown_1'):
            switchLed = switch1
            item = '1'
            break
          case getGlobalParamsDp('countdown_2'):
            switchLed = switch2
            item = '2'
            break
        }
        // @ts-ignore
        const key: I18nKey = `switch${item}_active_timer_field_small_${switchLed ? 'off' : 'on'}_text`
        return I18n.formatValue(key, timeFormatToRealTime(timer.countdown[0]))
      })
    }, [switch1, switch2, JSON.stringify(tasks)])

    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'countdown_1', code: getGlobalParamsDp('countdown_1') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      }
    })
  }

  const historyParams = useMemo(() => {
    const dpIds =  [getGlobalParamsDp('switch_1'), getGlobalParamsDp('switch_2')]
    const tags = {
      [getGlobalParamsDp('switch_1')]:  I18n.getLang('switchmodule_switch1title'),
      [getGlobalParamsDp('switch_2')]: I18n.getLang('switchmodule_switch2title'),
    }
    return createParams<SwitchHistoryPageRouteParams>({
      dpIds,
      tags,
      getActionsText: (dpData: any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
    })
  }, [])

  advanceData.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return advanceData;
}

async function getSwitchNames(devId: string) {
  const data = await getDeviceExtInfo(devId, 'NAMES')
  if (data) {
    return JSON.parse(data)
  }
  return null
}

async function saveSwitchNames(devId: string, names: Names) {
  return await saveDeviceExtInfo(devId, 'NAMES', JSON.stringify(names))
}
