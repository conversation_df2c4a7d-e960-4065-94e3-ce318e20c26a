import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import RepeaterPage from 'pages/repeater/RepeaterPage'
import SelectRouterPage from 'pages/repeater/SelectRouterPage';
import RepeaterSettingPage from 'pages/repeater/RepeaterSettingPage'
import RepeaterTimingPage from 'pages/repeater/RepeaterTimingPage'
import ConfirmPasswordPage from 'pages/repeater/ConfirmPasswordPage'
import InternetAccessPage from 'pages/repeater/InternetAccessPage'
import ScheduleDetailPage from 'pages/repeater/ScheduleDetailPage'

export const RouterKey = {
    main: 'main',
    repeater: 'repeater',
    selectRouter: 'selectRouter',
    repeaterSetting: 'repeaterSetting',
    repeaterTiming: 'repeaterTiming',
    confirmPassword: 'confirmPassword',
    internetAccess: 'internetAccess',
    scheduleDetail: 'scheduleDetail',
    ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.repeater,
        component: RepeaterPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.selectRouter,
        component: SelectRouterPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.repeaterSetting,
        component: RepeaterSettingPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.repeaterTiming,
        component: RepeaterTimingPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },

    },
    {
        name: RouterKey.confirmPassword,
        component: ConfirmPasswordPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.internetAccess,
        component: InternetAccessPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.scheduleDetail,
        component: ScheduleDetailPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    ...TimerPageRouters,
    ...TimeSchedulePageRouters,
    ...HistoryPageRouters,
]
