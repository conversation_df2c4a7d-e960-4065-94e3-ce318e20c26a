import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, { useMemo } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Dialog, TYSdk, Utils } from 'tuya-panel-kit'
import { NativeApi } from '@ledvance/base/src/api/native'
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native"
import Card from "@ledvance/base/src/components/Card";
import SocketItem from "@ledvance/base/src/components/SocketItem";
import Spacer from "@ledvance/base/src/components/Spacer";
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import I18n from '@ledvance/base/src/i18n'
import Res from '@res'
import {
  SignalStrength,
  useAdvanceData,
  useIsPasswordSet,
  useSignalStrength,
  useSwitch1,
  useSwitch2,
  useSwitchNames
} from "../../features/FeatureHooks";
import { useReactive } from "ahooks";
import { cloneDeep } from "lodash";
import { useNavigation } from '@react-navigation/native'
import { RouterKey } from 'navigation/Router'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const RepeaterUiId = '000001dsfx'
const HomePage = (props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const [signalStrength] = useSignalStrength()
  const [switch1, setSwitch1] = useSwitch1()
  const [switch2, setSwitch2] = useSwitch2()
  const [isPasswordSet, setIsPasswordSet] = useIsPasswordSet()
  const [switchNames, setSwitchNames] = useSwitchNames()
  const navigation = useNavigation()
  const advanceData = useAdvanceData()

  const state = useReactive({
    loading: false,
  })

  const signalIcon = useMemo(() => {
    switch (signalStrength) {
      case SignalStrength.Bad:
        return Res.wifiLow
      case SignalStrength.Good:
        return Res.wifiGood
      case SignalStrength.Great:
        return Res.wifiGreat
      default:
        return Res.wifiLow
    }
  }, [signalStrength])

  const styles = StyleSheet.create({
    container: { 
      flexDirection: 'row', 
      justifyContent: 'center', 
      alignItems: 'center', 
      marginHorizontal: cx(16), 
      marginVertical: cx(16) 
    },
    title: {
      fontSize: cx(16),
      color: props.theme?.global.fontColor,
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    icon: {
      width: cx(80),
      height: cx(20),
    },
    nameLine: {
      marginHorizontal: cx(16),
    },
    name: {
      fontSize: cx(14),
      color: props.theme?.global.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
  })

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer />
        <Card style={{ marginHorizontal: cx(24) }}>
          <TouchableOpacity onPress={() => {
            TYSdk.mobile.jumpSubPage({ uiId: RepeaterUiId }, {})
          }}>
            <View style={styles.container}>
              <View style={{ flex: 1 }}>
                <Text style={styles.title}>{I18n.getLang('wifi_repeater_title')}</Text>
                <Spacer height={cx(8)} />
                <TouchableOpacity
                  onPress={() => {
                    Dialog.prompt({
                      title: I18n.getLang('routines_add_edit_name'),
                      value: switchNames.repeaterName,
                      placeholder: I18n.getLang('wifi_repeater_description'),
                      cancelText: I18n.getLang('auto_scan_system_cancel'),
                      confirmText: I18n.getLang('auto_scan_system_wifi_confirm'),
                      inputWrapperStyle: { backgroundColor: props.theme?.textInput.background, borderRadius: cx(10) },
                      onChangeText: text => {
                        return text.length <= 32 ? text : text.slice(0, 32)
                      },
                      onConfirm: async (text, { close }) => {
                        state.loading = true
                        const newSwitchNames = cloneDeep(switchNames)
                        newSwitchNames.repeaterName = text
                        await setSwitchNames(newSwitchNames)
                        state.loading = false
                        close()
                      },
                    })
                  }}>
                  <Text style={styles.name}>{switchNames.repeaterName || I18n.getLang('wifi_repeater_description')}</Text>
                </TouchableOpacity>
              </View>
              <Spacer height={0} width={cx(12)} />
              <Image source={signalIcon} width={cx(20)} height={cx(20)} style={{ tintColor: props.theme?.icon.normal }} />
            </View>
          </TouchableOpacity>
        </Card>
        <Spacer />
        <SocketItem
          title={I18n.getLang('switchmodule_switch1title')}
          name={switchNames.switch1Name}
          placeholder={I18n.getLang('power_strip_specific_settings_desc_socket_1')}
          icon={undefined}
          onNameChange={async newName => {
            state.loading = true
            const newSwitchNames = cloneDeep(switchNames)
            newSwitchNames.switch1Name = newName
            await setSwitchNames(newSwitchNames)
            state.loading = false
          }}
          // enabled={switch1}
          enabled={isPasswordSet}
          onSwitchChange={async enable => {
            // state.loading = true
            // await setSwitch1(enable)
            // state.loading = false
            setIsPasswordSet(enable)
          }}
        />
        <Spacer />
        <SocketItem
          title={I18n.getLang('switchmodule_switch2title')}
          name={switchNames.switch2Name}
          placeholder={I18n.getLang('power_strip_specific_settings_desc_socket_2')}
          icon={undefined}
          onNameChange={async newName => {
            state.loading = true
            const newSwitchNames = cloneDeep(switchNames)
            newSwitchNames.switch2Name = newName
            await setSwitchNames(newSwitchNames)
            state.loading = false
          }}
          enabled={switch2}
          onSwitchChange={async enable => {
            // state.loading = true
            // await setSwitch2(enable)
            // state.loading = false
            const routerKey = isPasswordSet ? RouterKey.repeater : RouterKey.repeaterSetting
            navigation.navigate(routerKey, { activeTab: 'device' })
          }}
        />
        <Spacer />
        <AdvanceList advanceData={advanceData} />
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
