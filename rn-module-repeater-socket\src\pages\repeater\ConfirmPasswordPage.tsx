import React, { useMemo } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { NativeModules, StyleSheet, Text } from "react-native"
import { Utils } from 'tuya-panel-kit'
import Card from '@ledvance/base/src/components/Card'
import TextField from '@ledvance/base/src/components/TextField'
import { useReactive } from 'ahooks'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import res from '@ledvance/base/src/res'
import { RouterKey } from 'navigation/Router'
import { useNavigation } from '@react-navigation/native'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface ConfirmPasswordProps {
  theme?: ThemeType
}

const ConfirmPasswordPage = (props: ConfirmPasswordProps) => {
  const { ssid, encrypted } = useParams<{ ssid: string, encrypted: boolean }>()
  const navigation = useNavigation()

  const state = useReactive({
    password: '',
  })

  const canSave = useMemo(() => {
    return !encrypted || state.password
  }, [state.password])

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      padding: cx(10)
    },
    title: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      marginBottom: cx(16),
    },
    tips: {
      color: props.theme?.global.secondFontColor,
      marginHorizontal: cx(24),
    },
  })

  return (
    <Page
      backText={I18n.getLang('login_textfield_headline_pw')}
      rightButtonIcon={canSave ? res.ic_check : res.ic_uncheck}
      rightButtonIconClick={async () => {
        if (!canSave) {
          return
        }
        NativeModules.TYRCTRouteGatewayManager.repeaterSource({ router: { "pwd": state.password, "ssid": ssid }, "reqType": "repeaterSource" })
        navigation.navigate(RouterKey.repeaterTiming, { source: 'router' })
      }}
    >
      <Spacer />
      <Card style={styles.card}>
        <Text style={styles.title}>{I18n.formatValue('repeater_ssid_desc', ssid)}</Text>
        <TextField
          placeholder={I18n.getLang('login_textfield_headline_pw')}
          value={state.password}
          onChangeText={(t: string) => { state.password = t }}
          showError={encrypted && state.password.length === 0}
          errorText={I18n.getLang('password_required')}
        />
      </Card>
      <Spacer />
      <Text style={styles.tips}>{I18n.getLang('repeater_change_wifi_tips')}</Text>
    </Page>
  )
}

export default withTheme(ConfirmPasswordPage)
