import React, { useEffect } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { FlatList, Image, NativeModules, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native"
import { TYSdk, Utils } from 'tuya-panel-kit'
import { useDeviceInfo } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { useReactive } from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import I18n from '@ledvance/base/src/i18n'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import { useStationConfig } from './RepeaterActions'
import res from '@ledvance/base/src/res'
import ScheduleCard from './ScheduleCard'
import { getGlobalParamsDp, showDialog } from '@ledvance/base/src/utils/common'
import { RouterKey } from 'navigation/Router'
import { useNavigation } from '@react-navigation/native';
import InfoText from '@ledvance/base/src/components/InfoText'
import DeleteButton from '@ledvance/base/src/components/DeleteButton'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils
const { timezone } = Utils.TimeUtils

interface InternetAccessProps {
  theme?: ThemeType
}

const InternetAccessPage = (props: InternetAccessProps) => {
  const { mac } = useParams<{ mac: string }>()
  const devInfo = useDeviceInfo()
  const stationConfig = useStationConfig()
  const navigation = useNavigation()
  const state = useReactive({
    loading: false,
    schedules: [] as any[]
  })

  useEffect(() => {
    NativeModules.TYRCTPanelManager.putDpData({ "option": 3, "command": { [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\" }` } }, () => { }, () => { })
    getSchedules()
  }, [])

  const updateInternetAccess = (enable: boolean) => {
    NativeModules.TYRCTPanelManager.putDpData({ "option": 3, "command": { [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\", \"allowNetwork\": ${enable} }` } }, () => { }, () => { })
  }

  const getSchedules = () => {
    state.loading = true
    TYSdk.native.apiRNRequest({
      a: 'tuya.m.timer.group.list',
      postData: {
        category: "light",
        bizId: devInfo.devId,
        type: "device"
      },
      v: '2.0'
    }, (res: any) => {
      console.log('get schedules', res);
      const schedules = JSON.parse(res).groups.map((item: any, index: number) => {
        const schedule = {
          groupId: item.id,
          aliasName: item.timers[0].aliasName || `Schedule ${index + 1}`,
          status: item.timers[0].status || item.timers[1].status,
          loops: item.timers[0].loops,
          startTime: item.timers[0].time,
          endTime: item.timers[1].time,
          mac: JSON.parse(item.timers[0].dps['55']).mac,
        }
        return schedule
      }).filter((item: any) => item.mac === mac)
      state.schedules = schedules
      console.log('schedules', schedules);
      state.loading = false
    }, (err: any) => {
      state.loading = false
    })
  }

  const changeScheduleStatus = (item: any, status: boolean) => {
    state.loading = true
    TYSdk.native.apiRNRequest({
      a: 'tuya.m.timer.group.status.update',
      postData: {
        groupId: item.groupId,
        status: status ? 1 : 0,
        category: 'light',
        bizId: devInfo.devId,
        type: 'device'
      },
      v: '2.0'
    }, () => {
      item.status = status
      state.loading = false
    }, () => {
      state.loading = false
    })
  }

  const deleteSchedule = (item: any) => {
    state.loading = true
    TYSdk.native.apiRNRequest({
      a: 'tuya.m.timer.group.remove',
      postData: {
        groupId: item.groupId,
        category: 'light',
        bizId: devInfo.devId,
        type: 'device'
      },
      v: '2.0'
    }, (res: any) => {
      console.log('changeScheduleStatus success', res);
      state.schedules = state.schedules.filter((it: any) => it.groupId !== item.groupId)
      state.loading = false
      navigation.goBack()
    }, (err: any) => {
      console.log('changeScheduleStatus error', err);
      state.loading = false
    })
  }

  const addSchedule = (item: any) => {
    state.loading = true
    const schedule = {
      aliasName: item.aliasName,
      loops: item.loops,
      category: 'light',
      options: {},
      actionType: 'device',
      isAppPush: false,
      timeZone: timezone(),
      bizType: 0,
      bizId: devInfo.devId,
      instruct: [{
        dps: {
          [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\", \"allowNetwork\": true }`
        },
        time: item.startTime,
      }, {
        dps: {
          [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\", \"allowNetwork\": false }`
        },
        time: item.endTime,
      }]
    }
    TYSdk.native.apiRNRequest({
      a: 'tuya.m.timer.group.add',
      postData: schedule,
      v: '4.0'
    }, (res: any) => {
      console.log("addSchedule res===========>", res);
      navigation.goBack()
      getSchedules()
    }, (error: any) => {
      console.log("addSchedule error===========>", error);
      state.loading = false
      const errorCode = JSON.parse(error).errorCode
      const title = (errorCode === 'LINKAGE_TIMER_OVER_MAX_LIMIT_30') ? I18n.getLang('repeater_schedule_error_limit') : I18n.getLang('repeater_schedule_error_common')
      showDialog({
        method: 'alert',
        title: title,
        onConfirm: (_, { close }) => {
          close()
        }
      })
    })
  }

  const editSchedule = (item: any) => {
    const schedule = {
      groupId: item.groupId,
      aliasName: item.aliasName,
      loops: item.loops,
      category: 'light',
      options: {},
      actionType: 'device',
      isAppPush: false,
      timeZone: timezone(),
      bizType: 0,
      bizId: devInfo.devId,
      instruct: [{
        dps: {
          [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\", \"allowNetwork\": true }`
        },
        time: item.startTime,
      }, {
        dps: {
          [getGlobalParamsDp("sta_config")]: `{ \"mac\": \"${mac}\", \"allowNetwork\": false }`
        },
        time: item.endTime,
      }]
    }
    TYSdk.native.apiRNRequest({
      a: 'tuya.m.timer.group.update',
      postData: schedule,
      v: '4.0'
    }, (res: any) => {
      console.log("editSchedule res===========>", res);
      navigation.goBack()
      getSchedules()
    }, (error: any) => {
      console.log("editSchedule error===========>", error);
      state.loading = false
      const errorCode = JSON.parse(error).errorCode
      const title = (errorCode === 'LINKAGE_TIMER_OVER_MAX_LIMIT_30') ? I18n.getLang('repeater_schedule_error_limit') : I18n.getLang('repeater_schedule_error_common')
      showDialog({
        method: 'alert',
        title: title,
        onConfirm: (_, { close }) => {
          close()
        }
      })
    })
  }

  const navigateTo = (mode: 'add' | 'edit' | 'delete', item: any) => {
    navigation.navigate(RouterKey.scheduleDetail, {
      mode, item, onPost: (mode: 'add' | 'edit' | 'delete', item: any) => {
        switch (mode) {
          case 'add':
            addSchedule(item)
            break
          case 'edit':
            editSchedule(item)
            break
          case 'delete':
            deleteSchedule(item)
            break
        }
      }
    })
  }

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24)
    },
    header: {
      marginHorizontal: cx(24),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    title: {
      color: props.theme?.global.brand,
      fontSize: cx(24),
    },
    tips: {
      marginHorizontal: cx(24),
      color: props.theme?.global.secondFontColor,
      marginTop: cx(16),
    },
    addIcon: {
      width: cx(25),
      height: cx(25),
      tintColor: props.theme?.icon.primary
    },
    emptyImage: {
      width: cx(225),
      height: cx(198),
    },
    emptyContainer: {
      marginHorizontal: cx(24),
      alignItems: 'center',
    },
    addBtn: {
      height: cx(40),
      width: 'auto',
      minWidth: cx(150),
      paddingHorizontal: cx(16),
      backgroundColor: props.theme?.button.primary,
    },
  })

  return (
    <Page
      backText={I18n.getLang('repeater_other_device')}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer />
        <Card style={styles.card}>
          <LdvSwitch title={I18n.getLang('internet_access')} enable={stationConfig.allowNetwork} setEnable={updateInternetAccess} />
        </Card>
        <Spacer />
        <View style={styles.header}>
          <Text style={styles.title}>{I18n.getLang('timeschedule_overview_headline_text')}</Text>
          <TouchableOpacity
            onPress={() => {
              navigateTo('add', {})
            }}
          >
            <Image source={{ uri: res.add }} style={styles.addIcon} />
          </TouchableOpacity>
        </View>
        <Text style={styles.tips}>{I18n.getLang('timeschedule_overview_description_text')}</Text>
        <Spacer />
        {
          state.schedules.length ? (<FlatList
            data={state.schedules}
            renderItem={({ item }) => (
              <ScheduleCard
                item={item}
                onEnableChange={async enable => {
                  state.loading = true;
                  changeScheduleStatus(item, enable)
                  state.loading = false;
                }}
                onPress={() => {
                  navigateTo('edit', item)
                }}
                onLongPress={() => {
                  showDialog({
                    method: 'confirm',
                    title: I18n.getLang('cancel_dialog_delete_item_timeschedule_titel'),
                    subTitle: I18n.getLang('cancel_dialog_delete_item_timeschedule_description'),
                    onConfirm: async (_, { close }) => {
                      close();
                      deleteSchedule(item)
                    },
                  });
                }}
              />
            )}
            keyExtractor={item => item.groupId}
            ListEmptyComponent={<Spacer />}
            ListHeaderComponent={() => <Spacer height={cx(10)} />}
            ItemSeparatorComponent={() => <Spacer />}
            ListFooterComponent={() => <Spacer height={cx(30)} />}
          />) : (<View style={styles.emptyContainer}>
            <Spacer height={cx(60)}/>
            <Image
              style={styles.emptyImage}
              source={{uri: res.ldv_timer_empty}}
              resizeMode="contain"
            />
            <InfoText
              icon={res.device_panel_schedule_alert}
              text={I18n.getLang('timeschedule_overview_empty_information_text')}
              style={{width: 'auto', alignItems: 'center'}}
              textStyle={{color: props.theme?.global.secondFontColor, fontSize: cx(12), flex: undefined}}
              iconStyle={{tintColor: props.theme?.global.secondFontColor}}
            />
            <Spacer height={cx(16)}/>
            <DeleteButton
              style={styles.addBtn}
              text={`${I18n.getLang('timeschedule_overview_empty_button_add_text')}`}
              textStyle={{fontSize: cx(12)}}
              onPress={() => {
                navigateTo('add', {});
              }}
            />
          </View>)
        }
      </ScrollView>
    </Page>
  )
}

export default withTheme(InternetAccessPage)
