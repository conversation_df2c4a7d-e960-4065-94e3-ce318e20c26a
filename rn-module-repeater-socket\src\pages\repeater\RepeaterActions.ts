import { useDp } from '@ledvance/base/src/models/modules/NativePropsSlice';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import Res from '@res';
import { useEffect, useState } from 'react';

export interface StationConfig {
  mac: string
  allowNetwork: boolean
  speedLimit?: boolean
  maxUpSpeed?: number
  maxDownSpeed?: number
}

export function useStationConfig(): StationConfig {
  const [dpValue] = useDp<string, any>(getGlobalParamsDp('sta_config'))
  const [config, setConfig] = useState(() => {
    return JSON.parse(dpValue || '{}') as StationConfig
  })

  useEffect(() => { 
    setConfig(JSON.parse(dpValue || '{}'))
  }, [dpValue])

  return config
}

// Base interface for all MQTT events
interface BaseMqttEvent {
  protocol: number
  data: {
    reqType: string
    data: Record<string, any>
  }
}

export interface MacInfo {
  mac: string
  name: string
}

export interface OnlineListEvent extends BaseMqttEvent { 
  protocol: 65
  data: {
    reqType: 'routerOnlineList'
    data: Record<string, any>
    macList: MacInfo[]
  }
}

// Signal query response
export interface SignalQueryEvent extends BaseMqttEvent {
  protocol: 23
  data: {
    reqType: 'sigQry'
    data: {
      signal: number
    }
  }
}

// Router information
export interface RouterInfo {
  ssid: string
  pwd: string
}

// Repeater source response
export interface RepeaterSourceEvent extends BaseMqttEvent {
  protocol: 65
  data: {
    reqType: 'repeaterSource'
    data: Record<string, any>
    router: RouterInfo
  }
}

// Hotspot configuration
export interface HotspotConfig {
  ssid: string
  pwd: string
  hwmode: string
  channel: string
  wifiType: number
  htmode: string
}

// Repeater hotspot response
export interface RepeaterHotspotEvent extends BaseMqttEvent {
  protocol: 65
  data: {
    reqType: 'repeaterHotspot'
    data: Record<string, any>
    hotspot: HotspotConfig[]
  }
}

// WiFi list item
export interface WifiListItem {
  name: string
  signalStrength: number
  encrypted: string
}

// Repeater WiFi list response
export interface RepeaterWifiListEvent extends BaseMqttEvent {
  protocol: 65
  data: {
    reqType: 'repeaterWifiList'
    data: Record<string, any>
    wifiList: WifiListItem[]
  }
}

// Union type for all known MQTT events
export type MqttEvent = 
  | OnlineListEvent
  | SignalQueryEvent
  | RepeaterSourceEvent
  | RepeaterHotspotEvent
  | RepeaterWifiListEvent

// Generic MQTT event for unknown/future event types
export interface GenericMqttEvent extends BaseMqttEvent {
  data: {
    reqType: string
    data: Record<string, any>
    [key: string]: any // Allow additional properties for extensibility
  }
}

// Complete event type that includes both known and unknown events
export type CompleteMqttEvent = MqttEvent | GenericMqttEvent

// Type guard functions for runtime type checking
export const isOnlineListEvent = (event: CompleteMqttEvent): event is OnlineListEvent => {
  return event.protocol === 65 && event.data.reqType === 'routerOnlineList'
}

export const isSignalQueryEvent = (event: CompleteMqttEvent): event is SignalQueryEvent => {
  return event.protocol === 23 && event.data.reqType === 'sigQry'
}

export const isRepeaterSourceEvent = (event: CompleteMqttEvent): event is RepeaterSourceEvent => {
  return event.protocol === 65 && event.data.reqType === 'repeaterSource'
}

export const isRepeaterHotspotEvent = (event: CompleteMqttEvent): event is RepeaterHotspotEvent => {
  return event.protocol === 65 && event.data.reqType === 'repeaterHotspot'
}

export const isRepeaterWifiListEvent = (event: CompleteMqttEvent): event is RepeaterWifiListEvent => {
  return event.protocol === 65 && event.data.reqType === 'repeaterWifiList'
}

export const getWifiSignalIcon = (signal: number) => {
  if (signal >= -30) {
    return Res.wifiGreat
  } else if (signal >= -50) {
    return Res.wifiGood
  } else if (signal >= -70) {
    return Res.wifiLow
  } else if (signal >= -80) {
    return Res.wifiWeak
  } else {
    return Res.wifiPoor
  }
}