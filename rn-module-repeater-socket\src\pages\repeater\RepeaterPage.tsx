import { useDeviceInfo } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, { useEffect, useMemo, useRef } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import Res from '@res'
import { Toast, TYSdk, Utils } from 'tuya-panel-kit'
import { Image, NativeModules, ScrollView, StyleSheet, Text, View } from "react-native"
import { useNavigation } from '@react-navigation/native'
import { useReactive } from 'ahooks'
import Cell from '@ledvance/base/src/components/Cell'
import { NativeApi } from '@ledvance/base/src/api/native'
import { CompleteMqttEvent, getWifiSignalIcon, HotspotConfig, isOnlineListEvent, isRepeaterHotspotEvent, isRepeaterSourceEvent, isRepeaterWifiListEvent, isSignalQueryEvent, MacInfo, RouterInfo } from './RepeaterActions'
import BottomTabNavigator from '@ledvance/base/src/components/BottomTabNavigator'
import Spacer from '@ledvance/base/src/components/Spacer'
import InfoText from '@ledvance/base/src/components/InfoText'
import I18n from '@ledvance/base/src/i18n'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import { RouterKey } from 'navigation/Router'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import { isSupportFunctions } from '@ledvance/base/src/utils/common'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

const routeManager = NativeModules.TYRCTRouteGatewayManager

interface RepeaterProps {
  theme?: ThemeType
}

const RepeaterPage = (props: RepeaterProps) => {
  const params = useParams<{ activeTab?: string }>()
  const devInfo = useDeviceInfo()
  const navigation = useNavigation()
  const isMountedRef = useRef(true)

  const state = useReactive({
    deviceLoading: true,
    activeTab: params.activeTab || 'device',
    onlineDevices: [] as MacInfo[],
    showPassword: false,
    routerInfo: {} as RouterInfo,
    hotspotConfig: {} as HotspotConfig,
    signal: -100,
  })

  const isSupportInternetAccess = useMemo(() => {
    return isSupportFunctions('internet_access')
  }, [])

  useEffect(() => {
    isMountedRef.current = true

    TYSdk.native.receiverMqttData(65)
    TYSdk.native.receiverMqttData(23)
    if (state.activeTab === 'device') {
      routeManager.queryRouteDeviceList("routerOnlineList")
    } else {
      routeManager.repeaterSource({ reqType: "repeaterSource" })
      routeManager.repeaterHotspot({ reqType: "repeaterHotspot" })
      TYSdk.native.sendMqttData(22, { "reqType": "sigQry" })
    }

    const handleMqttData = (event: CompleteMqttEvent) => {
      if (!isMountedRef.current) return // Prevent state updates if unmounted

      console.log("receiveMqttData event===========>", event);
      NativeApi.log(`receiveMqttData event===========>${JSON.stringify(event)}`)

      if (isSignalQueryEvent(event)) {
        state.signal = event.data.data.signal;
      } else if (isRepeaterSourceEvent(event)) {
        state.routerInfo = event.data.router
      } else if (isRepeaterHotspotEvent(event)) {
        state.hotspotConfig = event.data.hotspot.length > 0 ? event.data.hotspot[0] : {} as HotspotConfig
      } else if (isOnlineListEvent(event)) {
        state.deviceLoading = false
        state.onlineDevices = event.data.macList || []
      }
    }

    TYSdk.DeviceEventEmitter.addListener('receiveMqttData', handleMqttData)

    return () => {
      isMountedRef.current = false
      TYSdk.DeviceEventEmitter.removeListener('receiveMqttData', handleMqttData);
    }
  }, [])

  const renderDevicePage = () => {
    return (
      <View style={styles.pageContent}>
        <ScrollView nestedScrollEnabled={true}>
          {
            state.onlineDevices.length === 0 ? (
              <View style={{ flex: 1, marginTop: cx(150), alignItems: 'center', marginHorizontal: cx(24) }}>
                <Image
                  style={{ width: cx(225), height: cx(198) }}
                  source={Res.noDevice}
                  resizeMode="contain" />
                <InfoText
                  icon={res.device_panel_schedule_alert}
                  text={I18n.getLang('repeater_no_device')}
                  style={{ width: 'auto', alignItems: 'center' }}
                  textStyle={{ color: props.theme?.global.secondFontColor, fontSize: cx(12), flex: undefined }}
                  iconStyle={{ width: cx(16), height: cx(16), tintColor: props.theme?.global.secondFontColor }}
                />
              </View>
            ) : (
              state.onlineDevices.map((item, index) => (
                <View key={index}>
                  <Cell
                    key={index}
                    style={{ height: cx(60) }}
                    icon={Res.otherDevice}
                    iconStyle={{ width: cx(50), height: cx(50) }}
                    title={item.name}
                    value={''}
                    onPress={() => { 
                      if (isSupportInternetAccess) { 
                        navigation.navigate(RouterKey.internetAccess, { mac: item.mac.replace(/:/g, '') })
                      }
                    }}
                    hideArrow={!isSupportInternetAccess}
                  />
                  <Spacer height={1} style={{ backgroundColor: '#E5E5E5' }} />
                </View>
              ))
            )
          }
        </ScrollView>
        <Toast.Loading style={{ zIndex: 999, elevation: 10 }} show={state.deviceLoading} onFinish={() => { }} />
      </View>
    )
  }

  const renderSetting = () => {
    return (
      <View style={styles.pageContent}>
        <Spacer />
        <Card
          style={styles.card}
          onPress={() => {
            if (!state.routerInfo.ssid) return
            navigation.navigate(RouterKey.selectRouter, { routerInfo: state.routerInfo, signal: state.signal })
          }}
        >
          <Text style={styles.title}>{I18n.getLang('repeater_router_setting')}</Text>
          <View style={styles.wifiSetting}>
            <Image source={Res.router} style={styles.icon} />
            <View style={styles.wifiInfo}>
              <Text style={styles.wifiName}>{state.routerInfo.ssid}</Text>
              <Text style={styles.password}>{`${I18n.getLang('login_textfield_headline_pw')}: ${state.showPassword ? (state.routerInfo.pwd || '') : '******'}`}</Text>
            </View>
            <Image source={getWifiSignalIcon(state.signal)} style={{ tintColor: props.theme?.icon.normal }} />
          </View>
          <Spacer />
        </Card>
        <Spacer />
        <Card
          style={styles.card}
          onPress={() => {
            if (!state.routerInfo.ssid || !state.hotspotConfig.ssid) return
            navigation.navigate(RouterKey.repeaterSetting, { routerInfo: state.routerInfo, hotspotConfig: state.hotspotConfig })
          }}
        >
          <Text style={styles.title}>{I18n.getLang('repeater_repeater_setting')}</Text>
          <View style={styles.wifiSetting}>
            <Image source={Res.wifi24g} style={styles.icon} />
            <View style={styles.wifiInfo}>
              <Text style={styles.wifiName}>{state.hotspotConfig.ssid}</Text>
              <Text style={styles.password}>{`${I18n.getLang('login_textfield_headline_pw')}: ${state.showPassword ? (state.hotspotConfig.pwd || '') : '******'}`}</Text>
            </View>
          </View>
          <Spacer />
        </Card>
        <Spacer />
        <Card style={styles.card}>
          <LdvSwitch title={I18n.getLang('show_password')} enable={state.showPassword} setEnable={(value: boolean) => { state.showPassword = value }} />
        </Card>
      </View>
    )
  }

  const styles = StyleSheet.create({
    pageContent: {
      flex: 1,
    },
    card: {
      marginHorizontal: cx(24),
    },
    title: {
      fontSize: cx(16),
      color: props.theme?.global.fontColor,
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
      paddingVertical: cx(12),
      paddingStart: cx(12),
    },
    wifiSetting: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      paddingHorizontal: cx(12)
    },
    wifiInfo: {
      flex: 1,
      marginLeft: cx(12)
    },
    wifiName: {
      fontSize: cx(16),
      color: props.theme?.global.fontColor
    },
    password: {
      fontSize: cx(12),
      color: props.theme?.global.secondFontColor
    },
    icon: {
      width: cx(50),
      height: cx(50),
      tintColor: props.theme?.icon.normal
    }
  })

  return (
    <Page
      backText={devInfo.name}
    >
      <BottomTabNavigator
        tabs={[{
          key: 'device',
          title: I18n.getLang('manage_room_headline_devices'),
          icon: Res.devices,
        }, {
          key: 'setting',
          title: I18n.getLang('contact_sensor_specific_settings'),
          icon: Res.setting,
        }, ]}
        activeTabKey={state.activeTab}
        onTabChange={(tabKey) => {
          if (!isMountedRef.current) return

          state.activeTab = tabKey
          if (tabKey === 'device') {
            state.deviceLoading = true
            routeManager.queryRouteDeviceList("routerOnlineList")
          } else {
            state.deviceLoading = false
            routeManager.repeaterSource({ reqType: "repeaterSource" })
            routeManager.repeaterHotspot({ reqType: "repeaterHotspot" })
            TYSdk.native.sendMqttData(22, { "reqType": "sigQry" })
          }
        }}
      >
        {
          state.activeTab === 'device' ? renderDevicePage() : renderSetting()
        }
      </BottomTabNavigator>
    </Page>
  )
}

export default withTheme(RepeaterPage)
