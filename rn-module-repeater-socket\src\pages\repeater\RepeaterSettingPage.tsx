import React, { useEffect, useMemo, useRef } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { NativeModules, StyleSheet, Text, View } from "react-native"
import { TYSdk, Utils } from 'tuya-panel-kit'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import { CompleteMqttEvent, HotspotConfig, isRepeaterHotspotEvent, isRepeaterSourceEvent, RouterInfo } from './RepeaterActions'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import { useReactive } from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import TextField from '@ledvance/base/src/components/TextField'
import res from '@ledvance/base/src/res'
import { useNavigation } from '@react-navigation/native'
import { RouterKey } from 'navigation/Router'
import I18n from '@ledvance/base/src/i18n'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils
const routeManager = NativeModules.TYRCTRouteGatewayManager

interface RepeaterSettingProps {
  theme?: ThemeType
}

interface RepeaterSettingParams {
  isPasswordSet?: boolean
  routerInfo?: RouterInfo
  hotspotConfig?: HotspotConfig
}

const RepeaterSettingPage = (props: RepeaterSettingProps) => {
  const { routerInfo, hotspotConfig, isPasswordSet } = useParams<RepeaterSettingParams>()
  const navigation = useNavigation()
  const isMountedRef = useRef(true)
  const state = useReactive({
    copy: false,
    flag: false,
    isPasswordSet: isPasswordSet ?? true,
    routerInfo: {
      ssid: routerInfo?.ssid || '',
      pwd: routerInfo?.pwd || '',
    },
    hotspotConfig: {
      ssid: hotspotConfig?.ssid || '',
      pwd: hotspotConfig?.pwd || '',
      wifiType: 1,
    },
  })

  useEffect(() => {
    isMountedRef.current = true
    if (!isPasswordSet) {
      TYSdk.native.receiverMqttData(65)
      routeManager.repeaterSource({ reqType: "repeaterSource" })
      routeManager.repeaterHotspot({ reqType: "repeaterHotspot" })
    }

    const handleMqttData = (event: CompleteMqttEvent) => {
      if (!isMountedRef.current) return // Prevent state updates if unmounted

      if (isRepeaterSourceEvent(event)) {
        state.routerInfo = event.data.router
      } else if (isRepeaterHotspotEvent(event)) {
        if (state.isPasswordSet || state.flag) {
          return
        }
        const hotSpots = event.data.hotspot
        if (hotSpots.length > 0) {
          state.hotspotConfig = {
            ssid: hotSpots[0].ssid,
            pwd: hotSpots[0].pwd,
            wifiType: 1
          }
          state.flag = true
        }
      }
    }

    TYSdk.DeviceEventEmitter.addListener('receiveMqttData', handleMqttData)

    return () => {
      isMountedRef.current = false
      TYSdk.DeviceEventEmitter.removeListener('receiveMqttData', handleMqttData);
    }
  }, [])

  const canSave = useMemo(() => {
    return (state.hotspotConfig.ssid && state.hotspotConfig.pwd)
      && ((state.hotspotConfig.ssid !== hotspotConfig?.ssid || state.hotspotConfig.pwd !== hotspotConfig.pwd) || !state.isPasswordSet)
  }, [JSON.stringify(state.hotspotConfig)])

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
    },
    title: {
      paddingHorizontal: cx(24),
      paddingVertical: cx(8),
      color: props.theme?.global.secondFontColor
    },
  })

  return (
    <Page
      backText={I18n.getLang('repeater_repeater_setting')}
      rightButtonIcon={canSave ? res.ic_check : res.ic_uncheck}
      rightButtonIconClick={async () => {
        if (!canSave) {
          return
        }
        NativeModules.TYRCTRouteGatewayManager.repeaterHotspot({
          reqType: 'repeaterHotspot',
          hotspot: [state.hotspotConfig],
        })
        navigation.navigate(RouterKey.repeaterTiming, { source: 'hotspot' })
      }}
    >
      <View>
        <Text style={styles.title}>{I18n.getLang('repeater_quickly_config')}</Text>
        <Card style={styles.card}>
          <LdvSwitch
            title={I18n.getLang('repeater_copy_router_info')}
            enable={state.copy}
            setEnable={(value: boolean) => {
              state.copy = value
              if (value) {
                state.hotspotConfig = {
                  ...state.hotspotConfig,
                  ssid: `${state.routerInfo.ssid}_Ext_2.4G`,
                  pwd: state.routerInfo.pwd,
                }
              }
            }} />
        </Card>
        <Spacer />
        <Text style={styles.title}>{I18n.getLang('repeater_network_2_4g')}</Text>
        <Card style={[styles.card, { padding: cx(5) }]}>
          <TextField
            placeholder={I18n.getLang('repeater_ssid')}
            value={state.hotspotConfig.ssid}
            onChangeText={(t: string) => { state.hotspotConfig = { ...state.hotspotConfig, ssid: t } }}
            showError={state.hotspotConfig?.ssid?.length === 0}
            errorText={I18n.getLang('repeater_ssid_required')}
          />
          <TextField
            placeholder={I18n.getLang('login_textfield_headline_pw')}
            value={state.hotspotConfig.pwd}
            onChangeText={(t: string) => { state.hotspotConfig = { ...state.hotspotConfig, pwd: t } }}
            showError={state.hotspotConfig?.pwd?.length === 0}
            errorText={I18n.getLang('password_required')}
          />
        </Card>
      </View>
    </Page>
  )
}

export default withTheme(RepeaterSettingPage)
