import React, { useEffect, useRef } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { StyleSheet, Text, View, Image, NativeModules } from "react-native"
import { TYSdk, Utils } from 'tuya-panel-kit'
import { useDeviceInfo } from '@ledvance/base/src/models/modules/NativePropsSlice'
import Res from '@res'
import I18n from '@ledvance/base/src/i18n'
import { CompleteMqttEvent, isRepeaterHotspotEvent, isRepeaterSourceEvent } from './RepeaterActions'
import { useNavigation } from '@react-navigation/native'
import { RouterKey } from 'navigation/Router'
import { useParams } from '@ledvance/base/src/hooks/Hooks'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

const routeManager = NativeModules.TYRCTRouteGatewayManager

interface RepeaterTimingProps {
  theme?: ThemeType
}

const RepeaterTimingPage = (props: RepeaterTimingProps) => {
  const { source } = useParams<{ source: 'router' | 'hotspot' }>()
  const deviceInfo = useDeviceInfo()
  const navigation = useNavigation()
  const isMountedRef = useRef(true)

  useEffect(() => {
    isMountedRef.current = true

    TYSdk.native.receiverMqttData(65)
    routeManager.repeaterSource({ reqType: "repeaterSource" })
    routeManager.repeaterHotspot({ reqType: "repeaterHotspot" })

    const handleMqttData = (event: CompleteMqttEvent) => {
      if (!isMountedRef.current) return // Prevent state updates if unmounted
      
      const shouldNavigate = (source === 'router' && isRepeaterSourceEvent(event)) || 
                            (source === 'hotspot' && isRepeaterHotspotEvent(event))
      
      if (shouldNavigate) {
        navigation.reset({ 
          index: 1, 
          routes: [
            { name: RouterKey.main }, 
            { name: RouterKey.repeater, params: { activeTab: 'setting' } }
          ] 
        })
      }
    }

    TYSdk.DeviceEventEmitter.addListener('receiveMqttData', handleMqttData)

    return () => {
      isMountedRef.current = false
      TYSdk.DeviceEventEmitter.removeListener('receiveMqttData', handleMqttData);
    }
  }, [])

  const styles = StyleSheet.create({
    container: {
      marginTop: cx(200),
      justifyContent: 'center',
      alignItems: 'center'
    },
    img: {
      width: cx(100),
      height: cx(100)
    },
    title: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginVertical: cx(18),
    },
    content: {
      color: props.theme?.global.secondFontColor,
    },
  })

  return (
    <Page
      backText={deviceInfo.name}
      onBackClick={() => navigation.reset({ index: 0, routes: [{ name: RouterKey.main }] })}
    >
      <View style={styles.container}>
        <Image source={Res.timing} style={styles.img} />
        <Text style={styles.title}>{I18n.getLang('repeater_applying_title')}</Text>
        <View>
          <Text style={styles.content}>{I18n.getLang('repeater_applying_content_1')}</Text>
          <Text style={styles.content}>{I18n.getLang('repeater_applying_content_2')}</Text>
        </View>
      </View>
    </Page>
  )
}

export default withTheme(RepeaterTimingPage)
