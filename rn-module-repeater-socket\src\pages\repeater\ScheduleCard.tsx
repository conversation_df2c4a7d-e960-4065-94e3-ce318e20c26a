import React from "react";
import { ViewStyle, View, Text, StyleSheet } from "react-native";
import Card from "@ledvance/base/src/components/Card";
import { SwitchButton, Utils } from 'tuya-panel-kit';
import { convertTo12HourFormat, loopText } from '@ledvance/base/src/utils/common';
import { useSystemTimeFormate } from "@ledvance/base/src/models/modules/NativePropsSlice";
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface ScheduleCardProps {
  theme?: ThemeType
  item: any
  style?: ViewStyle
  onEnableChange: (enable: boolean) => void
  onPress: () => void
  onLongPress: () => void
}

const ScheduleCard = (props: ScheduleCardProps) => {
  const { item, style, onEnableChange, onPress, onLongPress } = props;
  const is24HourClock = useSystemTimeFormate()

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
      borderRadius: cx(8),
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    infoContainer: {
      flex: 1,
      marginTop: cx(16),
      marginBottom: cx(16),
      flexDirection: 'column',
      marginLeft: cx(16),
    },
    time: {
      color: props.theme?.global.fontColor,
      marginBottom: cx(5),
      fontSize: 16,
      fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    loop: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginTop: cx(8),
    },
    name: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
      marginTop: cx(8),
    },
    switchContainer: {
      marginRight: cx(16),
      marginTop: cx(16),
    },
  })

  return (
    <Card
      style={styles.card}
      containerStyle={[styles.container, style]}
      onPress={onPress}
      onLongPress={onLongPress}>
      <View style={styles.infoContainer}>
        <Text style={styles.time}>{is24HourClock ? `${item.startTime} - ${item.endTime}` : `${convertTo12HourFormat(item.startTime)} - ${convertTo12HourFormat(item.endTime)}`}</Text>
        <Text style={styles.loop}>
          {loopText(item.loops.split('').map(loop => parseInt(loop)), item.endTime)}
        </Text>
        <Text style={styles.name}>{item.aliasName}</Text>
      </View>
      <View style={styles.switchContainer}>
        <SwitchButton
          value={!!item.status}
          thumbStyle={{ elevation: 0 }}
          onValueChange={() => {
            onEnableChange(!item.status);
          }}
        />
      </View>
    </Card>
  )

}

export default withTheme(ScheduleCard)
