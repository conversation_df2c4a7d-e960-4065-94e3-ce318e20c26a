import React, { useEffect, useMemo } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { ScrollView, StyleSheet, Text, View } from "react-native"
import { TimerPicker, Utils } from 'tuya-panel-kit'
import LdvTopName from '@ledvance/base/src/components/ldvTopName'
import I18n from '@ledvance/base/src/i18n'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import TextField from '@ledvance/base/src/components/TextField'
import Spacer from '@ledvance/base/src/components/Spacer'
import { useReactive } from 'ahooks'
import { useSystemTimeFormate } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { loopText, showDialog } from '@ledvance/base/src/utils/common'
import LdvWeekView from "@ledvance/base/src/components/weekSelect"
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import res from '@ledvance/base/src/res'
import { useNavigation } from '@react-navigation/native'
import DeleteButton from '@ledvance/base/src/components/DeleteButton'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils
const { parseTimer, stringToSecond } = Utils.TimeUtils

interface ScheduleDetailProps {
  theme?: ThemeType
}

interface ScheduleParams {
  mode: 'add' | 'edit' | 'delete'
  onPost: (mode: 'add' | 'edit' | 'delete', item: any) => void
  item: any
}

const ScheduleDetailPage = (props: ScheduleDetailProps) => {
  const params = useParams<ScheduleParams>()
  const is24Hour = useSystemTimeFormate()
  const navigation = useNavigation()
  const defItem = useMemo(() => {
    const current = dayjs()
    return {
      aliasName: I18n.getLang('timeschedule_add_schedule_system_back_text'),
      startTime: current.format('HH:mm'),
      endTime: current.add(30, 'minute').format('HH:mm'),
      loops: '0000000',
    }
  }, [])

  const state = useReactive({
    item: cloneDeep(params.mode === 'add' ? defItem : params.item),
  })

  const canSave = useMemo(() => {
    return state.item.aliasName && state.item.startTime && state.item.endTime && state.item.loops
  }, [JSON.stringify(state.item)])

  const styles = StyleSheet.create({
    cardContainer: {
      marginHorizontal: cx(24)
    },
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
  })

  return (
    <Page
      backText={I18n.getLang('motion_detection_add_time_schedule_system_back_text')}
      rightButtonIcon={canSave ? res.ic_check : res.ic_uncheck}
      rightButtonIconClick={() => {
        if (canSave) {
          params.onPost(params.mode, state.item)
        }
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <LdvTopName title={I18n.getLang(params.mode === 'add' ? 'motion_detection_add_time_schedule_headline_text' : 'edit_timeschedule_headline_text')} />
        <TextField
          style={styles.cardContainer}
          value={state.item.aliasName}
          showError={(state.item.aliasName?.length || 0) > 32}
          maxLength={33}
          errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}
          placeholder={I18n.getLang('add_new_trigger_time_inputfield_value_text')}
          onChangeText={(t: string) => {
            state.item.aliasName = t;
          }}
        />
        {/* pick */}
        <TimerPicker
          key={props.theme?.type}
          itemTextColor='#aeadb5'
          style={{ paddingVertical: cx(0), marginVertical: cx(0), backgroundColor: props.theme?.global.background }}
          pickerFontColor={props.theme?.global.fontColor}
          is12Hours={!is24Hour}
          startTime={stringToSecond(state.item.startTime)}
          endTime={stringToSecond(state.item.endTime)}
          onTimerChange={(startTime, endTime) => {
            state.item.startTime = parseTimer(startTime * 60)
            state.item.endTime = parseTimer(endTime * 60)
          }} />
        <LdvWeekView
          value={state.item.loops.split('').map(Number)}
          style={styles.cardContainer}
          onSelect={(index: number) => {
            const rawIndex = index - 1;
            const weeks = state.item.loops.split('');
            weeks[rawIndex] = weeks[rawIndex] === '1' ? '0' : '1';
            state.item.loops = weeks.join('');
          }}
        />
        <Spacer />
        <Text style={{ ...styles.cardContainer, color: props.theme?.global.fontColor, fontSize: cx(14) }}>
          {loopText(state.item.loops.split('').map(loop => parseInt(loop)), state.item.endTime)}
        </Text>
        <Spacer />
        {params.mode === 'edit' && (
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              text={I18n.getLang('edit_timeschedule_bttn_text')}
              onPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('cancel_dialog_delete_item_timeschedule_titel'),
                  subTitle: I18n.getLang('cancel_dialog_delete_item_timeschedule_description'),
                  onConfirm: async (_, { close }) => {
                    // state.loading = true;
                    close();
                    params.onPost('delete', state.item)
                    // const res = await params.modDeleteTimeSchedule('del', { ...state.timeSchedule, status: 2 }, state.timeSchedule.id.toString());
                    // state.loading = false;
                    // if (res.success) {
                    //   params.refreshFn()
                    //   navigation.goBack();
                    // }
                  },
                });
              }}
            />
            <Spacer />
          </View>
        )}
      </ScrollView>
    </Page>
  )
}

export default withTheme(ScheduleDetailPage)
