import React, { useEffect, useRef } from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import { StyleSheet, Text, View, Image, ScrollView, NativeModules, TouchableOpacity } from "react-native"
import { TYSdk, Utils } from 'tuya-panel-kit'
import { useReactive } from 'ahooks'
import { CompleteMqttEvent, getWifiSignalIcon, isRepeaterWifiListEvent, isSignalQueryEvent, RouterInfo, WifiListItem } from './RepeaterActions'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import res from '@ledvance/base/src/res'
import Res from '@res';
import Spacer from '@ledvance/base/src/components/Spacer'
import { useNavigation } from '@react-navigation/native'
import { RouterKey } from 'navigation/Router'
import I18n from '@ledvance/base/src/i18n'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface SelectRouterProps {
  theme?: ThemeType
}

interface SelectRouterParams {
  routerInfo: RouterInfo
  signal: number
}

const SelectRouterPage = (props: SelectRouterProps) => {
  const params = useParams<SelectRouterParams>()
  const navigation = useNavigation()
  const isMountedRef = useRef(true)

  const state = useReactive({
    loading: true,
    wifiList: [] as WifiListItem[],
    signal: params.signal || -100,
  })

  useEffect(() => {
    isMountedRef.current = true
    TYSdk.native.receiverMqttData(65)
    NativeModules.TYRCTRouteGatewayManager.repeaterWifiList("repeaterWifiList")
    TYSdk.native.sendMqttData(22, { "reqType": "sigQry" })
    
    const handleMqttData = (event: CompleteMqttEvent) => {
      if (!isMountedRef.current) return
      
      if (isRepeaterWifiListEvent(event)) {
        state.loading = false
        state.wifiList = event.data.wifiList
      } else if (isSignalQueryEvent(event)) {
        state.signal = event.data.data.signal;
      }
    }

    TYSdk.DeviceEventEmitter.addListener('receiveMqttData', handleMqttData)
    
    return () => {
      isMountedRef.current = false
      TYSdk.DeviceEventEmitter.removeListener('receiveMqttData', handleMqttData);
    }
  }, [])

  const onChangeWifi = (item: WifiListItem) => {
    navigation.navigate(RouterKey.confirmPassword, { ssid: item.name, encrypted: item.encrypted === "1" })
  }

  const styles = StyleSheet.create({
    title: {
      paddingHorizontal: cx(12),
      paddingVertical: cx(8),
      color: props.theme?.global.secondFontColor
    },
    wifiItem: {
      height: cx(50),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: cx(12),
      backgroundColor: props.theme?.card.head,
    },
    tips: {
      paddingHorizontal: cx(12),
      color: props.theme?.global.error
    }
  })

  return (
    <Page
      backText={I18n.getLang('repeater_select_router')}
      rightButtonIcon={res.ic_refresh}
      rightButtonStyle={{ width: cx(24), height: cx(24), marginTop: cx(10), marginLeft: cx(10), tintColor: props.theme?.icon.primary }}
      rightButtonIconClick={() => {
        state.loading = true
        NativeModules.TYRCTRouteGatewayManager.repeaterWifiList("repeaterWifiList")
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Text style={styles.title}>{I18n.getLang('repeater_current_router')}</Text>
        <View style={styles.wifiItem}>
          <Image source={getWifiSignalIcon(state.signal)} style={{ width: cx(32), height: cx(32), tintColor: props.theme?.icon.normal }} />
          <Text style={{ flex: 1, marginLeft: cx(10), color: props.theme?.global.fontColor, fontSize: cx(18) }}>{params.routerInfo.ssid}</Text>
          <Image source={{ uri: res.ic_check }} style={{ width: cx(50), height: cx(50), tintColor: props.theme?.icon.primary }} />
        </View>
        <Spacer />
        <Text style={styles.tips}>{I18n.getLang('repeater_change_router_tips')}</Text>
        <Spacer />
        <Text style={styles.title}>{I18n.getLang('repeater_select_other_router')}</Text>
        {
          state.wifiList.map((item, index) => (
            <TouchableOpacity key={index} onPress={() => onChangeWifi(item)}>
              <View style={styles.wifiItem}>
                <Text style={{ flex: 1, color: props.theme?.global.fontColor, fontSize: cx(18) }}>{item.name}</Text>
                {item.encrypted === '1' && <Image source={Res.lock} style={{ width: cx(16), height: cx(22), tintColor: props.theme?.icon.normal, marginRight: cx(10) }} />}
                <Image source={getWifiSignalIcon(item.signalStrength)} style={{ width: cx(32), height: cx(32), tintColor: props.theme?.icon.normal }} />
              </View>
              <Spacer height={1} style={{ backgroundColor: props.theme?.global.background }} />
            </TouchableOpacity>
          ))
        }
      </ScrollView>
    </Page>
  )
}

export default withTheme(SelectRouterPage)
