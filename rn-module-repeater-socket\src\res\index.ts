export default {
  wifiPoor: require('./wifi-00.png'),
  wifiWeak: require('./wifi-01.png'),
  wifiLow: require('./wifi-02.png'),
  wifiGood: require('./wifi-03.png'),
  wifiGreat: require('./wifi-04.png'),
  devices: require('./src_res_devices.png'),
  setting: require('./src_res_setting.png'),
  otherDevice: require('./src_res_otherdevice.png'),
  noDevice: require('./src_res_nonedevice.png'),
  router: require('./src_res_wifi_router.png'),
  wifi24g: require('./src_res_wifi_24g.png'),
  lock: require('./src_res_lock.png'),
  timing: require('./src_res_timing.png'),
}
