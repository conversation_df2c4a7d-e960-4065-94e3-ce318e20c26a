import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard'
import { cloneDeep } from 'lodash';
import I18n from '@ledvance/base/src/i18n';
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { RouterKey } from 'navigation/Router';
import { useReactive, useUpdateEffect } from 'ahooks';
import { NativeApi } from '@ledvance/base/src/api/native';
import { useCallback, useEffect } from 'react';
import { useDeviceId, useDp, useTimeSchedule } from '@ledvance/base/src/models/modules/NativePropsSlice';
import { getGlobalParamsDp, isSupportFunctions } from '@ledvance/base/src/utils/common';
import { DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { ApplyForItem } from '@ledvance/base/src/utils/interface'

type ControlType = 'open' | 'close' | 'stop' | 'continue'

export const useControlType = () => {
  return useDp<ControlType, any>(getGlobalParamsDp('control'))
}

export const usePercentControl = () => {
  return useDp<number, any>(getGlobalParamsDp('percent_control'))
}

export function useAdvanceData(): AdvancedData[] {
  const advanceData = [] as AdvancedData[]
  const deviceId = useDeviceId();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const state = useReactive({
    timeScheduleStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          console.log('timerList', res.value)
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const shutterApplyFor: ApplyForItem[] = [
    {
      type: 'shutter',
      key: 'Curtain Control',
      name: 'Curtain',
      dp: getGlobalParamsDp('percent_control'),
      enable: true,
    },
  ];

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      deviceData: {
        type: DeviceType.Shutter,
        // @ts-ignore
        deviceData: {
          percentControl: 100,
        },
      },
      isManual: true,
      mood: undefined,
    };
    if (isSupportFunctions('percent_control')) {
      // @ts-ignore
      deviceState.deviceData.deviceData.percentControl = dps[getGlobalParamsDp('percent_control')] || 0
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType) => {
      const { deviceData } = deviceState;
      const manualDps = {};
      manualDps[getGlobalParamsDp('percent_control')] = (deviceData.deviceData as any).percentControl
      return manualDps;
    },
    []
  );


  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(shutterApplyFor),
        applyForDisabled: true,
        isShutter: true,
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: false,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    }
  })

  if (isSupportFunctions('cur_calibration') || isSupportFunctions('control_back') || isSupportFunctions('quick_calibration_1')) {
    advanceData.push({
      title: I18n.getLang('contact_sensor_specific_settings'),
      dp: { key: '', code: 'setting' },
      router: {
        key: RouterKey.settings,
      },
    })
  }

  return advanceData;
}
