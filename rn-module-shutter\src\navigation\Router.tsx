import { NavigationRoute } from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import SettingsPage from '../pages/settings/SettingPage'
import MotorSteeringPage from '../pages/settings/MotorSteeringPage'
import CalibrationPage from '../pages/settings/CalibrationPage'

export const RouterKey = {
    main: 'main',
    settings: 'settings',
    motor_steering: 'motor_steering',
    calibration: 'calibration',
    ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.settings,
        component: SettingsPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.motor_steering,
        component: MotorSteeringPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
        },
    {
        name: RouterKey.calibration,
        component: CalibrationPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    ...TimeSchedulePageRouters,
]