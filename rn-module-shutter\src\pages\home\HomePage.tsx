import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Utils } from 'tuya-panel-kit'
import { NativeApi } from '@ledvance/base/src/api/native'
import { ScrollView, View, StyleSheet } from "react-native"
import Spacer from '@ledvance/base/src/components/Spacer'
import Card from '@ledvance/base/src/components/Card'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import { useAdvanceData, useControlType, usePercentControl } from 'features/FeatureHooks'
import LdvSlider from '@ledvance/base/src/components/ldvSlider'
import ShutterControlView from './ShutterControlView'
import { useReactive, useUpdateEffect } from 'ahooks'
import I18n from '@ledvance/base/src/i18n'
const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const HomePage = (props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const advanceData = useAdvanceData()
  const [controlType, setControlType] = useControlType()
  const [percentControl, setPercentControl] = usePercentControl()
  const state = useReactive({
    percent: percentControl,
    controlType: controlType,
  })

  useUpdateEffect(() => {
    state.percent = percentControl
    state.controlType = controlType
  }, [percentControl, controlType])

  const styles = StyleSheet.create({
    content: {
      flex: 1,
    },
    shutterCard: {
      marginHorizontal: cx(24),
    },
    curtainCard: {
      marginHorizontal: cx(24),
      paddingVertical: cx(16),
    },
    shutterCardTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    shutterControlContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  })

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={styles.content}>
          <Spacer />
          <View style={styles.shutterCard}>
            <ShutterControlView 
              controlType={state.controlType}
              onControlTypeChange={(controlType) => {
                const cannotChange = (state.percent === 0 && controlType === 'close') || (state.percent === 100 && controlType === 'open')
                if (cannotChange) return
                state.controlType = controlType
                setControlType(controlType).then()
              }}
            />
          </View>
          <Spacer height={cx(16)} />
          <Card style={styles.curtainCard}>
            <LdvSlider
              title={I18n.getLang('curtain_control_headline_text')}
              value={state.percent}
              min={0}
              max={100}
              onValueChange={(value) => {
                state.percent = value
              }}
              onSlidingComplete={async (value) => {
                await setPercentControl(value)
              }}
            />
          </Card>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
