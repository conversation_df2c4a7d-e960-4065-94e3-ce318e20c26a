import ThemeType from '@ledvance/base/src/config/themeType'
import React, { useState } from 'react'
import { Image, StyleProp, StyleSheet, TouchableOpacity, View, Text, ViewStyle } from 'react-native'
import { Utils } from 'tuya-panel-kit'
import res from '@ledvance/base/src/res'
import Spacer from '@ledvance/base/src/components/Spacer'
import Card from '@ledvance/base/src/components/Card'
import { useUpdateEffect } from 'ahooks'
import I18n from '@ledvance/base/src/i18n'
const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

type ControlType = 'open' | 'stop' | 'close' | 'continue'

interface ShutterControlProps {
  theme?: ThemeType
  style?: StyleProp<ViewStyle>
  controlType?: ControlType
  onControlTypeChange?: (controlType: ControlType) => void
}

const ShutterControlView = (props: ShutterControlProps) => {
  const [controlType, setControlType] = useState<ControlType>(props.controlType || 'close')

  useUpdateEffect(() => {
    setControlType(props.controlType || 'stop')
  }, [props.controlType])

  const handleControlTypeChange = (controlType: ControlType) => {
    setControlType(controlType)
    props.onControlTypeChange?.(controlType)
  }

  const styles = StyleSheet.create({
    root: {
      paddingHorizontal: cx(16),
    },
    shutterControlContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    shutterCardTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    controlContainer: {
      marginStart: cx(10),
      width: cx(80),
      height: cx(200),
      padding: cx(5), 
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: props.theme?.container.background, 
      borderRadius: cx(40), 
    },
    innerContainer: {
      width: '100%',
      height: '100%',
      backgroundColor: props.theme?.card.background,
      borderRadius: cx(35), 
      borderWidth: cx(3),
      borderColor: props.theme?.card.border, 
      overflow: 'hidden', 
    },
    controlIconContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    line: {
      width: '100%',
      height: cx(3),
      backgroundColor: props.theme?.card.border,
    },
    shutterIcon: {
      width: '60%',
      height: '60%'
    }
  })
  return (
    <Card style={[styles.root, props.style]}>
      <Spacer width={cx(16)} />
      <Text style={styles.shutterCardTitle}>{I18n.getLang('curtain_control_title')}</Text>
      <Spacer />
      <View style={styles.shutterControlContainer}>
        <View style={styles.controlContainer}>
          <View style={styles.innerContainer}>
            <TouchableOpacity style={styles.controlIconContainer} onPress={() => handleControlTypeChange('open')}>
              <Image source={{ uri: res.shutter_open }} resizeMode='contain' style={[styles.shutterIcon, { tintColor: controlType === 'open' ? props.theme?.icon.primary : props.theme?.icon.normal }]} />
            </TouchableOpacity>
            <View style={styles.line} />
            <TouchableOpacity style={styles.controlIconContainer} onPress={() => handleControlTypeChange('stop')}>
              <Image source={{ uri: res.shutter_stop }} resizeMode='contain' style={[styles.shutterIcon, { tintColor: props.theme?.icon.normal }]} />
            </TouchableOpacity>
            <View style={styles.line} />
            <TouchableOpacity style={styles.controlIconContainer} onPress={() => handleControlTypeChange('close')}>
              <Image source={{ uri: res.shutter_close }} resizeMode='contain' style={[styles.shutterIcon, { tintColor: controlType === 'close' ? props.theme?.icon.primary : props.theme?.icon.normal }]} />
            </TouchableOpacity>
          </View>
        </View>
        <Spacer />
      </View>
    </Card>
  )
}

export default withTheme(ShutterControlView)