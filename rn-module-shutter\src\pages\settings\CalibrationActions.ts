import I18n from "@ledvance/base/src/i18n"
import { useDp, useInitialDp } from "@ledvance/base/src/models/modules/NativePropsSlice"
import { getGlobalParamsDp } from "@ledvance/base/src/utils/common"

export interface CalibrationParams {
  mode: 'intelligent' | 'fast'
}

type CurCalibrationType = 'start' | 'end'
export const useCurCalibration = () => {
  const initialDp = useInitialDp<CurCalibrationType>(getGlobalParamsDp('cur_calibration'))
  const [curCalibration, setCurCalibration] = useDp<CurCalibrationType, any>(getGlobalParamsDp('cur_calibration'))
  return [curCalibration, setCurCalibration, initialDp] as const
}

export const useQuickCalibration = () => {
  const initialDp = useInitialDp<number>(getGlobalParamsDp('quick_calibration_1'))
  const [quickCalibration, setQuickCalibration] = useDp<number, any>(getGlobalParamsDp('quick_calibration_1'))
  return [quickCalibration, setQuickCalibration, initialDp] as const
}

export const IntelligentText = I18n.getLang('curtain_intelligent_calibration_step1')

export const IntelligentNextStepText = I18n.getLang('curtain_intelligent_calibration_step2')

export const FastText = I18n.getLang('curtain_fast_calibration_step1')

export const FastNextStepText = I18n.getLang('curtain_fast_calibration_step2')


