import React, { useEffect, useRef } from 'react'
import { ScrollView, StyleSheet, Text, TextInput, View, Image, TouchableOpacity } from 'react-native'
import Page from '@ledvance/base/src/components/Page'
import { useDeviceInfo } from '@ledvance/base/src/models/modules/NativePropsSlice'
import I18n from '@ledvance/base/src/i18n'
import { Slider, Utils } from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import { useReactive, useUpdateEffect } from 'ahooks'
import ThemeType from '@ledvance/base/src/config/themeType'
import { useParams } from '@ledvance/base/src/hooks/Hooks'
import { CalibrationParams, IntelligentText, FastText, IntelligentNextStepText, FastNextStepText } from './CalibrationActions'
import { useCurCalibration, useQuickCalibration } from './CalibrationActions'
import DeleteButton from '@ledvance/base/src/components/DeleteButton'
import Card from '@ledvance/base/src/components/Card'
import InfoText from '@ledvance/base/src/components/InfoText'
import res from '@ledvance/base/src/res'
import { useNavigation } from '@react-navigation/native'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils
const CalibrationPage = (props: { theme?: ThemeType }) => {
  const params = useParams<CalibrationParams>()
  const deviceInfo = useDeviceInfo()
  const [, setCurCalibration, initialCurCalibration] = useCurCalibration()
  const [, setQuickCalibration, initialQuickCalibration] = useQuickCalibration()
  const navigation = useNavigation()
  const timer1 = useRef<any>(null)
  const curCalibrationRef = useRef<any>(initialCurCalibration)
  const quickCalibrationRef = useRef<any>(initialQuickCalibration)

  const state = useReactive({
    step: 1,
    inputValue: initialQuickCalibration?.toString(),
    startCalibration: false,
    calibrationProgress: 0,
    calibrationSuccess: false,
    tryAgain: false,
  })

  useEffect(() => {
    return () => {
      if (timer1.current) clearInterval(timer1.current)
    }
  }, [])

  useUpdateEffect(() => {
    curCalibrationRef.current = initialCurCalibration
    quickCalibrationRef.current = initialQuickCalibration
  }, [initialCurCalibration, initialQuickCalibration])

  const handleInputChange = (text: string) => {
    // 只允许输入数字
    const numericValue = text.replace(/[^0-9]/g, '');
    if (numericValue === '') {
      state.inputValue = '';
      return;
    }
    const num = parseInt(numericValue, 10);
    if (Number.isInteger(num) && num >= 0 && num <= 900) {
      state.inputValue = numericValue;
    }
  };

  const setCalibrationFailed = () => {
    state.calibrationSuccess = false;
    state.calibrationProgress = 100;
    state.tryAgain = true;
    clearCalibrationTimer();
  };

  const clearCalibrationTimer = () => {
    if (timer1.current) {
      clearInterval(timer1.current);
      timer1.current = null;
    }
  };

  const startProgressAnimation = () => {
    const intervalTime = 1000; // 更新间隔(毫秒)

    let currentProgress = 0;
    timer1.current = setInterval(() => {
      currentProgress += 10;

      // 每次更新都检查校准是否已成功
      const isIntelligentMode = params.mode === 'intelligent';
      let calibrationReceived = false;

      // 根据不同模式，检查校准结果
      if (isIntelligentMode) {
        calibrationReceived = curCalibrationRef.current === 'end';
      } else {
        calibrationReceived = quickCalibrationRef.current === Number(state.inputValue);
      }

      // 如果收到成功响应，立即完成校准
      if (calibrationReceived) {
        currentProgress = 100;
        state.calibrationSuccess = true;
        state.calibrationProgress = 100;
        state.tryAgain = false;
        clearInterval(timer1.current);
        return;
      }

      // 如果达到100%但没有收到成功响应，则标记为失败
      if (currentProgress >= 100) {
        currentProgress = 100;
        clearInterval(timer1.current);
        state.calibrationSuccess = false;
        state.tryAgain = true;
      }

      state.calibrationProgress = Math.round(currentProgress);
    }, intervalTime);

  };

  const handleCalibration = async () => {
    state.startCalibration = true
    state.calibrationProgress = 0
    startProgressAnimation()
    try {
      const result = await (params.mode === 'fast'
        ? setQuickCalibration(Number(state.inputValue))
        : setCurCalibration('end'));

      if (!result.success) {
        setCalibrationFailed();
      }
    } catch (error) {
      setCalibrationFailed();
    }
  }

  const styles = StyleSheet.create({
    root: {
      flex: 1,
    },
    tipText: {
      marginHorizontal: cx(24),
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    deviceInfoView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    deviceName: {
      marginLeft: cx(12),
      fontSize: cx(14),
      color: props.theme?.global.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    progressTipText: {
      fontSize: cx(12),
      color: props.theme?.global.fontColor,
      fontFamily: 'helvetica_neue_lt_std_roman',
      marginTop: cx(-8),
      textAlign: 'center'
    }
  })

  return (
    <Page
      backText={I18n.getLang('contact_sensor_specific_settings')}
      headlineText={I18n.getLang(params.mode === 'intelligent' ? 'curtain_intelligent_calibration' : 'curtain_fast_calibration')}
      onBackClick={() => {
        if (params.mode === 'intelligent') {
          setCurCalibration('end').then()
        }
        navigation.goBack()
      }}
    >
      <ScrollView style={styles.root} nestedScrollEnabled={true}>
        <Spacer />
        {state.step === 1 && <>
          <Text style={styles.tipText}>{params.mode === 'intelligent' ? IntelligentText : FastText}</Text>
          <Spacer height={cx(40)} />
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              text={'Next Step'}
              style={{ backgroundColor: props.theme?.button.primary }}
              onPress={() => {
                state.step = 2
              }}
            />
          </View>
        </>}
        {state.step === 2 && <>
          <Text style={styles.tipText}>{params.mode === 'intelligent' ? IntelligentNextStepText : FastNextStepText}</Text>
          {params.mode === 'fast' && <>
            <Spacer />
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <TextInput
                value={state.inputValue}
                keyboardType='numeric'
                autoFocus={true}
                onChangeText={handleInputChange}
                style={{ marginHorizontal: cx(12), textAlign: 'center', fontSize: cx(14), width: cx(60), height: cx(40), backgroundColor: props.theme?.textInput.background }}
              />
              <Text style={{ fontSize: cx(14), fontWeight: 'bold', color: props.theme?.global.fontColor }}>S</Text>
            </View>
          </>}
          <Spacer height={cx(40)} />
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              text={params.mode === 'intelligent' ? I18n.getLang('curation_calibration_callibrate_btn_text') : I18n.getLang('curation_calibration_nextbtn_text')}
              disabled={params.mode === 'fast' && (!state.inputValue || state.inputValue === '0')}
              style={{ backgroundColor: (params.mode === 'fast' && (!state.inputValue || state.inputValue === '0')) ? props.theme?.button.disabled : props.theme?.button.primary }}
              onPress={async () => {
                handleCalibration()
                state.step = 3
              }}
            />
          </View>
        </>}
        {state.step === 3 && <>
          <Card style={{ marginHorizontal: cx(24), paddingHorizontal: cx(12) }}>
            <Spacer height={cx(12)} />
            <View style={styles.deviceInfoView}>
              {/* @ts-ignore */}
              <Image source={{ uri: deviceInfo?.icon }} style={{ width: cx(42), height: cx(42) }} />
              <Text style={styles.deviceName}>{deviceInfo?.name}</Text>
            </View>

            {/* 根据校准状态显示不同内容 */}
            {state.calibrationProgress !== 100 ? (
              // 校准进行中显示进度条
              <>
                <Slider.Horizontal
                  minimumValue={0}
                  maximumValue={100}
                  value={state.calibrationProgress}
                  disabled={true}
                  theme={{
                    trackRadius: 3,
                    trackHeight: 6,
                    thumbSize: 0,
                    minimumTrackTintColor: props.theme?.global.brand,
                    maximumTrackTintColor: props.theme?.global.disabledFontColor,
                  }}
                />
                <Text style={styles.progressTipText}>
                  {I18n.getLang('curtain_calibration_progress_text')}
                </Text>
              </>
            ) : (
              // 校准完成显示结果
              <InfoText
                icon={state.calibrationSuccess ? res.ic_checked : res.ic_info}
                iconStyle={{ tintColor: state.calibrationSuccess ? props.theme?.global.success : props.theme?.global.error }}
                textStyle={{ color: state.calibrationSuccess ? props.theme?.global.success : props.theme?.global.error }}
                text={state.calibrationSuccess
                  ? I18n.getLang('curtain_calibration_success_text')
                  : I18n.getLang('curtain_calibration_failed_text')}
              />
            )}

            <Spacer height={cx(12)} />
          </Card>
          <Spacer height={cx(40)} />
          {state.tryAgain && <View style={{ alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => {
                state.tryAgain = false
                handleCalibration()
              }}>
              <InfoText
                icon={res.ic_refresh}
                text={I18n.getLang('curtain_calibration_tryagin_text')}
                textStyle={{ flex: 0, color: props.theme?.global.brand, marginTop: 0 }}
                iconStyle={{ tintColor: props.theme?.global.brand }}
              />
            </TouchableOpacity>
          </View>}
        </>}
      </ScrollView>
    </Page>
  )
}

export default withTheme(CalibrationPage)
