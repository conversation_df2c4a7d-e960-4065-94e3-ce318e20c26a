import { useDp } from "@ledvance/base/src/models/modules/NativePropsSlice"
import { getGlobalParamsDp } from "@ledvance/base/src/utils/common"
import I18n from "@ledvance/base/src/i18n"
type MotorSteeringType = 'forward' | 'back'

export const MotorSteeringDp: Record<MotorSteeringType, string> = {
  forward: I18n.getLang('curtain_motor_steering1'),
  back: I18n.getLang('curtain_motor_steering2'),
}

export const useMotorSteering = () => {
  return useDp<MotorSteeringType, any>(getGlobalParamsDp('control_back'))
}

