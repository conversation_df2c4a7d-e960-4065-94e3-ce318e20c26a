import React, { PropsWithChildren } from 'react'
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View, ViewProps } from 'react-native'
import Page from '@ledvance/base/src/components/Page'
import I18n from '@ledvance/base/src/i18n'
import { Utils } from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import Card from '@ledvance/base/src/components/Card'
import res from '@ledvance/base/src/res'
import { useReactive, useUpdateEffect } from 'ahooks'
import ThemeType from '@ledvance/base/src/config/themeType'
import { useMotorSteering } from './MotorSteeringActions'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const RELAY_STATUS_FORWARD = 'forward'
const RELAY_STATUS_BACK = 'back'

const MotorSteeringPage = (props: { theme?: ThemeType }) => {
  const [motorSteering, setMotorSteering] = useMotorSteering()

  const state = useReactive({
    motorSteering,
    loading: false,
  })

  useUpdateEffect(() => {
    state.motorSteering = motorSteering
  }, [motorSteering])

  const styles = StyleSheet.create({
    root: {
      flex: 1,
    },
    tipText: {
      marginHorizontal: cx(24),
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    modeSelectGroup: {
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      borderRadius: cx(4),
    },
    modeTip: {
      marginHorizontal: cx(8),
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    modeSelectCard: {
      marginHorizontal: cx(8),
    },
    line: {
      height: cx(1),
      marginHorizontal: cx(12),
      backgroundColor: props.theme?.container.divider,
    },
    itemRoot: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: cx(12),
      paddingBottom: cx(8),
    },
    itemTextGroup: {
      flex: 1,
      marginEnd: cx(12),
      justifyContent: 'center',
    },
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    itemContent: {
      color: props.theme?.global.secondFontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    itemCheckedIcon: {
      
    },
  })

  return (
    <Page
      backText={I18n.getLang('contact_sensor_specific_settings')}
      headlineText={I18n.getLang('curtain_motor_steering')}
      loading={state.loading}>
      <ScrollView style={styles.root} nestedScrollEnabled={true}>
        <Spacer />
        <View style={styles.modeSelectGroup}>
          <Spacer height={cx(8)} />
          <Text style={styles.modeTip}>
            {I18n.getLang('curtain_motor_steering_tip')}
          </Text>
          <Spacer height={cx(8)} />
          <Card style={styles.modeSelectCard}>
            <Spacer height={cx(12)} />
            <MotorSteeringItem
              styles={styles}
              enable={state.motorSteering === RELAY_STATUS_FORWARD}
              title={I18n.getLang('curtain_motor_steering1')}
              content={I18n.getLang('curtain_motor_steering1_description')}
              onPress={async () => {
                state.motorSteering = RELAY_STATUS_FORWARD
                await setMotorSteering(state.motorSteering)
              }} />
            <View style={styles.line} />
            <Spacer height={cx(8)} />
            <MotorSteeringItem
              styles={styles}
              enable={state.motorSteering === RELAY_STATUS_BACK}
              title={I18n.getLang('curtain_motor_steering2')}
              content={I18n.getLang('curtain_motor_steering2_description')}
              onPress={async () => {
                state.motorSteering = RELAY_STATUS_BACK
                await setMotorSteering(state.motorSteering)
              }} />
          </Card>
          <Spacer height={cx(8)} />
        </View>
      </ScrollView>
    </Page>
  )
}

interface MotorSteeringItemProps extends PropsWithChildren<ViewProps> {
  onPress: () => void
  title: string
  content: string
  enable: boolean
  styles: StyleSheet.NamedStyles<any>
}

function MotorSteeringItem(props: MotorSteeringItemProps) {
  const { styles } = props
  return (
    <TouchableOpacity onPress={props.onPress}>
      <View style={styles.itemRoot}>
        <View style={styles.itemTextGroup}>
          <Text style={styles.itemTitle}>{props.title}</Text>
          <Spacer height={cx(4)} />
          <Text style={styles.itemContent}>{props.content}</Text>
        </View>
        <Image
          source={{ uri: res.ic_check }}
          style={{
            width: cx(32),
            height: cx(32),
            marginEnd: cx(4),
            display: props.enable ? 'flex' : 'none',
          }} />
      </View>
    </TouchableOpacity>
  )
}

export default withTheme(MotorSteeringPage)
