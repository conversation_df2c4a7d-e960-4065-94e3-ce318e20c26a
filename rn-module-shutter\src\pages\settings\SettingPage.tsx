import { View, Text, StyleSheet, TouchableOpacity, Image } from "react-native";
import React from "react";
import Page from "@ledvance/base/src/components/Page";
import { useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import { useReactive } from "ahooks";
import { Utils } from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import ThemeType from "@ledvance/base/src/config/themeType";
import { useControlType, usePercentControl } from "../../features/FeatureHooks";
import { RouterKey } from "navigation/Router";
import { useNavigation } from "@react-navigation/native";
import res from "@ledvance/base/src/res";
import { MotorSteeringDp, useMotorSteering } from "./MotorSteeringActions";
import { showDialog } from "@ledvance/base/src/utils/common";
import { useCurCalibration, useQuickCalibration } from "./CalibrationActions";
const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

const SettingsPage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo();
  const navigation = useNavigation()
  const [motorSteering] = useMotorSteering()
  const [controlType] = useControlType()
  const [percentControl] = usePercentControl()
  const [, setCurCalibration] = useCurCalibration()
  const [quickCalibration] = useQuickCalibration()
  const state = useReactive({
    switchTypeModal: false,
    loading: false,
  });

  const styles = StyleSheet.create({
    itemContainer: {
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      borderRadius: cx(6),
      padding: cx(10)
    },
    touchableItem: {
      paddingHorizontal: cx(10),
      backgroundColor: props.theme?.global.background,
      borderRadius: cx(6),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: cx(40)
    },
    text: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    }
  })

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('contact_sensor_specific_settings')}
      loading={state.loading}>
      <Spacer height={cx(16)} />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            navigation.navigate(RouterKey.motor_steering)
          }}
          disabled={controlType !== 'stop'}
        >
          <Text style={styles.text}>{I18n.getLang('curtain_motor_steering')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1, marginStart: cx(5) }}>
            <Text style={[styles.text, { flex: 1, textAlign: 'right' }]} numberOfLines={1}>{MotorSteeringDp[motorSteering]}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20), tintColor: props.theme?.card.fontColor }} />
          </View>
        </TouchableOpacity>
      </View>
      <Spacer />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            setCurCalibration('start').then()
            navigation.navigate(RouterKey.calibration, { mode: 'intelligent' })
          }}
          disabled={controlType !== 'stop'}
        >
          <Text style={styles.text}>{I18n.getLang('curtain_intelligent_calibration')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20), tintColor: props.theme?.card.fontColor }} />
          </View>
        </TouchableOpacity>
        <Spacer height={cx(10)} />
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            if(percentControl !== 0) {
              return showDialog({
                method: 'alert',
                title: I18n.getLang('curtain_fast_toast_text'),
                onConfirm: async (_, { close }) => {
                  close()
                }
              })
            }
            navigation.navigate(RouterKey.calibration, { mode: 'fast' })
          }}
          disabled={controlType !== 'stop'}
        >
          <Text style={styles.text}>{I18n.getLang('curtain_fast_calibration')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{quickCalibration} S</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20), tintColor: props.theme?.card.fontColor }} />
          </View>
        </TouchableOpacity>
      </View>
    </Page>
  )
}

export default withTheme(SettingsPage);