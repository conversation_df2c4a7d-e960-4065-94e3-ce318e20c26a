import React from 'react';
import Card from '@ledvance/base/src/components/Card';
import {Battery, Utils} from 'tuya-panel-kit';
import {View, Text, StyleSheet} from 'react-native';
import I18n from '@ledvance/base/src/i18n';
import Spacer from '@ledvance/base/src/components/Spacer';
import ThemeType from '@ledvance/base/src/config/themeType'
import {BatterState} from "../utils/BatterState";

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils
type BatteryProps = {
    theme?: ThemeType
    batteryState: string;
};

const batteryLevels = {
    [BatterState.High]: 100,
    [BatterState.Middle]: 60,
    [BatterState.Low]: 30,
};

const getBatteryDisplayName = (value: string): string => {
    switch (value) {
        case BatterState.High:
            return I18n.getLang('contact_sensor_battery_state1');
        case BatterState.Middle:
            return I18n.getLang('contact_sensor_battery_state2');
        case BatterState.Low:
        default:
            return I18n.getLang('contact_sensor_battery_state3');
    }
};

export default withTheme(function BatteryStateView(props: BatteryProps) {
    const {batteryState} = props;
    const calcColor = (_, highColor, middleColor, lowColor) => {
        switch (batteryState) {
            case BatterState.High:
                return highColor;
            case BatterState.Middle:
                return middleColor;
            default:
                return lowColor;
        }
    };

    const battery = batteryLevels[batteryState];

    const styles = StyleSheet.create({
        title: {
            marginStart: cx(16),
            marginVertical: cx(18),
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
            fontFamily: 'helvetica_neue_lt_std_bd',
        },
        batteryRotate: {
            transform: [{ rotate: '90deg' }],
            position: 'relative',
            right: cx(30)
        },
        content: {
            marginEnd: cx(16),
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
            fontFamily: 'helvetica_neue_lt_std_roman',
        }
    })

    return (
      <Card
        style={{ marginHorizontal: cx(24) }}
        containerStyle={{ flexDirection: 'row', alignItems: 'center' }}
      >
          <Text
            style={styles.title}>
              {I18n.getLang('motion_detector_battery__state4')}
          </Text>
          <Spacer height={0} style={{ flex: 1 }} />
          <View style={styles.batteryRotate}>
              <Battery
                value={battery}
                onCalcColor={calcColor}
                size={cx(30)}
                theme={{ batteryColor: props.theme?.global.secondFontColor || '#000' }}
                middleColor='#999999'
              />
          </View>
          <Text style={styles.content}>{getBatteryDisplayName(batteryState)}</Text>
      </Card>
    )
})
