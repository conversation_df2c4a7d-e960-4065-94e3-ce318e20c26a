import {View} from "react-native";
import React, {useEffect} from "react";
import I18n from "@ledvance/base/src/i18n/index";
import ItemView from "./ItemView";
import {useReactive} from "ahooks";
import {useDeviceMode} from "../hooks/DeviceHooks";
import {DeviceMode} from "../utils/DeviceMode";
import Spacer from "@ledvance/base/src/components/Spacer";

interface DeviceModelProps extends LoadingCallback {
}

export default function DeviceModeView(props: DeviceModelProps) {
    const {onLoading} = props;
    const [deviceMode, setDeviceMode] = useDeviceMode();
    const state = useReactive({
        isAutoMode: deviceMode === DeviceMode.AUTO,
    });
    useEffect(() => {
        state.isAutoMode = deviceMode === DeviceMode.AUTO;
    }, [deviceMode]);
    return (<View>
        <Spacer/>
        <ItemView
            title={I18n.getLang('device_menu_solar_lights_secondbox_text1')}
            description={I18n.getLang('device_menu_solar_lights_secondbox_note')}
            switchValue={state.isAutoMode}
            onSwitchChange={async (value: boolean) => {
                onLoading && onLoading(true);
                await setDeviceMode(value ? DeviceMode.AUTO : DeviceMode.MANUAL);
                onLoading && onLoading(false);
                state.isAutoMode = value;
            }}
        />
    </View>);
}