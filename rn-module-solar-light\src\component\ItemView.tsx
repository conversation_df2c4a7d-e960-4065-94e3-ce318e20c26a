import {Text, View, StyleSheet, ViewProps} from "react-native";
import React, {PropsWithChildren} from "react";
import Card from "@ledvance/base/src/components/Card";
import {IconFont, SwitchButton, Utils} from "tuya-panel-kit";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface ItemProps extends PropsWithChildren<ViewProps> {
    theme?: ThemeType
    title: string,
    description?: string,
    content?: string,
    switchValue?: boolean,
    onSwitchChange?: (value: boolean) => void,
    onItemPress?: () => void
}

export default withTheme(function ItemView(props: ItemProps) {
    const {title, description, content, switchValue, onSwitchChange, onItemPress} = props
    const styles = StyleSheet.create({
        titleLayout: {
            flexDirection: 'column',
            flex: 1,
            alignItems: 'flex-start',
            marginEnd: cx(24),
        },
        title: {
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
        },
        contentLayout: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        content: {
            fontSize: cx(14),
            color: props.theme?.global.secondFontColor,
            maxWidth: cx(80)
        },
    })

    return (
        <Card style={{marginHorizontal: cx(24)}}
              containerStyle={{flexDirection: 'column'}} onPress={onItemPress}>
            <View style={{flexDirection: 'row', alignItems: 'center', padding: cx(16)}}>
                <View style={styles.titleLayout}>
                    <Text style={styles.title}>{title}</Text>
                    {!!description && <Text style={{marginTop: cx(2), color: props.theme?.global.secondFontColor,}}>{description}</Text>}
                </View>
                {!!content ? (<View style={styles.contentLayout}>
                    <Text style={styles.content}>{content}</Text>
                    <IconFont name={'arrow'} color={props.theme?.global.secondFontColor} size={cx(11)}/>
                </View>) : (switchValue != null && onSwitchChange != null &&
                    <SwitchButton value={switchValue} onValueChange={onSwitchChange}/>)}
            </View>
            <View>{props.children}</View>
        </Card>)
})
