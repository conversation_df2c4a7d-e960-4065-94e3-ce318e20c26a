import React, {useCallback, useEffect} from 'react';
import {Utils} from 'tuya-panel-kit';
import {useReactive, useThrottleFn, useUpdateEffect} from 'ahooks';
import {
  isSupportBrightness,
  isSupportColour,
  isSupportTemperature,
  useBrightness,
  useColour,
  useSwitchLed,
  useTemperature,
  useWorkMode,
} from 'hooks/DeviceHooks';
import Card from '@ledvance/base/src/components/Card';
import LampAdjustView from '@ledvance/base/src/components/LampAdjustView';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import {WorkMode} from '@ledvance/base/src/utils/interface';
import I18n from '@ledvance/base/src/i18n';
import {View} from 'react-native';
import {cctToColor} from '@ledvance/base/src/utils/cctUtils';
import {hsv2Hex} from '@ledvance/base/src/utils';
import {mapFloatToRange} from '@ledvance/base/src/utils';
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'

const {convertX: cx} = Utils.RatioUtils;

interface LampControlProps extends LoadingCallback {
}

export default function LampControlView({onLoading}: LampControlProps) {
  const [switchLed, setSwitchLed] = useSwitchLed();
  const [workMode, setWorkMode] = useWorkMode();
  const [temperature, setTemperature] = useTemperature();
  const [brightness, setBrightness] = useBrightness();
  const [colour, setColour] = useColour();
  const {h, s, v} = colour;
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const state = useReactive({
    switchLed: switchLed,
    isColor: workMode !== WorkMode.White,
    workMode: workMode,
    h: h,
    s: s,
    v: v,
    temperature: Math.round(temperature / 10),
    brightness: Math.round(brightness / 10),
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useEffect(() => {
    state.switchLed = switchLed;
  }, [switchLed]);

  useEffect(() => {
    state.isColor = isSupportColour() && workMode !== WorkMode.White;
    state.workMode = workMode;
  }, [workMode]);

  useUpdateEffect(() => {
    state.h = h;
    state.s = s;
    state.v = v;
  }, [h, s, v]);

  useEffect(() => {
    state.temperature = Math.round(temperature / 10);
  }, [temperature]);

  useEffect(() => {
    state.brightness = Math.round(brightness / 10);
  }, [brightness]);

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = workMode === WorkMode.Colour
    if (!isFocused || !isColorMode || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    runColour()
  }, [isFocused, workMode, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = workMode === WorkMode.Colour
    if (isColorMode) {
      state.v = gestureBrightness
      runColour()
    } else {
      state.brightness = gestureBrightness
      runBrightness()
    }
  }, [isFocused, workMode, gestureBrightness])

  const { run: runColour } = useThrottleFn(() => {
    if (switchLed) {
      if (state.workMode !== WorkMode.Colour) {
        setWorkMode(WorkMode.Colour).then()
      }
      const colour = {h: state.h, s: state.s, v: state.v};
      setColour(colour)?.then()
    }
  }, { wait: 500 })
  const { run: runBrightness } = useThrottleFn(() => {
    if (switchLed) {
      if (state.workMode !== WorkMode.White) {
        setWorkMode(WorkMode.White).then()
      }
      setBrightness(state.brightness * 10)
    }
  }, { wait: 500 })

  const setSwitchLedAction = useCallback(async enable => {
    onLoading && onLoading(true);
    await setSwitchLed(enable);
    onLoading && onLoading(false);
    state.switchLed = enable;
  }, []);

  const setWorkModeAction = useCallback(async isColorMode => {
    onLoading && onLoading(true);
    await setWorkMode(isColorMode ? WorkMode.Colour : WorkMode.White);
    onLoading && onLoading(false);
    state.isColor = isColorMode;
  }, []);

  const setColourAction = useCallback(async (h, s, v) => {
    const colour = {h: h, s: s, v: v};
    onLoading && onLoading(true);
    await setColour(colour);
    if (state.workMode !== WorkMode.Colour) {
      await setWorkMode(WorkMode.Colour);
    }
    onLoading && onLoading(false);
    state.h = h;
    state.s = s;
    state.v = v;
  }, []);

  const setTemperatureAction = useCallback(async (temperature: number) => {
    onLoading && onLoading(true);
    await setTemperature(temperature * 10);
    if (state.workMode !== WorkMode.White) {
      await setWorkMode(WorkMode.White);
    }
    onLoading && onLoading(false);
    state.temperature = temperature;
  }, []);

  const setBrightnessAction = useCallback(async (brightness: number) => {
    onLoading && onLoading(true);
    await setBrightness(brightness * 10);
    if (state.workMode !== WorkMode.White) {
      await setWorkMode(WorkMode.White);
    }
    onLoading && onLoading(false);
    state.brightness = brightness;
  }, []);

  const getColorBlockColor = useCallback(() => {
    if (state.isColor) {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100));
      return hsv2Hex(state.h, s, 100);
    }
    if (isSupportTemperature()) {
      return cctToColor(state.temperature.toFixed());
    }
    return '#FEAC5B'
  }, [state.temperature]);

  return (
    <Card style={{marginVertical: cx(12), marginHorizontal: cx(24)}}>
      <View>
        <LdvSwitch
          title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
          color={getColorBlockColor()}
          colorAlpha={1}
          enable={state.switchLed}
          setEnable={setSwitchLedAction}
        />
        {state.switchLed && (
          <LampAdjustView
            isColorMode={state.isColor}
            setIsColorMode={setWorkModeAction}
            isSupportColor={isSupportColour()}
            isSupportTemperature={isSupportTemperature()}
            isSupportBrightness={isSupportBrightness()}
            h={state.h}
            s={state.s}
            v={state.v}
            onHSVChange={(h, s, v) => {
              state.h = h;
              state.s = s;
              state.v = v;
            }}
            reserveSV={true}
            onHSVChangeComplete={setColourAction}
            colorTemp={state.temperature}
            brightness={state.brightness}
            onCCTChange={(temperature: number) => {
              state.temperature = temperature;
            }}
            onCCTChangeComplete={setTemperatureAction}
            onBrightnessChange={(brightness: number) => {
              state.brightness = brightness;
            }}
            onBrightnessChangeComplete={setBrightnessAction}
            minBrightness={1}
            minSaturation={1}
          />
        )}
      </View>
    </Card>
  );
}
