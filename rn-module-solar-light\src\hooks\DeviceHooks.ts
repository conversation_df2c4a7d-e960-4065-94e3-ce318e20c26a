import {useDeviceId, useDp, useFlagMode,} from '@ledvance/base/src/models/modules/NativePropsSlice';
import {Result} from '@ledvance/base/src/models/modules/Result';
import {getHexByHSV, getHSVByHex} from '@ledvance/base/src/utils';
import {useCallback, useEffect} from 'react';
import {parseJSON} from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import {AdvancedData, AdvancedStatus, getAdvancedStatusColor,} from '@ledvance/base/src/components/AdvanceCard';
import I18n from '@ledvance/base/src/i18n';
import {WorkMode} from '@ledvance/base/src/utils/interface';
import {RouterKey} from 'navigation/Router';
import {MusicPageRouterParams} from "@ledvance/ui-biz-bundle/src/modules/music/MusicPage";
import {createParams} from "@ledvance/base/src/hooks/Hooks";
import {getGlobalParamsDp, isSupportFunctions} from '@ledvance/base/src/utils/common';
import {MoodPageParams} from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface';
import {FlagPageProps} from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage';
import {useUpdateEffect} from 'ahooks';
import {getFlagMode, saveFlagMode} from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions';
import res from '@ledvance/base/src/res';

const dpKey = {
    switch_led: 'switch_led',
    work_mode: 'work_mode',
    bright_value: 'bright_value',
    temp_value: 'temp_value',
    colour_data: 'colour_data',
    scene_data: 'scene_data',
    music_data: 'music_data',
    device_mode: 'device_mode',
    pir_state: 'pir_state',
    cds: 'cds',
    pir_sensitivity: 'pir_sensitivity',
    pir_delay: 'pir_delay',
    switch_pir: 'switch_pir',
    standby_time: 'standby_time',
    standby_bright: 'standby_bright',
    sensorgroup_sync: 'sensorgroup_sync',
    standby_on: 'standby_on',
    timer_report: 'timer_report',
    switch_save_energy: 'switch_save_energy',
    battery_state: 'battery_state',
    brightgroup_sync: 'brightgroup_sync',
};

export interface Colour {
    h: number;
    s: number;
    v: number;
}

export function useBatteryState(): string {
    return useDp<string, any>(getGlobalParamsDp(dpKey.battery_state))[0];
}

export function useSwitchLed(): [boolean, (value: boolean) => Promise<Result<any>>] {
    return useDp<boolean, any>(getGlobalParamsDp(dpKey.switch_led));
}

export function useSwitchPIR(): [boolean, (value: boolean) => Promise<Result<any>>] {
    return useDp<boolean, any>(getGlobalParamsDp(dpKey.switch_pir));
}

export function useWorkMode(): [string, (value: string) => Promise<Result<any>>] {
    return useDp<string, any>(getGlobalParamsDp(dpKey.work_mode));
}

export function useTemperature(): [number, (value: number) => (Promise<Result<any>> | null)] {
    if (!isSupportTemperature()) {
        const temperature = 1000;
        const setTemperature = useCallback((_: number) => {
            return null
        }, []);
        return [temperature, setTemperature];
    }
    return useDp<number, any>(getGlobalParamsDp(dpKey.temp_value));
}

export function useBrightness(): [number, (value: number) => (Promise<Result<any>> | null)] {
    if (!isSupportBrightness()) {
        const brightness = 1000;
        const setBrightness = useCallback((_: number) => {
            return null
        }, []);
        return [brightness, setBrightness];
    }
    return useDp<number, any>(getGlobalParamsDp(dpKey.bright_value));
}

export function usePIRState(): [string, (value: string) => Promise<Result<any>>] {
    return useDp<string, any>(getGlobalParamsDp(dpKey.pir_state));
}

export function usePIRDelay(): [number, (value: number) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.pir_delay));
}

export function usePIRSensitivity(): [string, (value: string) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.pir_sensitivity));
}

export function useLuxValue(): [string, (value: string) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.cds));
}

export function useSlightBright(): [number, (value: number) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.standby_bright));
}

export function useSlightBrightTime(): [number, (value: number) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.standby_time));
}

export function useSlightBrightAlwaysON(): [boolean, (value: boolean) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.standby_on));
}

export function useSwitchSaveEnergy(): [boolean, (value: boolean) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.switch_save_energy));
}

export function useDeviceMode(): [string, (value: string) => Promise<Result<any>>] {
    return useDp(getGlobalParamsDp(dpKey.device_mode));
}

export interface Colour {
    h: number;
    s: number;
    v: number;
}

export function useColour(): [Colour, (value: Colour) => (Promise<Result<any>> | null)] {
    if (!isSupportColour()) {
        const color = {h: 0, s: 100, v: 100};
        const setColor = useCallback((_: Colour) => {
            return null;
        }, [])
        return [color, setColor];
    }
    const [colourData, setColourData] = useDp<string, any>(getGlobalParamsDp(dpKey.colour_data));
    const hsv = getHSVByHex(colourData);
    const colour = {h: hsv.h || 0, s: Math.round((hsv.s || 1000) / 10), v: Math.round((hsv.v || 1000) / 10)};
    const setColour = useCallback((colour: Colour) => {
        const colourHex = getHexByHSV({h: colour.h, s: colour.s * 10, v: colour.v * 10});
        return setColourData(colourHex);
    }, []);
    return [colour, setColour];
}

export const isSupportMotionDetector = () => {
    return isSupportFunctions(dpKey.pir_state)
}

export const isSupportTemperature = () => {
    return isSupportFunctions(dpKey.temp_value);
};

export const isSupportColour = () => {
    return isSupportFunctions(dpKey.colour_data);
};

export const isSupportBrightness = () => {
    return isSupportFunctions(dpKey.bright_value);
};

export const isSupportMood = () => {
    return isSupportFunctions(dpKey.scene_data);
};

export const isSupportMusic = () => {
    return isSupportFunctions(dpKey.music_data);
}

export const isSupportSwitchSaveEnergy = () => {
    return isSupportFunctions(dpKey.switch_save_energy);
}

export const isSupportDeviceMode = () => {
    return isSupportFunctions(dpKey.device_mode)
}

export const isLamp = (): boolean => {
    return isSupportBrightness() || isSupportColour() || isSupportTemperature()
}

export function useAdvancedData(): AdvancedData[] {
    const deviceId = useDeviceId();
    const [workMode] = useWorkMode();
    const [switchLed] = useSwitchLed();
    const [switchPIR] = useSwitchPIR();
    const [flagMode, setFlagMode] = useFlagMode();
    const advanceData: AdvancedData[] = [];

    useEffect(() => {
        if (deviceId) {
            getFlagMode(deviceId).then(res => {
                if (res.success && res.data) {
                    setFlagMode(parseJSON(res.data));
                }
            });
        }
    }, [deviceId]);

    useUpdateEffect(() => {
        if (workMode !== WorkMode.Scene && flagMode?.flagMode) {
            setFlagMode({
                flagMode: false,
                flagId: undefined,
            });
            saveFlagMode(
                deviceId,
                JSON.stringify({
                    flagMode: false,
                    flagId: undefined,
                })
            ).then();
        }
    }, [workMode]);

    if (isSupportMood() && isSupportColour()) {
        const params = createParams<FlagPageProps>({
            sceneDataCode: getGlobalParamsDp(dpKey.scene_data),
            workModeCode: getGlobalParamsDp(dpKey.work_mode),
            isSupportColor: true,
            switchLedCode: getGlobalParamsDp(dpKey.switch_led),
            isSupportSceneStatus: true,
            isSolarLight:true,
        });
        advanceData.push({
            title: I18n.getLang('Feature_devicepanel_flags'),
            dp: {key: 'flag', code: 'flag'},
            icons: res.flag_icon,
            statusColor: getAdvancedStatusColor(
                flagMode?.flagMode && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
            ),
            router: {
                key: RouterKey.ui_biz_flag_page,
                params,
            },
        });
    }

    if (isSupportMood()) {
        const params = createParams<MoodPageParams>({
            isSupportColor: isSupportColour(),
            isSupportBrightness: isSupportBrightness(),
            isSupportTemperature: isSupportTemperature(),
            switchLedDp: getGlobalParamsDp(dpKey.switch_led),
            mainDp: getGlobalParamsDp(dpKey.scene_data),
            mainWorkMode: getGlobalParamsDp(dpKey.work_mode),
            mainSwitch: getGlobalParamsDp(dpKey.switch_led),
            isSupportSceneStatus: true,
            isSolarLight: true,
        });
        advanceData.push({
            title: I18n.getLang('mesh_device_detail_mode'),
            statusColor: getAdvancedStatusColor(!flagMode?.flagMode && workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
            dp: {key: dpKey.scene_data, code: getGlobalParamsDp(dpKey.scene_data)},
            router: {
                key: RouterKey.ui_biz_mood,
                params,
            },
        });
    }

    if (isSupportMusic()) {
        advanceData.push({
            title: I18n.getLang('devicemusic_headline_text'),
            statusColor: getAdvancedStatusColor(workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
            dp: {key: dpKey.music_data, code: getGlobalParamsDp(dpKey.music_data)},
            router: {
                key: RouterKey.ui_biz_music,
                params: {
                    switch_led: getGlobalParamsDp(dpKey.switch_led),
                    work_mode: getGlobalParamsDp(dpKey.work_mode),
                    music_data: getGlobalParamsDp(dpKey.music_data),
                    mix_rgbcw: '',
                    mix_light_scene: '',
                    isMixRGBWLamp: false,
                } as MusicPageRouterParams
            }
        });
    }

    if (isSupportMotionDetector()) {
        advanceData.push({
            title: I18n.getLang('motion_detection_headline_text'),
            statusColor: getAdvancedStatusColor(switchPIR ? AdvancedStatus.Enable : AdvancedStatus.Disable),
            router: {
                key: RouterKey.motion_detection,
            }
        });
    }

    if (isSupportSwitchSaveEnergy()) {
        advanceData.push({
            title: I18n.getLang('contact_sensor_specific_settings'),
            router: {key: RouterKey.settings},
        });
    }
    return advanceData;
}
