import {NavigationRoute} from 'tuya-panel-kit';
import HomePage from '../pages/HomePage';
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers';
import SettingsPage from "../pages/SettingsPage";
import MoodPageRouters from '@ledvance/ui-biz-bundle/src/newModules/mood/Router'
import MusicPageRouters from '@ledvance/ui-biz-bundle/src/modules/music/Router'
import FlagPageRouters from '@ledvance/ui-biz-bundle/src/modules/flags/Router'
import SelectPageRouter from '@ledvance/ui-biz-bundle/src/newModules/select/Route'
import MotionDetectionPage from "../pages/motionDetection/MotionDetectionPage";

export const RouterKey = {
    main: 'main',
    motion_detection: 'motion_detection',
    settings: 'settings',
    routines: 'routings',
    ...ui_biz_routerKey,
};

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.motion_detection,
        component: MotionDetectionPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        }
    },
    {
        name: RouterKey.settings,
        component: SettingsPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        }
    },
    ...MoodPageRouters,
    ...MusicPageRouters,
    ...FlagPageRouters,
    ...SelectPageRouter
];
