import React from 'react';
import Page from '@ledvance/base/src/components/Page';
import {useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import {NativeApi} from '@ledvance/base/src/api/native';
import {
    isLamp,
    isSupportDeviceMode,
    isSupportMotionDetector,
    useAdvancedData,
    useBatteryState
} from '../hooks/DeviceHooks';
import BatteryStateView from '../component/BatteryStateView';
import LampControlView from '../component/LampControlView';
import {ScrollView} from 'react-native';
import {useReactive} from 'ahooks';
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import DeviceModeView from "../component/DeviceModeView";

const HomePage = () => {
    const devInfo = useDeviceInfo();
    const familyName = useFamilyName();
    const batteryState = useBatteryState();
    const advancedData = useAdvancedData();
    const state = useReactive({
        loading: false,
    });
    return (
        <Page
            backText={familyName}
            headlineText={devInfo.name}
            headlineIcon={res.ic_more}
            onBackClick={() => NativeApi.back()}
            onHeadlineIconClick={() => NativeApi.toDeviceSettingsPage(devInfo.devId)}
            loading={state.loading}
        >
            <ScrollView>
                {batteryState && <BatteryStateView batteryState={batteryState}/>}
                {/*增加取反isSupportMotionDetector判断是因为PIR Light不需要支持DeviceMode*/}
                {isSupportDeviceMode() && !isSupportMotionDetector() &&
                    <DeviceModeView onLoading={(loading) => state.loading = loading}/>}
                {isLamp() && <LampControlView
                    onLoading={loading => {
                        state.loading = loading;
                    }}
                />}
                <AdvanceList advanceData={advancedData}/>
            </ScrollView>
        </Page>
    );
};

export default HomePage;
