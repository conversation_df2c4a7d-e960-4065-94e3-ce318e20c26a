import Page from "@ledvance/base/src/components/Page";
import React, {useEffect} from "react";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import ItemView from "../component/ItemView";
import {isSupportSwitchSaveEnergy, useSwitchSaveEnergy} from "../hooks/DeviceHooks";
import {useReactive} from "ahooks";

const SettingsPage = () => {
    const deviceInfo = useDeviceInfo();
    const [switchSaveEnergy, setSwitchSaveEnergy] = useSwitchSaveEnergy();

    const state = useReactive({
        loading: false,
        switchSaveEnergy: switchSaveEnergy,
    });
    useEffect(() => {
        state.switchSaveEnergy = switchSaveEnergy;
    }, [switchSaveEnergy]);
    return (
        <Page backText={deviceInfo.name}
              headlineText={I18n.getLang('contact_sensor_specific_settings')}
              loading={state.loading}>
            {isSupportSwitchSaveEnergy() && <ItemView
                title={I18n.getLang('energy_saving_switch')}
                description={I18n.getLang('energy_saving_switch_description')}
                switchValue={state.switchSaveEnergy}
                onSwitchChange={async (value: boolean) => {
                    state.loading = true;
                    await setSwitchSaveEnergy(value);
                    state.loading = false;
                    state.switchSaveEnergy = value;
                }}
            />}
        </Page>
    )
}

export default SettingsPage;