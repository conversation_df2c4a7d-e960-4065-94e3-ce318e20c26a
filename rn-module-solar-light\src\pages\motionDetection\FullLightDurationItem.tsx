import {Text, View} from "react-native";
import React, {useEffect, useMemo} from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {PickerDataProps, Utils} from "tuya-panel-kit";
import {useReactive, useThrottleFn, useUpdateEffect} from "ahooks";
import {usePIRDelay} from "../../hooks/DeviceHooks";
import Spacer from "@ledvance/base/src/components/Spacer";
import LDVPicker from "../../component/LDVPicker";
import ThemeType from "@ledvance/base/src/config/themeType";


const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function FullLightDurationItem(props: {theme?: ThemeType}) {
    const [pirDelay, setPIRDelay] = usePIRDelay();
    const state = useReactive({
        pirDelay: pirDelay,
        selectedMinutes: Math.floor(pirDelay / 60),
        selectedSeconds: pirDelay % 60,
        minutesItemList: getMinutesItemList(),
        secondsItemList: getNormalSecondsItemList(),
    });

    useEffect(() => {
        if (state.pirDelay !== pirDelay) {
            state.pirDelay = pirDelay;
            state.selectedMinutes = Math.floor(pirDelay / 60);
            state.selectedSeconds = pirDelay % 60;
        }
    }, [pirDelay]);


    useEffect(() => {
        switch (state.selectedMinutes) {
            case 60:
                state.secondsItemList = getZeroSecondsItemList();
                state.selectedSeconds = 0;
                break
            case 0:
                state.secondsItemList = getMinSecondsItemList();
                const minValue = parseInt(state.secondsItemList[0].value)
                if (state.selectedSeconds < minValue) {
                    state.selectedSeconds = minValue;
                }
                break
            default:
                state.secondsItemList = getNormalSecondsItemList();
                break
        }
    }, [state.selectedMinutes]);

    const saveTimeAction = () => {
        const pirDelay = state.selectedMinutes * 60 + state.selectedSeconds;
        setPIRDelay(pirDelay).then();
        state.pirDelay = pirDelay;
    }

    useUpdateEffect(() => {
        run();
    }, [
        state.selectedMinutes, state.selectedSeconds,
    ])

    const {run} = useThrottleFn(saveTimeAction, {wait: 500});

    return (<View style={{marginHorizontal: cx(24)}}>
        <Spacer/>
        <Text style={{color: props.theme?.global.fontColor, fontSize: cx(14)}}>{I18n.getLang('motion_detection_switched_off_text')}</Text>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <LDVPicker
                selectedValue={`${state.selectedMinutes}`}
                dataSource={state.minutesItemList}
                label={I18n.getLang('socket_settings_switch_off_min')}
                style={{flex: 1}}
                onValueChange={(value) => {
                    state.selectedMinutes = parseInt(value);
                }}
            />
            <LDVPicker
                selectedValue={`${state.selectedSeconds}`}
                dataSource={state.secondsItemList}
                label={I18n.getLang('socket_settings_switch_off_s')}
                style={{flex: 1}}
                onValueChange={(value) => {
                    state.selectedSeconds = parseInt(value);
                }}
            />
        </View>
    </View>)
})

const getMinutesItemList = (): PickerDataProps[] => {
    return useMemo(() => {
        return Array.from({length: 61}, (_, i) => i).map(value => {
            return {label: `${value}`.padStart(2, '0'), value: `${value}`}
        })
    }, []);
}

const getNormalSecondsItemList = (): PickerDataProps[] => {
    return Array.from({length: 60}, (_, i) => i).map(value => {
        return {label: `${value}`.padStart(2, '0'), value: `${value}`}
    });
}

const getZeroSecondsItemList = (): PickerDataProps[] => {
    return [{label: '00', value: '0'}];
}

const getMinSecondsItemList = (): PickerDataProps[] => {
    return Array.from({length: 55}, (_, i) => i + 5).map(value => {
        return {label: `${value}`.padStart(2, '0'), value: `${value}`}
    });
}
