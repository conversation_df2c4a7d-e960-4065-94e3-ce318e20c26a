import {FlatList, Text, View} from "react-native";
import React, {useEffect} from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {useLuxValue} from "../../hooks/DeviceHooks";
import {useReactive} from "ahooks";
import {Lux} from "../../utils/Lux";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import {SelectPageData, SelectPageParams} from "@ledvance/ui-biz-bundle/src/modules/select/SelectPage";
import {useNavigation} from '@react-navigation/native'
import {StackNavigationProp} from '@react-navigation/stack'
import {toSelectPage} from "@ledvance/ui-biz-bundle/src/navigation/tools";
import Spacer from "@ledvance/base/src/components/Spacer";
import {Utils} from "tuya-panel-kit";
import LDVModal from "../../component/LDVModal";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function LuxValueItem(props: {theme?: ThemeType}) {
    const navigation = useNavigation<StackNavigationProp<any>>()
    const [lux, setLux,] = useLuxValue()
    const state = useReactive({
        lux: lux,
        luxName: getLuxName(lux),
        luxItemList: getLuxItemList(lux),
        showLuxTipModal: false,
    });

    useEffect(() => {
        state.lux = lux;
        state.luxName = getLuxName(state.lux);
        state.luxItemList = getLuxItemList(state.lux);
    }, [lux]);

    const renderModalItem = (item: LuxDescription) => (
        <View style={{flexDirection: 'row', paddingVertical: cx(5)}}>
            <Text style={{fontSize: cx(14), color: props.theme?.global.fontColor, minWidth: cx(150)}}>{getLuxName(item.value)}</Text>
        </View>)

    const renderModalFooter = (item: LuxDescription) => (
        <View>
            <Spacer/>
            <Text style={{fontSize: cx(14), color: props.theme?.global.fontColor}}>{item.value}</Text>
        </View>)

    return (<View style={{marginHorizontal: cx(24),}}>
        <Spacer/>
        <TextFieldStyleButton
            placeholder={I18n.getLang('motion_detection_selectionfield2_topic_text')}
            text={state.luxName}
            showTipIcon={true}
            onTipIconPress={() => {
                state.showLuxTipModal = true;
            }}
            onPress={() => {
                const params: SelectPageParams<string> = {
                    title: I18n.getLang('motion_detection_selectionfield2_topic_text'),
                    data: state.luxItemList,
                    onSelect: selectPageData => {
                        setLux(selectPageData.value).then();
                        state.lux = selectPageData.value
                        state.luxName = getLuxName(selectPageData.value);
                    }
                }
                toSelectPage(navigation, params)
            }}/>
        <LDVModal visible={state.showLuxTipModal}
                  title={I18n.getLang('motion_detection_selectionfield2_topic_text')}
                  confirm={I18n.getLang('home_screen_home_dialog_yes_con')}
                  onConfirmPress={() => {
                      state.showLuxTipModal = false;
                  }}
        >
            <Spacer/>
            <Text style={{
                fontSize: cx(15),
                fontWeight: 'bold',
                color: props.theme?.global.fontColor
            }}>{I18n.getLang('lux_value_headline_text')}</Text>
            <Spacer/>
            <FlatList
                data={getModalItemData()}
                renderItem={({item}) => item.type === ItemType.item ? renderModalItem(item) : renderModalFooter(item)}
                keyExtractor={item => item.value}
                scrollEnabled={false}
            />
        </LDVModal>
    </View>)
})

const getLuxName = (lux: string): string => {
    switch (lux) {
        case Lux.Dusk:
            return I18n.getLang('motion_detection_selectionfield2_value2_text');
        case Lux.Evening:
            return I18n.getLang('motion_detection_selectionfield2_value3_text');
        case Lux.Night:
            return I18n.getLang('motion_detection_selectionfield2_value4_text');
        case Lux.Dark:
            return I18n.getLang('motion_detection_selectionfield2_value5_text');
        case Lux.Day:
        default:
            return I18n.getLang('motion_detection_selectionfield2_value_text');

    }
}

const getLuxItemList = (selectedLux: string): SelectPageData<string>[] => {
    return Object.entries(Lux).map(([_, value]) => {
        return {text: getLuxName(value), value: value, selected: selectedLux === value};
    })
};

interface LuxDescription {
    type: 'item' | 'footer'
    value: string,
}

enum ItemType {
    item = 'item',
    footer = 'footer'
}

const getModalItemData = (): LuxDescription[] => {
    return [
        {
            type: ItemType.item,
            value: Lux.Dark,
        },
        {
            type: ItemType.item,
            value: Lux.Night,
        },
        {
            type: ItemType.item,
            value: Lux.Evening,
        },
        {
            type: ItemType.item,
            value: Lux.Dusk,
        },
        {
            type: ItemType.item,
            value: Lux.Day,
        },
        {
            type: ItemType.footer,
            value: I18n.getLang('lux_value_headline_description'),
        }
    ]
}
