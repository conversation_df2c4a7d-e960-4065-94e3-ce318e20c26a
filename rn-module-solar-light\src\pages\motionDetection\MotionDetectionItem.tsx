import {View} from "react-native";
import React, {useEffect} from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {usePIRSensitivity} from "../../hooks/DeviceHooks";
import {useReactive} from "ahooks";
import TextFieldStyleButton from "@ledvance/base/src/components/TextFieldStyleButton";
import {PIRSensitivity} from "../../utils/PIRSensitivity";
import {SelectPageData, SelectPageParams} from "@ledvance/ui-biz-bundle/src/modules/select/SelectPage";
import {useNavigation} from '@react-navigation/native'
import {StackNavigationProp} from '@react-navigation/stack'
import {toSelectPage} from "@ledvance/ui-biz-bundle/src/navigation/tools";
import Spacer from "@ledvance/base/src/components/Spacer";
import {Utils} from "tuya-panel-kit";

const cx = Utils.RatioUtils.convertX;
export default function MotionDetectionItem() {
    const navigation = useNavigation<StackNavigationProp<any>>()
    const [pirSensitivity, setPIRSensitivity] = usePIRSensitivity()

    const state = useReactive({
        pirSensitivity: pirSensitivity,
        pirSensitivityName: getPIRSensitivityName(pirSensitivity),
        pirSensitivityItemList: getPIRSensitivityItemList(pirSensitivity),
    });

    useEffect(() => {
        state.pirSensitivity = pirSensitivity;
        state.pirSensitivityName = getPIRSensitivityName(state.pirSensitivity)
        state.pirSensitivityItemList = getPIRSensitivityItemList(state.pirSensitivity);
    }, [pirSensitivity]);
    return (<View style={{marginHorizontal: cx(24),}}>
        <Spacer/>
        <TextFieldStyleButton
            text={state.pirSensitivityName}
            placeholder={I18n.getLang('motion_detection_selectionfield_topic_text')}
            onPress={() => {
                const params: SelectPageParams<string> = {
                    title: I18n.getLang('motion_detection_selectionfield_topic_text'),
                    data: state.pirSensitivityItemList,
                    onSelect: selectPageData => {
                        setPIRSensitivity(selectPageData.value).then();
                        state.pirSensitivity = selectPageData.value
                        state.pirSensitivityName = getPIRSensitivityName(selectPageData.value);
                    }
                }
                toSelectPage(navigation, params)
            }}/>
    </View>);
}

const getPIRSensitivityName = (pirSensitivity: string): string => {
    switch (pirSensitivity) {
        case PIRSensitivity.High:
            return I18n.getLang('contact_sensor_battery_state1');
        case PIRSensitivity.Middle:
            return I18n.getLang('contact_sensor_battery_state2');
        case PIRSensitivity.Low:
        default:
            return I18n.getLang('contact_sensor_battery_state3');
    }
}

const getPIRSensitivityItemList = (selectedValue: string): SelectPageData<string>[] => {
    return Object.entries(PIRSensitivity).map(([_, value]) => {
        return {text: getPIRSensitivityName(value), value: value, selected: selectedValue === value};
    })
};

