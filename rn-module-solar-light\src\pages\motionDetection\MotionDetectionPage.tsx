import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import {
    isSupportMotionDetector, useSwitchPIR,
} from "../../hooks/DeviceHooks";
import MotionDetectorView from "./MotionDetectorView";
import Spacer from "@ledvance/base/src/components/Spacer";
import {useReactive} from "ahooks";
import {ScrollView, Text, View, StyleSheet} from "react-native";
import {SwitchButton, Utils} from "tuya-panel-kit";
import MotionDetectionItem from "./MotionDetectionItem";
import FullLightDurationItem from "./FullLightDurationItem";
import LuxValueItem from "./LuxValueItem";
import SlightBrightItem from "./SlightBrightItem";
import React, {useEffect} from "react";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

const MotionDetectionPage = (props: {theme?: ThemeType}) => {
    const devInfo = useDeviceInfo()
    const [switchPIR, setSwitchPIR] = useSwitchPIR();
    const state = useReactive({
        loading: false,
        switchPIR: switchPIR,
    })

    useEffect(() => {
        state.switchPIR = switchPIR;
    }, [switchPIR]);

    const styles = StyleSheet.create({
        description: {
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
            marginHorizontal: cx(24),
        },
        lighting: {
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            fontWeight: 'bold',
            marginHorizontal: cx(24),
        },
        triggerLightingParent: {
            flexDirection: 'row',
            alignItems: 'center',
            marginHorizontal: cx(24)
        },
        triggerLighting: {
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
            flex: 1
        }
    })

    return (
        <Page backText={devInfo.name}
              headlineText={I18n.getLang('motion_detection_headline_text')}
              loading={state.loading}>
            <ScrollView>
                <Text style={styles.description}>{I18n.getLang('motion_detection_description_text')}</Text>
                {isSupportMotionDetector() && <MotionDetectorView/>}
                <Spacer/>
                <Text style={styles.lighting}>{I18n.getLang('motion_detection_subheadline_text')}</Text>
                <Spacer/>
                <View style={styles.triggerLightingParent}>
                    <Text style={styles.triggerLighting}>{I18n.getLang('motion_detection_trigger_text')}</Text>
                    <SwitchButton value={state.switchPIR} onValueChange={async (switchPIR) => {
                        state.loading = true;
                        await setSwitchPIR(switchPIR);
                        state.loading = false;
                        state.switchPIR = switchPIR;
                    }}/>
                </View>
                {state.switchPIR && <MotionDetectionItem/>}
                {state.switchPIR && <LuxValueItem/>}
                {state.switchPIR && <FullLightDurationItem/>}
                {state.switchPIR && <SlightBrightItem onLoading={(loading) => {
                    state.loading = loading;
                }}/>}
                <Spacer height={cx(40)}/>
            </ScrollView>
        </Page>
    )
}

export default withTheme(MotionDetectionPage)
