import Card from "@ledvance/base/src/components/Card";
import React, {useEffect} from "react";
import {Utils} from "tuya-panel-kit";
import {Image, Text, StyleSheet, View} from "react-native";
import I18n from "@ledvance/base/src/i18n";
import Res from '@ledvance/base/src/res'
import {usePIRState, useSwitchPIR} from "../../hooks/DeviceHooks";
import {useReactive} from "ahooks";
import {PIRState} from "../../utils/PIRState";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

export default withTheme(function MotionDetectorView(props: {theme?: ThemeType}) {
    const [pirState] = usePIRState();
    const [switchPIR] = useSwitchPIR();
    const state = useReactive({
        switchPIR: switchPIR,
        isPIR: pirState === PIRState.PIR,
    });
    useEffect(() => {
        state.isPIR = pirState === PIRState.PIR;
    }, [pirState]);

    useEffect(() => {
        state.switchPIR = switchPIR;
    }, [switchPIR]);

    const styles = StyleSheet.create({
        title: {
            color: props.theme?.global.fontColor,
            fontSize: cx(16),
            flex: 1,
            fontFamily: 'helvetica_neue_lt_std_bd',
        },
        image: {
            width: cx(150),
            height: cx(150),
            alignSelf: 'center',
        },
        content: {
            marginVertical: cx(24),
            alignSelf: 'center',
            color: props.theme?.global.fontColor,
            fontSize: cx(14),
            fontFamily: 'helvetica_neue_lt_std_bd',
        }
    })

    return (
        <Card style={{marginHorizontal: cx(24), marginTop: cx(26)}}
              containerStyle={{flexDirection: 'column',}}>
            <View style={{flexDirection: 'row', alignItems: 'center', margin: cx(16)}}>
                <Text style={styles.title}> {I18n.getLang('motion_detector_tile_headline')}</Text>
            </View>
            <Image
                style={styles.image}
                source={{uri: state.isPIR && state.switchPIR ? Res.rn_image_motion_detected : Res.rn_image_no_motion_detected}}/>
            <Text
                style={styles.content}>
                {I18n.getLang(state.isPIR && state.switchPIR ? 'motion_detector_tile_status_1' : 'motion_detector_tile_status_2')}
            </Text>
        </Card>
    )
})
