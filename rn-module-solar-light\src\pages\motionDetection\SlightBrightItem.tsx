import {Image, Text, TouchableOpacity, View} from "react-native";
import React, {useEffect} from "react";
import I18n from "@ledvance/base/src/i18n/index";
import {PickerDataProps, SwitchButton, Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import LdvSlider from "@ledvance/base/src/components/ldvSlider";
import {
    useSlightBright,
    useSlightBrightTime,
    useSlightBrightAlwaysON,
} from "../../hooks/DeviceHooks";
import {useReactive, useThrottleFn, useUpdateEffect} from "ahooks";
import res from "@ledvance/base/src/res";
import LDVPicker from "../../component/LDVPicker";
import LDVModal from "../../component/LDVModal";
import ThemeType from '@ledvance/base/src/config/themeType'

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils

interface SlightBrightProps extends LoadingCallback {
    theme?: ThemeType
}

const SLIGHT_BRIGHT_DEFAULT = 10;
export default withTheme(function SlightBrightItem(props: SlightBrightProps) {
    const { onLoading } = props
    const [slightBright, setSlightBright] = useSlightBright();
    const [slightBrightTime, setSlightBrightTime] = useSlightBrightTime();
    const [slightBrightAlwaysON, setSlightBrightAlwaysON] = useSlightBrightAlwaysON();
    const state = useReactive({
        slightBright: slightBright / 10,
        // lastSlightBright用于保存当前操作的最新值，不包含0，slightBright存在为0情况
        lastSlightBright: slightBright / 10,
        slightBrightON: slightBright !== 0,
        slightBrightTime: slightBrightTime,
        selectedHour: Math.floor(slightBrightTime / 60),
        selectedMinutes: slightBrightTime % 60,
        slightBrightAlwaysON: slightBrightAlwaysON,
        minutesItemList: getNormalMinutesItemList(),
        hourItemList: getHourItemList(),
        showStandbyTipModal: false,
    });

    useEffect(() => {
        if (state.slightBrightTime !== slightBrightTime) {
            state.slightBrightTime = slightBrightTime;
            state.selectedHour = Math.floor(slightBrightTime / 60);
            state.selectedMinutes = slightBrightTime % 60;
        }
    }, [slightBrightTime]);

    useEffect(() => {
        state.slightBrightAlwaysON = slightBrightAlwaysON;
    }, [slightBrightAlwaysON]);

    useEffect(() => {
        state.slightBright = slightBright / 10;
        state.slightBrightON = slightBright !== 0;
    }, [slightBright]);

    useEffect(() => {
        switch (state.selectedHour) {
            case 0:
                state.minutesItemList = getMinMinutesItemList();
                const minValue = parseInt(state.minutesItemList[0].value);
                if (state.selectedMinutes < minValue) {
                    state.selectedMinutes = minValue;
                }
                break
            case 8:
                state.minutesItemList = getZeroMinutesItemList();
                state.selectedMinutes = 0;
                break
            default:
                state.minutesItemList = getNormalMinutesItemList();
                break
        }
    }, [state.selectedHour]);

    useUpdateEffect(() => {
        run();
    }, [state.selectedHour, state.selectedMinutes])

    const setSlightBrightAction = async (value: number) => {
        await setSlightBright(value * 10);
        state.slightBright = value;
    };

    const saveStandbyLightTime = () => {
        const duration = state.selectedHour * 60 + state.selectedMinutes;
        setSlightBrightTime(duration).then();
        state.slightBrightTime = duration;
    }
    const {run} = useThrottleFn(saveStandbyLightTime, {wait: 500});

    return (<View>
        <Spacer/>
        <View style={{flexDirection: 'row', alignItems: 'center', marginHorizontal: cx(24)}}>
            <Text
                style={{color: props.theme?.global.fontColor, fontSize: cx(14)}}>{I18n.getLang('motion_detection_standby_light_text1')}</Text>
            <TouchableOpacity onPress={() => {
                state.showStandbyTipModal = true;
            }}>
                <Image source={{ uri: res.ic_info}} style={{marginStart: cx(4), width: cx(16), height: cx(16), tintColor: props.theme?.icon.primary}}/>
            </TouchableOpacity>
            <Spacer style={{flex: 1}}/>
            <SwitchButton value={state.slightBrightON} onValueChange={async (switchValue: boolean) => {
                // 当未设置过slightBright时默认使用10%
                const slightBright = state.lastSlightBright !== 0 ? state.lastSlightBright : SLIGHT_BRIGHT_DEFAULT;
                const value = switchValue ? slightBright : 0;
                onLoading && onLoading(true);
                await setSlightBrightAction(value);
                onLoading && onLoading(false);
                state.slightBrightON = switchValue;
            }}/>
        </View>
        {state.slightBrightON && <View style={{flexDirection: 'column', marginHorizontal: cx(24)}}>
            <Spacer/>
            <LdvSlider
                title={I18n.getLang('motion_detection_standby_light_brightness_text')}
                value={state.slightBright}
                min={1}
                max={30}
                style={{paddingHorizontal: cx(0)}}
                onSlidingComplete={(value) => {
                    setSlightBrightAction(value).then();
                    state.lastSlightBright = value;
                }}/>
            <Spacer/>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Text style={{
                    color: props.theme?.global.fontColor,
                    fontSize: cx(14),
                    flex: 1
                }}>{I18n.getLang('motion_detection_standby_light_text2')}</Text>
                <SwitchButton value={!state.slightBrightAlwaysON} onValueChange={async (switchValue: boolean) => {
                    // 开关功能为是否开启一段时间后自动关闭，所以开关状态取是否常亮的相反值
                    onLoading && onLoading(true);
                    await setSlightBrightAlwaysON(!switchValue);
                    onLoading && onLoading(false);
                    state.slightBrightAlwaysON = !switchValue;
                }}/>
            </View>
            {!state.slightBrightAlwaysON && <View>
                <Spacer/>
                <Text style={{
                    color: props.theme?.global.fontColor,
                    fontSize: cx(14)
                }}>{I18n.getLang('motion_detection_with_safe_mode_switch_off_text2')}</Text>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <LDVPicker
                        selectedValue={`${state.selectedHour}`}
                        dataSource={state.hourItemList}
                        label={I18n.getLang('time_unit_h')}
                        style={{flex: 1}}
                        onValueChange={(value) => {
                            state.selectedHour = parseInt(value);
                        }}
                    />
                    <LDVPicker
                        selectedValue={`${state.selectedMinutes}`}
                        dataSource={state.minutesItemList}
                        label={I18n.getLang('socket_settings_switch_off_min')}
                        style={{flex: 1}}
                        onValueChange={(value) => {
                            state.selectedMinutes = parseInt(value);
                        }}
                    />
                </View>
            </View>}
        </View>}

        <LDVModal visible={state.showStandbyTipModal}
                  title={I18n.getLang('motion_detection_standby_light_text1')}
                  confirm={I18n.getLang('home_screen_home_dialog_yes_con')}
                  onConfirmPress={() => {
                      state.showStandbyTipModal = false;
                  }}
        >
            <Spacer/>
            <Text style={{fontSize: cx(14),color:props.theme?.global.fontColor,}}>{I18n.getLang('standby_light_information_text')}</Text>
        </LDVModal>
    </View>)
})

const getHourItemList = (): PickerDataProps[] => {
    return Array.from({length: 9}, (_, i) => i).map(value => {
        return {label: `${value}`.padStart(2, '0'), value: `${value}`}
    });
}

const getNormalMinutesItemList = (): PickerDataProps[] => {
    return Array.from({length: 60}, (_, i) => i).map(value => {
        return {label: `${value}`.padStart(2, '0'), value: `${value}`}
    });
}

const getMinMinutesItemList = (): PickerDataProps[] => {
    return Array.from({length: 59}, (_, i) => i + 1).map(value => {
        return {label: `${value}`.padStart(2, '0'), value: `${value}`}
    });
}

const getZeroMinutesItemList = (): PickerDataProps[] => {
    return [{label: '00', value: '0'}];
}
