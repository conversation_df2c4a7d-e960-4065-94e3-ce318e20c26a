import {
  useDp,
  useDeviceId,
  useTimeSchedule,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import { NativeApi } from '@ledvance/base/src/api/native';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { useUpdateEffect, useReactive } from 'ahooks';
import { useCallback, useEffect, useMemo } from 'react';
import { SupportUtils, nToHS } from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import I18n, { I18nKey } from '@ledvance/base/src/i18n';
import { RouterKey } from '../navigation/Router';
import {MoodInfo, MoodPageParams } from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface';
import {
  AdvancedData,
  AdvancedStatus,
  getAdvancedStatusColor,
} from '@ledvance/base/src/components/AdvanceCard';
import { createParams } from '@ledvance/base/src/hooks/Hooks';
import {
  DeviceStateType,
  DeviceType,
  FanLightData,
} from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { dp2Obj, obj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse';
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common';
import { timeFormat, useCountdowns } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'
import { ApplyForItem } from '@ledvance/base/src/utils/interface';

export function useSwitchLed(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('switch_led'));
}

export function useSwitchFan(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(getGlobalParamsDp('fan_switch'));
}

export function useFanSpeed(): [number, (value: number) => Promise<Result<any>>] {
  return useDp<number, any>(getGlobalParamsDp('fan_speed'));
}

export function useFanMode(): [string, (mode: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('fan_mode'));
}

export function useFanDirection(): [string, (mode: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('fan_direction'));
}

export function useFanDisinfect(): [boolean, (enable: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('disinfect'));
}

export enum WorkMode {
  Plan = 'Plan',
  Control = 'control',
  Scene = 'scene',
  Rhythm = 'rhythm',
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  return useDp<WorkMode, any>(getGlobalParamsDp('work_mode'));
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4);
  const s = str.substring(4, 6);
  const v = str.substring(6, 8);
  const brightness = str.substring(8, 10);
  const temperature = str.substring(10, 12);
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16),
    v: parseInt(v, 16),
    brightness: parseInt(brightness, 16),
    temperature: parseInt(temperature, 16),
  };
};

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80));
};

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(
    getGlobalParamsDp('bright_value')
  );
  const setBrightFn = (v: number) => {
    return setBright(v * 10);
  };
  return [Math.round(bright / 10), setBrightFn];
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue]: [number, (v: number) => Promise<Result<any>>] = useDp(
    getGlobalParamsDp('temp_value')
  );
  const setTempValueFn = (v: number) => {
    return setTempValue(v * 10);
  };
  return [Math.round(tempValue / 10), setTempValueFn];
}

export function useCountDown(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('countdown'));
}

export function useCountDownsFan(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('countdown_left_fan'));
}

export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp('bright_value');
}

export function isSupportTemperature(): boolean {
  return SupportUtils.isSupportDp('temp_value');
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp('countdown');
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp('scene_data');
}

export function isSupportNightLight() : boolean{
  return SupportUtils.isSupportDp('switch_night_light')
}


export function useAdvancedData(): AdvancedData[] {
  const res: AdvancedData[] = [];
  const deviceId = useDeviceId();
  const [workMode] = useWorkMode();
  const [switchLed] = useSwitchLed();
  const [switchFan] = useSwitchFan()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const state = useReactive({
    rhythmModeStatus: AdvancedStatus.Disable,
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    sleepWakeUpStatus: AdvancedStatus.Disable,
  });
  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  const fanLightApplyFor: ApplyForItem[] = [
    {
      type: 'light',
      name: I18n.getLang('Onoff_button_socket'),
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch_led'),
      enable: true,
    },
    {
      type: 'fan',
      key: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
      dp: getGlobalParamsDp('fan_switch'),
      enable: true
    }
  ];

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      deviceData: {
        type: DeviceType.FanLight,
        // @ts-ignore
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
          fanSpeed: 1,
          direction: 'forward',
          mode: 'normal',
          disinfect: false
        }
      },
      isManual: !(dps.hasOwnProperty(getGlobalParamsDp('scene_data'))),
      mood: undefined
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('scene_data'))) {
      const sceneMood = dp2Obj(dps[getGlobalParamsDp('scene_data')], true, true)
      deviceState.mood = {
        ...sceneMood,
        mainLamp: {
          ...sceneMood.mainLamp,
          id: sceneMood.id
        }
      }
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('bright_value'))) {
      deviceState.deviceData.deviceData.brightness = Math.round(dps[getGlobalParamsDp('bright_value')] / 10);
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('temp_value'))) {
      deviceState.deviceData.deviceData.temperature = Math.round(dps[getGlobalParamsDp('temp_value')] / 10);
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('fan_speed'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.fanSpeed = dps[getGlobalParamsDp('fan_speed')];
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('fan_mode'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.mode = dps[getGlobalParamsDp('fan_mode')];
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('fan_direction'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.direction = dps[getGlobalParamsDp('fan_direction')];
    }
    if (dps.hasOwnProperty(getGlobalParamsDp('disinfect'))) {
      // @ts-ignore
      deviceState.deviceData.deviceData.disinfect = dps[getGlobalParamsDp('disinfect')];
    }
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback((deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
    const { deviceData, isManual, mood } = deviceState;
    const manualDps = {};
    if (!isManual && mood) {
      if ((mood as MoodInfo).mainLamp && (mood as MoodInfo).mainLamp?.nodes?.length) {
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene
        manualDps[getGlobalParamsDp('scene_data')] = obj2Dp((mood as MoodInfo), true, true)
        manualDps[getGlobalParamsDp('switch_led')] = true
        manualDps[getGlobalParamsDp('fan_switch')] = (mood as MoodInfo).mainLamp.fanEnable
        if ((mood as MoodInfo).mainLamp.fanEnable){
          manualDps[getGlobalParamsDp('fan_speed')] = (mood as MoodInfo).mainLamp.fanSpeed
        }
      }
    } else {
      const device = deviceData.deviceData as FanLightData
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
        if (apply.enable){
          if (apply.type === 'light'){
            manualDps[getGlobalParamsDp('bright_value')] = device.brightness * 10
            manualDps[getGlobalParamsDp('temp_value')] = device.temperature * 10
          }
          if (apply.type === 'fan'){
            manualDps[getGlobalParamsDp('fan_speed')] = device.fanSpeed
            manualDps[getGlobalParamsDp('fan_direction')] = device.direction
            manualDps[getGlobalParamsDp('disinfect')] = device.disinfect
            manualDps[getGlobalParamsDp('fan_mode')] = device.mode
          }
        }
      });
      manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Control
    }
    return manualDps;
  }, []);

  if (isSupportMood()) {
    const params = createParams<MoodPageParams>({
      isSupportColor: false,
      isSupportBrightness: isSupportBrightness(),
      isSupportTemperature: isSupportTemperature(),
      switchLedDp: getGlobalParamsDp('switch_led'),
      mainDp: getGlobalParamsDp('scene_data'),
      mainWorkMode: getGlobalParamsDp('work_mode'),
      mainSwitch: getGlobalParamsDp('switch_led'),
      isFanLight: true,
      isUVCFan: true
    });
    res.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(
        workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'scene_data', code: getGlobalParamsDp('scene_data')},
      router: {
        key: RouterKey.ui_biz_mood,
        params,
      },
    });
  }


  res.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: fanLightApplyFor,
        isSupportMood: isSupportMood(),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: isSupportBrightness(),
        isSupportTemperature: isSupportTemperature(),
        isFanLight: true,
        isUVCFan: true
      } as TimeSchedulePageParams,
    },
  });

  if (isSupportTimer()) {
    const params = createParams({
      dps: [
        {
          label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
          value: 'lighting',
          dpId: getGlobalParamsDp('countdown'),
          enableDp: getGlobalParamsDp('switch_led'),
          cloudKey: 'lightingInfo',
          stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
          stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
        },
        {
          label: I18n.getLang('add_new_dynamic_mood_ceiling_fan_field_headline'),
          value: 'fan',
          dpId: getGlobalParamsDp('countdown_left_fan'),
          enableDp: getGlobalParamsDp('fan_switch'),
          cloudKey: 'fanInfo',
          stringOn: 'timer_ceiling_fan_switched_on_text',
          stringOff: 'timer_ceiling_fan_switched_off_text'
        },
      ],
    })
    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() =>{
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let enable = false
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown'):
            enable = switchLed
            break
          case getGlobalParamsDp('countdown_left_fan'):
            enable = switchFan
            break
        }
        const L = 'ceiling_fan_feature_2_light_text_min_'
        const F = 'ceiling_fan_feature_2_fan_text_min_'
        const isLight = timer.dpId === getGlobalParamsDp('countdown')
        const key: I18nKey = `${isLight ? L : F}${enable ? 'off': 'on'}`
        return I18n.formatValue(key, timeFormat(timer.countdown[0], true))
      })
    }, [switchLed, switchFan, JSON.stringify(tasks)])
    res.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'countdown', code: getGlobalParamsDp('countdown')},
      router: {
        key: RouterKey.ui_biz_timer,
        params
      },
    });
  }

  return res;
}
