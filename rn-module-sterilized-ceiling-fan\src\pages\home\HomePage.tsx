import React, {useCallback, useEffect} from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Utils } from 'tuya-panel-kit';
import {useReactive, useThrottleFn, useUpdateEffect} from 'ahooks';
import Page from '@ledvance/base/src/components/Page';
import { NativeApi, sendAppEvent } from '@ledvance/base/src/api/native';
import Card from '@ledvance/base/src/components/Card';
import Spacer from '@ledvance/base/src/components/Spacer';
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch';
import ColorTempAdjustView from '@ledvance/base/src/components/ColorTempAdjustView';
import FanAdjustView from '@ledvance/base/src/components/FanAdjustView';
import I18n from '@ledvance/base/src/i18n';
import {
  useDeviceInfo,
  useFamilyName,
  useFanMaxSpeed,
  useGestureControl,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import {
  useBrightness,
  useTemperature,
  useFanDirection,
  useFanDisinfect,
  useFanMode,
  useFanSpeed,
  useSwitchFan,
  useSwitchLed,
  useWorkMode,
  useAdvancedData,
  WorkMode,
} from 'hooks/FeatureHooks';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import { cctToColor } from '@ledvance/base/src/utils/cctUtils';
import { useIsFocused } from '@react-navigation/core'

const { convertX: cx } = Utils.RatioUtils;

export const directOptions = [
  { label: I18n.getLang('ceiling_fan_tile_uvc_fan_direction_opt_1'), value: 'forward' },
  { label: I18n.getLang('ceiling_fan_tile_uvc_fan_direction_opt_2'), value: 'reverse' },
];
export const modeOptions = [
  { label: I18n.getLang('ceiling_fan_tile_uvc_fan_mode_opt_1'), value: 'normal' },
  { label: I18n.getLang('ceiling_fan_tile_uvc_fan_mode_opt_2'), value: 'nature' },
];
const HomePage = () => {
  const devInfo = useDeviceInfo();
  const familyName = useFamilyName();
  const [switchLed, setSwitchLed] = useSwitchLed();
  const [brightValue, setBrightValue] = useBrightness();
  const [tempValue, setTempValue] = useTemperature();
  const [fanEnable, setFanEnable] = useSwitchFan();
  const [fanSpeed, setFanSpeed] = useFanSpeed();
  const [fanMode, setFanMode] = useFanMode();
  const [direction, setDirection] = useFanDirection();
  const [disinfect, setDisinfect] = useFanDisinfect();
  const [workMode, setWorkMode] = useWorkMode();
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureBrightness] = useGestureControl('brightness')
  const advanceData = useAdvancedData();
  const state = useReactive({
    brightValue,
    tempValue,
    fanSpeed,
    directOptions: directOptions,
    modeOptions: modeOptions,
  });

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useUpdateEffect(() => {
    if (brightValue) {
      state.brightValue = brightValue;
    }
  }, [brightValue]);

  useUpdateEffect(() => {
    state.tempValue = Math.floor(tempValue);
  }, [tempValue]);

  useUpdateEffect(() => {
    state.fanSpeed = fanSpeed || 1;
  }, [fanSpeed]);

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    state.brightValue = gestureBrightness
    runBrightness()
  }, [isFocused, gestureBrightness])

  const { run: runBrightness } = useThrottleFn(() => {
    if (switchLed) {
      if (workMode !== WorkMode.Control) {
        setWorkMode(WorkMode.Control).then()
      }
      setBrightValue(state.brightValue).then()
    }
  }, { wait: 500 })

  const setBrightStateValue = useCallback((brightness: number) => {
    state.brightValue = brightness;
  }, []);

  const setRemoteBrightValue = useCallback(async (brightness: number) => {
    setBrightStateValue(brightness);
    if (workMode !== WorkMode.Control) {
      await setWorkMode(WorkMode.Control);
    }
    await setBrightValue(state.brightValue);
  }, []);

  const setTempStateValue = useCallback((temp: number) => {
    state.tempValue = temp;
  }, []);

  const setRemoteTempValue = useCallback(async (temp: number) => {
    setTempStateValue(temp);
    if (workMode !== WorkMode.Control) {
      await setWorkMode(WorkMode.Control);
    }
    await setTempValue(state.tempValue);
  }, []);

  const getBlockColor = useCallback(() => {
    return cctToColor(state.tempValue);
  }, [state.tempValue]);

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId);
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={styles.content}>
          <Spacer />
          <Card style={styles.lightCard}>
            <LdvSwitch
              title={I18n.getLang('light_sources_tile_tw_lighting_headline')}
              color={getBlockColor()}
              colorAlpha={1}
              enable={switchLed}
              setEnable={(v: boolean) => {
                setSwitchLed(v).then();
                setWorkMode(WorkMode.Control).then();
              }}
            />
            {switchLed && (
              <>
                <ColorTempAdjustView
                  colorTemp={state.tempValue}
                  brightness={state.brightValue}
                  isSupportTemperature={true}
                  isSupportBrightness={true}
                  onCCTChange={setTempStateValue}
                  onCCTChangeComplete={setRemoteTempValue}
                  onBrightnessChange={setBrightStateValue}
                  onBrightnessChangeComplete={setRemoteBrightValue}
                />
                <Spacer />
              </>
            )}
          </Card>
          <Spacer />
          <FanAdjustView
            fanEnable={fanEnable}
            fanSpeed={state.fanSpeed}
            maxFanSpeed={useFanMaxSpeed()}
            isSupportDirection={true}
            isSupportDisinfect={true}
            isSupportMode={true}
            directValue={direction}
            disinfect={disinfect}
            modeValue={fanMode}
            directOptions={state.directOptions}
            modeOptions={state.modeOptions}
            directChange={direction => {
              setDirection(direction).then();
            }}
            modeChange={mode => {
              setFanMode(mode).then();
            }}
            disinfectChange={disinfect => {
              setDisinfect(disinfect).then();
            }}
            onFanSwitch={fanEnable => {
              setFanEnable(fanEnable).then();
              setWorkMode(WorkMode.Control).then();
            }}
            onFanSpeedChangeComplete={fanSpeed => {
              setFanSpeed(fanSpeed).then();
              setWorkMode(WorkMode.Control).then();
            }}
            style={styles.fanAdjustCard}
          />
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
  },
  lightCard: {
    marginHorizontal: cx(24),
  },
  fanAdjustCard: {
    marginHorizontal: cx(24),
    borderBottomLeftRadius: cx(10),
    borderTopLeftRadius: cx(10),
  },
});

export default HomePage;
