
import { useDp, useDeviceId, useTimeSchedule, useFlagMode, useDps } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { NativeApi, getTuyaCloudData } from '@ledvance/base/src/api/native'
import { Result } from '@ledvance/base/src/models/modules/Result'
import { useUpdateEffect, useReactive, useCreation, useThrottleFn } from 'ahooks'
import { useCallback, useMemo, useEffect, useState } from 'react'
import { ColorUtils, SupportUtils, avgSplit, nToHS, parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import I18n from '@ledvance/base/src/i18n/index'
import { RouterKey } from '../navigation/Router'
import {MoodInfo, MoodPageParams} from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface'
import { getHSVByHex, getHexByHSV } from '@ledvance/base/src/utils'
import { DrawTool, drawToolFormat, drawToolParse } from './ProtocolConvert'
import { Node } from '@ledvance/base/src/components/DrawToolView'
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import { cloneDeep } from 'lodash'
import { lampApi } from '@tuya/tuya-panel-api'
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage'
import { Buffer } from 'buffer'
import { stripDp2Obj, stripObj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import { FlagPageProps } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage'
import { getFlagMode, saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import { ApplyForItem } from '@ledvance/base/src/utils/interface'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { DeviceData, DeviceStateType, DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import res from '@ledvance/base/src/res'
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'


const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  colour_data: { key: 'colour_data', code: '24' },
  countdown: { key: 'countdown', code: '26' },
  music_data: { key: 'music_data', code: '27' },
  rgbic_linerlight_scene: { key: 'rgbic_linerlight_scene', code: '56' },
  led_number_set: { key: 'led_number_set', code: '58' },
  draw_tool: { key: 'draw_tool', code: '59' }
}

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.switch_led.code)
}

export enum WorkMode {
  White = 'white',
  Colour = 'colour',
  Scene = 'scene',
  Music = 'music'
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(dpKC.work_mode.code)
  const [workMode, setWokMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWokMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWokMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

type HSV = {
  h: number
  s: number
  v: number
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4)
  const s = str.substring(4, 6)
  const v = str.substring(6, 8)
  const brightness = str.substring(8, 10)
  const temperature = str.substring(10, 12)
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16),
    v: parseInt(v, 16),
    brightness: parseInt(brightness, 16),
    temperature: parseInt(temperature, 16)
  }
}

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80))
}

export function useColorData(): [HSV, (h: number, s: number, v: number) => Promise<Result<any>>] {
  const [color, setColor] = useDp(dpKC.colour_data.code)
  const hsv = useMemo(() => {
    if (!color) return {
      h: 360,
      s: 100,
      v: 100
    }
    const hsvData = getHSVByHex(color)
    return {
      h: hsvData.h,
      s: Math.round(hsvData.s / 10),
      v: Math.round(hsvData.v / 10)
    }
  }, [color])

  const setColorFn = (h: number, s: number, v: number) => {
    return setColor(getHexByHSV({
      h,
      s: s * 10,
      v: v * 10
    }))
  }

  return [hsv, setColorFn]
}

export interface DrawToolProps {
  version?: number
  daubType?: number,
  effect?: number,
  h?: number,
  s?: number,
  v?: number,
  bright?: number,
  temp?: number,
  idx?: number
  nextWorkMode?: WorkMode
}
type ColorType = {
  h?: number
  s?: number
  v?: number
  b?: number
}
type DrawToolType = [DrawTool, (data: DrawToolProps) => Promise<Result<any>>, Node[], (color: ColorType, idxList: number[]) => void]
const whiteHex = '#FEAC5B'
const colorHex = '#FF0000'
export function useDrawTool(): DrawToolType {
  const [dps, setPaintData]: [string, (hex: Record<string, any>) => Promise<Result<any>>] = useDps()
  const drawTool = dps[dpKC.draw_tool.code]
  // const [drawTool, setDrawTool]: [string, (v: string) => Promise<Result<any>>] = useDp(dpKC.draw_tool.code)
  const [ledNum] = useLedNum()
  const [workMode] = useWorkMode()
  const devId = useDeviceId()
  const state = useReactive({
    cloudData: [] as Node[],
    sendingDp: [] as string[],
    cloudHex: ''
  })
  const hsv = useCreation(() => drawToolParse(drawTool), [drawTool])

  useEffect(() => {
    getTuyaData().then()
  }, [])

  useUpdateEffect(() => {
    if (state.sendingDp.includes(drawTool) || drawTool.slice(-4) === '480b') {
      state.sendingDp = state.sendingDp.filter(dp => dp === drawTool)
      return
    }
    getTuyaCloud.run()
  }, [drawTool, ledNum])

  const getTuyaData = async (num?: number) => {
    const n = num ?? ledNum
    if (hsv.daubType === 1) {
      const { bright, h, s, v } = hsv
      const isColorMode = h !== 0 || s !== 0 || v !== 0
      state.cloudData = Array.from({ length: n }, () => ({ color: isColorMode ? `#${getBrightOpacity(v)}${ColorUtils.hsv2hex(h, Math.max(s, 10), 100).slice(1)}` : `#${getBrightOpacity(bright)}${whiteHex.slice(1)}`}))
      state.cloudHex = isColorMode ? `${nToHS(h, 4)}${nToHS(s)}${nToHS(v)}0000`.repeat(n) : `00000000${nToHS(bright)}00`
    } else {
      const res = await getTuyaCloudData(devId)
      if (res && res['lights_0']) {
        const v = parseJSON(res['lights_0'])
        const isString = typeof v === 'string'
        const cloudNode = getColorFn(isString ? v : v.data)
        const nodeHex = isString ? v : v.data
        if (nodeHex) {
          if (n > cloudNode.length) {
            state.cloudData = cloudNode.concat(createColorFn(colorHex, n - cloudNode.length))
            state.cloudHex = nodeHex + '016864640000'.repeat(n - cloudNode.length)
            // colorHex 转换成 016864640000
          } else {
            state.cloudData = cloudNode.slice(0, n)
            state.cloudHex = nodeHex.slice(0, 12 * n)
          }
        } else {
          state.cloudData = createColorFn(colorHex, n)
          state.cloudHex = `${nToHS(360, 4)}${nToHS(100)}${nToHS(100)}0000`.repeat(n)
        }

      } else {
        state.cloudData = createColorFn(colorHex, n)
        state.cloudHex = `${nToHS(360, 4)}${nToHS(100)}${nToHS(100)}0000`.repeat(n)
      }
    }
  }

  const saveCloudData = async (data: string) => {
    return lampApi.generalApi.saveCloudConfig('lights_0', data)
  }

  const { run } = useThrottleFn(saveCloudData, { wait: 300, leading: true })
  const getTuyaCloud = useThrottleFn(getTuyaData, { wait: 400, leading: true })


  const getColorFn = (hex: string, n: number = 12) => {
    const colorDataList = avgSplit(hex, n).map(hex => {
      return getColorData(hex)
    })

    return colorDataList.map(color => {
      if (color.v !== 0) {
        return {
          color: `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
        }
      } else {
        return {
          color: Object.values(color).every(item => item === 0) ? `#20${whiteHex.slice(1)}` : `#${getBrightOpacity(color.brightness)}${whiteHex.slice(1)}`
        }
      }
    })
  }

  const createColorFn = useCallback((color: string, length: number) => {
    return Array.from({ length }, () => ({ color }))
  }, [])



  const setDrawToolFn = useCallback(async ({
    version = 1,  // 版本
    daubType = 1, // 涂抹动作  01 全选, 02 单选, 03 擦除
    effect = 1, // 涂抹效果 01 静态, 02 闪烁, 03 呼吸
    h = 0,
    s = 0,
    v = 0,
    bright = 0,
    temp = 0,
    idx,
    nextWorkMode
  }: DrawToolProps) => {
    await run(state.cloudHex)
    const data = { version, daubType, effect, h, s, v, bright, temp, idx }
    const hex = drawToolFormat(data)
    state.sendingDp = [...state.sendingDp, hex]
    if (workMode === WorkMode.Music) {
      MusicManager.close()
    }
    const extraDp = {
      [dpKC.draw_tool.code]: hex
    }
    extraDp[dpKC.work_mode.code] = nextWorkMode || WorkMode.Colour
    return setPaintData(extraDp)
  }, [state.sendingDp, state.cloudHex, workMode])

  const setColorFn = useCallback(async (color: ColorType, idxList: number[]) => {
    const newNodeList = cloneDeep(state.cloudData).map((item, idx) => {
      if (idxList.includes(idx)) {
        if (Object.keys(color).length) {
          if (color.h !== undefined && color.s !== undefined && color.v !== undefined) {
            item.color = `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
          } else {
            item.color = `#${getBrightOpacity(color.b as number)}${whiteHex.slice(1)}`
          }
        } else {
          item.color = `#20${whiteHex.slice(1)}`
        }
      }
      return item
    })
    const newHex = avgSplit(state.cloudHex, 12).map((hex, idx) => {
      if (idxList.includes(idx)) {
        if (Object.keys(color).length) {
          if (color.h !== undefined) {
            return `${nToHS(color.h, 4)}${nToHS(color.s)}${nToHS(color.v)}0000`
          } else {
            return `00000000${nToHS(color.b)}00`
          }
        } else {
          return '000000000000'
        }
      }
      return hex
    }).join('')
    state.cloudData = newNodeList
    state.cloudHex = newHex
  }, [state.cloudData, state.cloudHex])

  return [hsv, setDrawToolFn, state.cloudData, setColorFn]
}


export const useScene = (): [string, (v: string) => Promise<Result<any>>] => {
  const [scene, setScene]: [string, (v: string) => Promise<Result<any>>] = useDp(dpKC.rgbic_linerlight_scene.code)
  return [scene, setScene]
}

export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(dpKC.bright_value.code)
  const setBrightFn = (v: number) => {
    return setBright(v * 10)
  }
  return [Math.round(bright / 10), setBrightFn]
}

export function useLedNum(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.led_number_set.code)
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code)
}


export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.bright_value.key)
}

export function isSupportColor(): boolean {
  return SupportUtils.isSupportDp(dpKC.colour_data.key)
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp(dpKC.rgbic_linerlight_scene.key)
}

export function isSupportMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.music_data.key)
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key)
}

export function useAdvancedData(): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const deviceId = useDeviceId()
  const [workMode] = useWorkMode()
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const [flagMode, setFlagMode] = useFlagMode()
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })

      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          setFlagMode(parseJSON(res.data))
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    if (workMode !== WorkMode.Scene && flagMode?.flagMode) {
      setFlagMode({
        flagMode: false,
        flagId: undefined
      })
      saveFlagMode(deviceId, JSON.stringify({
        flagMode: false,
        flagId: undefined
      })).then()
    }
    if (workMode !== WorkMode.Music){
      MusicManager.close()
    }
  }, [workMode, JSON.stringify(flagMode)])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: isSupportColor() ? true : false,
        },
      },
      isManual: !dps.hasOwnProperty(getGlobalParamsDp('rgbic_linerlight_scene')
      ),
      mood: undefined,
    };
    if (dps.hasOwnProperty(getGlobalParamsDp('draw_tool'))){
      const paintColor = Buffer.from(dps[getGlobalParamsDp('draw_tool')], 'base64').toString('hex')
      const paintData: any = drawToolParse(paintColor)
      const isColorMode = dps[getGlobalParamsDp('work_mode')] === WorkMode.Colour
      deviceState.deviceData.deviceData = {
        ...deviceState.deviceData.deviceData,
        ...paintData,
        h: isColorMode ? Math.trunc(paintData.h) : 0,
        s: isColorMode ? Math.trunc(paintData.s) : 100,
        v: isColorMode ? Math.trunc(paintData.v) : 100,
        brightness: isColorMode ? 100 : Math.trunc(paintData.bright),
        temperature: 0,
        isColorMode
      }
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('rgbic_linerlight_scene'))) {
      const mood = stripDp2Obj(Buffer.from(dps[getGlobalParamsDp('rgbic_linerlight_scene')], 'base64').toString('hex'), true);
      deviceState.mood = cloneDeep(mood);
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('rgbic_linerlight_scene')] = Buffer.from(stripObj2Dp((mood as MoodInfo), true), 'hex').toString('base64');
        manualDps[getGlobalParamsDp('switch_led')] = true;
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene;
      } else {
        const device = deviceData.deviceData as DeviceData
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
        });
        if (manualDps[getGlobalParamsDp('switch_led')]) {
          const paint_color = drawToolFormat({
            ...device,
            temp: 0,
            bright: device.isColorMode ? 0 : device.brightness,
            h: device.isColorMode ? device.h : 0,
            s: device.isColorMode ? device.s : 0,
            v: device.isColorMode ? device.v : 0,
            daubType: 1,
            version: 1,
            effect: 1
          })
          if (getGlobalParamsDp('draw_tool')){
            manualDps[getGlobalParamsDp('draw_tool')] = Buffer.from(paint_color, 'hex').toString('base64')
          }
          manualDps[getGlobalParamsDp('work_mode')] = device.isColorMode ? WorkMode.Colour : WorkMode.White
        }
      }

      return manualDps;
    },
    []
  );

  advanceData.push({
    title: I18n.getLang('Feature_devicepanel_flags'),
    dp: { key: 'flag', code: 'flag'},
    icons: res.flag_icon,
    statusColor: getAdvancedStatusColor(flagMode?.flagMode && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
    router: {
      key: RouterKey.ui_biz_flag_page,
      params: {
        isStringLight: true,
        isSupportColor: isSupportColor(),
        sceneDataCode: dpKC.rgbic_linerlight_scene.code,
        workModeCode: dpKC.work_mode.code,
        switchLedCode: dpKC.switch_led.code
      } as FlagPageProps
    }
  })

  if (isSupportMood()) {
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(!flagMode?.flagMode && workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.rgbic_linerlight_scene,
      router: {
        key: RouterKey.ui_biz_mood,
        params: {
          switchLedDp: dpKC.switch_led.code,
          mainDp: dpKC.rgbic_linerlight_scene.code,
          mainWorkMode: dpKC.work_mode.code,
          mainSwitch: dpKC.switch_led.code,
          isSupportBrightness: isSupportBrightness(),
          isSupportColor: isSupportColor(),
          isSupportTemperature: false,
          isStringLight: true,
          featureId: dpKC.rgbic_linerlight_scene.key
        } as MoodPageParams,
      },
    })
  }

  const lightApplyFor: ApplyForItem[] = [
    {
      type: 'light',
      name: I18n.getLang('Onoff_button_socket'),
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch_led'),
      enable: true,
    },
  ];

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: lightApplyFor,
        isSupportBrightness: isSupportBrightness(),
        isSupportColor: isSupportColor(),
        isSupportTemperature: false,
        isSupportMood: isSupportMood(),
        isStringLight: true,
        featureId: 'rgbic_linerlight_scene',
        manualDataDp2Obj,
        manualDataObj2Dp
      } as TimeSchedulePageParams
    }
  })

  if (isSupportMusic()) {
    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.music_data,
      router: {
        key: RouterKey.ui_biz_music,
        params: {
          switch_led: dpKC.switch_led.code,
          work_mode: dpKC.work_mode.code,
          mix_rgbcw: undefined,
          mix_light_scene: undefined,
          music_data: dpKC.music_data.code,
          isMixRGBWLamp: false,
        },
      },
    })
  }


  if (isSupportTimer()) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: dpKC.countdown.code,
              enableDp: dpKC.switch_led.code,
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }
  return advanceData
}
