import { nToSH } from "@tuya/tuya-panel-lamp-sdk/lib/utils"
import { DrawToolProps } from "./FeatureHooks"

export interface DrawTool {
  version: number
  daubType: number,
  effect: number,
  h: number,
  s: number,
  v: number,
  bright: number,
  temp: number,
}

const getDefaultDrawTool = () =>{
  return {
    version: 0, 
    daubType: 0, 
    effect: 0,
    h: 360,
    s: 100,
    v: 100,
    bright: 1,
    temp: 0
  }
}

export function drawToolParse(hex: string) {
  if(!hex) return getDefaultDrawTool()
  const version = parseInt(hex.substring(0, 2), 16)
  const daubType = parseInt(hex.substring(2, 4), 16)
  const effect = parseInt(hex.substring(4, 6), 16)
  const h = parseInt(hex.substring(6, 10), 16)
  const s = parseInt(hex.substring(10, 12), 16)
  const v = parseInt(hex.substring(12, 14), 16)
  const bright = parseInt(hex.substring(14, 16), 16)
  const temp = parseInt(hex.substring(16, 18), 16)
  return {
    version,
    daubType,
    effect,
    h,
    s,
    v,
    bright,
    temp
  }
}

export function drawToolFormat(data: DrawToolProps) {
  const { version, daubType, effect, h, s, v, bright, temp, idx} = data
  const r = daubType === 1 ? '' : '8100' + nToSH(idx)
  return `${nToSH(version)}${nToSH(daubType)}${nToSH(effect)}${nToSH(h, 4)}${nToSH(s)}${nToSH(v)}${nToSH(bright)}${nToSH(temp)}${r}`
}



export function subHexStr2Int(str: string, start: number, end?: number) {
  return parseInt(str.substring(start, end), 16)
}

export function int2Hex(num: number, hexLength: number = 2): string {
  return num.toString(16).padStart(hexLength, '0')
}
