import React, {useCallback, useEffect} from 'react'
import {ScrollView, View} from 'react-native'
import Page from '@ledvance/base/src/components/Page'
import {useDeviceInfo, useFamilyName, useGestureControl} from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import {NativeApi, sendAppEvent} from '@ledvance/base/src/api/native'
import {range} from 'lodash'
import {
  isSupportBrightness,
  isSupportColor,
  useAdvancedData,
  useBrightness,
  useColorData,
  useDrawTool,
  useLedNum,
  useSwitch,
  useWorkMode,
  WorkMode,
} from '../../hooks/FeatureHooks'
import {useReactive, useThrottleFn, useUpdateEffect} from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import DrawToolView from '@ledvance/base/src/components/DrawToolView'
import {hsv2Hex, mapFloatToRange} from '@ledvance/base/src/utils'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import {useIsFocused} from '@react-navigation/core'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks'

type AdjustType = 1 | 2 | 3
const HomePage = () => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const [switchLed, setSwitchLed] = useSwitch()
  const [ledNum, setLedNum] = useLedNum()
  const [, setDrawTool, ledColorList, setColorFn] = useDrawTool()
  const [workMode, setWorkMode] = useWorkMode()
  const [brightness] = useBrightness()
  const [hsv] = useColorData()
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const advanceData = useAdvancedData()
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator();
  const state = useReactive({
    ...hsv,
    brightness,
    loading: false,
    ledNumModalVisible: false,
    adjustType: 1,
    touchIdx: undefined as undefined | number,
    touchIdxList: '',
    flag: Symbol(),
    workMode,
    nextWorkMode: workMode
  })

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useEffect(() => {
    if (ledNum < 5 || ledNum > 48) {
      state.ledNumModalVisible = true
    }
  }, [ledNum])

  useUpdateEffect(() => {
    state.workMode = workMode
  }, [workMode])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('bright_value'))
    if (!shouldBlock) {
      state.brightness = brightness
    }
  }, [brightness])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('colour_data'))
    if (!shouldBlock) {
      state.h = hsv.h
      state.s = hsv.s
      state.v = hsv.v
    }
  }, [hsv])


  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = state.workMode === WorkMode.Colour
    if (!isFocused || !isColorMode || state.adjustType !== 1 || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    setColorFn({ h: state.h, s: state.s, v: state.v }, range(ledNum))
    run()
  }, [isFocused, state.workMode, state.adjustType, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = state.workMode === WorkMode.Colour
    if (isColorMode) {
      state.v = gestureBrightness
    } else {
      state.brightness = gestureBrightness
    }
    setColorFn(workMode === WorkMode.Colour ? { h: state.h, s: state.s, v: state.v } : { b: state.brightness }, range(ledNum))
    run()
  }, [isFocused, state.workMode, gestureBrightness])


  useUpdateEffect(() => {
    if (state.adjustType === 1) {
      setColorFn(workMode === WorkMode.Colour ? { h: state.h, s: state.s, v: state.v } : { b: state.brightness }, range(ledNum))
    }
    updateDrawTool(state.touchIdx)
  }, [state.flag])

  const nodeTouchHandle = (idx: number) => {
    if (idx !== state.touchIdx && state.adjustType !== 1) {
      state.touchIdx = idx
      setColorFn(state.adjustType === 3 ? {} : state.workMode === WorkMode.Colour ? { h: state.h, s: state.s, v: state.v } : { b: state.brightness }, [idx])
      updateDrawTool(idx)
    }
  }

  const updateDrawTool = useCallback((idx?: number) => {
    const params = state.adjustType !== 3 ? (state.workMode === WorkMode.Colour ? { h: state.h, s: state.s, v: state.v } : { bright: state.brightness }) : {}
    if (params.hasOwnProperty('bright')) {
      sendDpWithTimestamps(getGlobalParamsDp('bright_value'), async () => {}).then()
    }
    if (params.hasOwnProperty('h')) {
      sendDpWithTimestamps(getGlobalParamsDp('colour_data'), async () => {}).then()
    }
    setDrawTool({
      ...params,
      daubType: state.adjustType,
      idx,
      nextWorkMode: state.nextWorkMode
    }).then()
  }, [state.adjustType, state.workMode, state.h, state.s, state.v, state.brightness])

  const { run } = useThrottleFn((idx?: number) => {
    if (switchLed) {
      updateDrawTool(idx)
    }
  }, { wait: 500 })

  const getBlockColor = useCallback(() => {
    if (workMode === WorkMode.Colour) {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100))
      return hsv2Hex(state.h, s, 100)
    }
    return '#FEAC5B'
  }, [workMode, state.h, state.s, state.v])

  useUpdateEffect(() => {
    if (workMode === WorkMode.Music || workMode === WorkMode.Scene) {
      state.adjustType = 1
    }
  }, [workMode])

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}>
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          <DrawToolView
            adjustType={state.adjustType as AdjustType}
            setAdjustType={(t) => {
              if (state.adjustType !== t) {
                state.adjustType = t
                state.touchIdx = undefined
              }
            }}
            nodes={ledColorList}
            fixCount={5}
            nodeTouch={nodeTouchHandle}
            fingerUp={() => { }}
            switchLed={switchLed}
            showEnable={true}
            setEnable={async (enable: boolean) => {
              state.loading = true
              await setSwitchLed(enable)
              state.loading = false
            }}
            isSupportColor={isSupportColor()}
            isSupportTemperature={false}
            isSupportBrightness={isSupportBrightness()}
            isColorMode={workMode === WorkMode.Colour}
            setIsColorMode={async isColorMode => {
              state.loading = true
              state.touchIdx = undefined
              await setWorkMode(isColorMode ? WorkMode.Colour : WorkMode.White)
              state.loading = false
            }}
            blockColor={getBlockColor()}
            h={state.h} s={state.s} v={state.v}
            onHSVChange={() => { }}
            onHSVChangeComplete={async (h, s, v) => {
              state.h = h
              state.s = s
              state.v = v
              state.touchIdx = undefined
              state.nextWorkMode = WorkMode.Colour
              if (state.adjustType === 1) state.flag = Symbol()
            }}
            temperature={0}
            brightness={state.brightness}
            onCCTChange={() => { }}
            onCCTChangeComplete={() => { }}
            onBrightnessChange={() => { }}
            onBrightnessChangeComplete={brightness => {
              state.brightness = brightness
              state.touchIdx = undefined
              state.nextWorkMode = WorkMode.White
              if (state.adjustType === 1) state.flag = Symbol()
            }}
            ledNum={ledNum}
            setLedNum={(v) => {
              setLedNum(v)
            }}
            ledNumModalVisible={state.ledNumModalVisible}
          />
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default HomePage
