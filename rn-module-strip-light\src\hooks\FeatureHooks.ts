import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDeviceId, useDp, useTimeSchedule, useDps, useDeviceInfo, useFlagMode } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { NativeApi, getTuyaCloudData } from '@ledvance/base/src/api/native'
import { Result } from '@ledvance/base/src/models/modules/Result'
import { ColorUtils, SupportUtils, avgSplit, nToHS, parseJSON } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import I18n from '@ledvance/base/src/i18n/index'
import { RouterKey } from '../navigation/Router'
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard'
import {MoodInfo, MoodPageParams} from '@ledvance/ui-biz-bundle/src/newModules/mood/Interface'
import { getHSVByHex, getHexByHSV } from '@ledvance/base/src/utils'
import { cloneDeep, flatMap, range } from 'lodash'
import { useCreation, useReactive, useThrottleFn, useUpdateEffect } from 'ahooks'
import { ColorTool, Combination, WhiteTool, drawToolFormat, drawToolParse, getDefaultDrawTool } from './ProtocolConvert'
import { Node } from '@ledvance/base/src/components/DrawToolView'
import * as MusicManager from '@ledvance/ui-biz-bundle/src/modules/music/MusicManager'
import { MusicPageRouterParams } from '@ledvance/ui-biz-bundle/src/modules/music/MusicPage'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import { lampApi } from '@tuya/tuya-panel-api'
import { stripDp2Obj, stripObj2Dp } from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodParse'
import { Buffer } from 'buffer'
import { FlagPageProps } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagPage'
import { getFlagMode, saveFlagMode } from '@ledvance/ui-biz-bundle/src/modules/flags/FlagActions'
import res from '@ledvance/base/src/res'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage'
import { ApplyForItem } from '@ledvance/base/src/utils/interface'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { DeviceStateType, DeviceType, StripLightData } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface'
import { timeFormat } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction'


export const dpKC = {
  switch_led: { key: 'switch_led', code: '20' },
  work_mode: { key: 'work_mode', code: '21' },
  bright_value: { key: 'bright_value', code: '22' },
  temp_value: { key: 'temp_value', code: '23' },
  colour_data: { key: 'colour_data', code: '24' },
  countdown: { key: 'countdown', code: '26' },
  music_data: { key: 'music_data', code: '27' },
  light_length: { key: 'light_length', code: '46' },
  light_pixel: { key: 'light_pixel', code: '47' },
  dreamlight_scene_mode: { key: 'dreamlight_scene_mode', code: '51' },
  dreamlightmic_music_data: { key: 'dreamlightmic_music_data', code: '52' },
  lightpixel_number_set: { key: 'lightpixel_number_set', code: '53' },
  paint_colour_data: { key: 'paint_colour_data', code: '61' },
  paint_color: { key: 'paint_color', code: '101' },  // 少数灯涂抹调色是这个dp
  sync_on_screen: { key: 'sync_on_screen', code: '101' }, // 目前只有一款灯有这个dp  PID:fgzre3xmrav5cykj
  lamp_top: { key: 'lamp_top', code: '107' },
  lamp_bottom: { key: 'lamp_bottom', code: '108' },
  lamp_left: { key: 'lamp_left', code: '109' },
  lamp_right: { key: 'lamp_right', code: '110' },
}

export function useSwitch(): [boolean, (value: boolean) => Promise<Result<any>>] {
  return useDp<boolean, any>(dpKC.switch_led.code)
}
// 彩光 all     [00,01,00,06,00,00,f6,03,20,03,e8]
// 彩光 single  [00,01,00,06,01,00,01,02,c1,03,e8,81,01]
// 彩光 single1 [00,01,00,06,01,00,01,02,c1,03,e8,86,01,02,03,04,05,06]
// 彩光 clear   [00,01,00,06,02,00,00,00,00,00,00,81,01]
// 白光 all     [00,00,00,06,00,03,e8,03,b4]
export enum WorkMode {
  White = 'white',
  Colour = 'colour',
  Scene = 'scene',
  Music = 'music'
}

export function useWorkMode(): [WorkMode, (value: WorkMode) => Promise<Result<any>>] {
  const [workModeDp, setWorkModeDp] = useDp<WorkMode, any>(dpKC.work_mode.code)
  const [workMode, setWokMode] = useState(workModeDp)

  useUpdateEffect(() =>{
    setWokMode(workModeDp)
  }, [workModeDp])

  const setWorkModeFn = (v: WorkMode) =>{
    setWokMode(v)
    return setWorkModeDp(v)
  }
  return [workMode, setWorkModeFn]
}

type HSV = {
  h: number
  s: number
  v: number
}

export const getColorData = (str: string) => {
  const h = str.substring(0, 4)
  const s = str.substring(4, 8)
  const v = str.substring(8, 12)
  const brightness = str.substring(12, 16)
  const temperature = str.substring(16, 20)
  return {
    h: parseInt(h, 16),
    s: parseInt(s, 16) / 10,
    v: parseInt(v, 16) / 10,
    brightness: parseInt(brightness, 16) / 10,
    temperature: parseInt(temperature, 16) / 10
  }
}

export const getBrightOpacity = (bright: number) => {
  return nToHS(Math.max(Math.round((bright / 100) * 255), 80))
}

export const getHexByTemp = (temp: number, bright?: number) => {
  const rgb = cctToColor(Math.round(temp)).match(/\d+/g)
  return `#${getBrightOpacity(bright || 100)}${ColorUtils.rgb2hex(rgb[0], rgb[1], rgb[2]).slice(1)}`
}

export function useColorData(): [HSV, (h: number, s: number, v: number) => Promise<Result<any>>] {
  const [color, setColor] = useDp(dpKC.colour_data.code)
  const hsv = useMemo(() => {
    if (!color) return {
      h: 360,
      s: 100,
      v: 100
    }
    const hsvData = getHSVByHex(color)
    return {
      h: hsvData.h,
      s: Math.round(hsvData.s / 10),
      v: Math.round(hsvData.v / 10)
    }
  }, [color])

  const setColorFn = (h: number, s: number, v: number) => {
    return setColor(getHexByHSV({
      h,
      s: s * 10,
      v: v * 10
    }))
  }
  return [hsv, setColorFn]
}

export function usePaintDp() {
  if (SupportUtils.isSupportDp(dpKC.paint_color.key)) {
    return dpKC.paint_color
  }
  return dpKC.paint_colour_data
}

type ColorType = {
  h?: number
  s?: number
  v?: number
  b?: number
  t?: number
}
type PaintColorType = [any, (data: ColorTool | WhiteTool) => Promise<Result<any>>, Node[], (color: ColorType | string[], idxList: number[]) => void]
const whiteHex = '#FEAC5B'
const colorHex = '#FF0000'
export function usePaintColorData(): PaintColorType {
  const [dps, setPaintData]: [string, (hex: Record<string, any>) => Promise<Result<any>>] = useDps()
  const paintDp = usePaintDp()
  const paintData = dps[paintDp.code]
  const devId = useDeviceId()
  const ledNum = useLightPixel()
  const [workMode] = useWorkMode()
  const [flagMode, setFlagMode] = useFlagMode()
  const state = useReactive({
    cloudData: [] as any[],
    cloudHex: '',
    ledNum,
    sendingDp: [] as string[]
  })

  const hsv = useCreation(() => {
    const paint = drawToolParse(paintData)
    const isWhite = isSupportBrightness() || isSupportTemperature()
    if (isSupportPixelNumberSet() && !isWhite && paint.adjustCode === 0) {
      return {
        ...getDefaultDrawTool(),
        s: 1
      }
    } else {
      return paint
    }
  }, [paintData])

  useEffect(() => {
    getTuyaData().then()
  }, [])

  useUpdateEffect(() => {
    state.ledNum = ledNum
  }, [ledNum])

  useUpdateEffect(() => {
    if (state.sendingDp.includes(paintData)) {
      state.sendingDp = state.sendingDp.filter(dp => dp === paintData)
      return
    }
    getTuyaCloud.run()

  }, [paintData])


  const getTuyaData = async () => {
    if (hsv.daubType === 0) {
      switch (hsv.adjustCode) {
        case 0:
          let { bright, temp } = hsv as WhiteTool
          bright = Math.round(bright / 10)
          temp = Math.round(temp / 10)
          state.cloudData = Array.from({ length: state.ledNum }, () => ({ color: getHexByTemp(temp, bright) }))
          state.cloudHex = `000000000000${nToHS(bright * 10, 4)}${nToHS(temp * 10, 4)}`.repeat(state.ledNum)
          break;
        case 3:
          const { colors } = hsv as Combination
          const colorsNode = flatMap(range(state.ledNum), (index) => ({
            color: colors[index % colors.length]
          }))
          state.cloudData = colorsNode
          state.cloudHex = colorsNode.map(c => {
            const v = ColorUtils.hex2hsv(c.color)
            return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
          }).join('')
          break;
        default:
          const { h, s, v } = hsv as ColorTool
          state.cloudData = Array.from({ length: state.ledNum }, () => ({ color: `#${getBrightOpacity(v)}${ColorUtils.hsv2hex(h, Math.max(s, 10), 100).slice(1)}` }))
          state.cloudHex = `${nToHS(h, 4)}${nToHS(s * 10, 4)}${nToHS(v * 10, 4)}00000000`.repeat(state.ledNum)
      }

    } else {
      const res = await getTuyaCloudData(devId)
      if (res && res['lights_0']) {
        const v = parseJSON(res['lights_0'])
        const isString = typeof v === 'string'
        const nodeHex = isString ? v : v?.data
        if (nodeHex) {
          state.cloudData = getColorFn(nodeHex)
          state.cloudHex = nodeHex
        } else {
          state.cloudData = createColorFn(isSupportPixelNumberSet() ? colorHex : whiteHex, state.ledNum)
          state.cloudHex = `000000000000${nToHS(0 * 10, 4)}${nToHS(100 * 10, 4)}`.repeat(state.ledNum)
        }
      } else {
        state.cloudData = createColorFn(isSupportPixelNumberSet() ? colorHex : whiteHex, state.ledNum)
        state.cloudHex = `000000000000${nToHS(0 * 10, 4)}${nToHS(100 * 10, 4)}`.repeat(state.ledNum)
      }
    }
  }

  const saveCloudData = async (data: string) => {
    const res = await lampApi.generalApi.saveCloudConfig('lights_0', data)
    return res
  }

  const { run } = useThrottleFn(saveCloudData, { wait: 300, leading: true })
  const getTuyaCloud = useThrottleFn(getTuyaData, { wait: 400, leading: true })

  const getColorFn = (hex: string, n: number = 20) => {
    const colorDataList = avgSplit(hex, n).map(hex => {
      return getColorData(hex)
    })
    return colorDataList.map(color => {
      if (color.v !== 0) {
        return {
          color: `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
        }
      } else {
        return {
          color: Object.values(color).every(item => item === 0) ? `#20${whiteHex.slice(1)}` : getHexByTemp(color.temperature, color.brightness)
        }
      }
    })
  }

  const createColorFn = useCallback((color: string, length: number) => {
    return Array.from({ length }, () => ({ color }))
  }, [])

  const addToSendDp = (dp: string) =>{
    const cloneArray = [...state.sendingDp]
    if (state.sendingDp.length >= 3){
      cloneArray.shift()
      state.sendingDp = cloneArray
    }else{
      cloneArray.push(dp)
      state.sendingDp = cloneArray
    }
  }


  const setDrawToolFn = async (data: ColorTool | WhiteTool | Combination) => {
    if (flagMode?.flagMode) {
      setFlagMode({
        ...flagMode,
        flagMode: false
      })
      saveFlagMode(devId, JSON.stringify({
        ...flagMode,
        flagMode: false
      })).then()
    }
    await run(state.cloudHex)
    const hex = drawToolFormat({
      ...data,
      num: state.ledNum
    })
    const { adjustCode } = data
    if(data.daubType !== 0){
      addToSendDp(hex)
    }
    const extraDp = {
      [paintDp.code]: hex
    }
    if (workMode === WorkMode.Music) {
      MusicManager.close()
    }
    if ((adjustCode !== 0 && workMode !== WorkMode.Colour) || (adjustCode === 0 && workMode !== WorkMode.White)) {
      // await setWorkMode(adjustCode === 0 ? WorkMode.White : WorkMode.Colour)
      extraDp[dpKC.work_mode.code] = adjustCode === 0 ? WorkMode.White : WorkMode.Colour
    }

    return setPaintData(extraDp)
  }

  const setColorFn = useCallback(async (color: ColorType | string[], idxList: number[]) => {
    if (Array.isArray(color)) {
      const colors = flatMap(range(state.ledNum), (index) => ({
        color: color[index % color.length]
      }))
      state.cloudData = colors
      state.cloudHex = colors.map(c => {
        const v = ColorUtils.hex2hsv(c.color)
        return `${nToHS(Math.round(v[0]), 4)}${nToHS(Math.round(v[1] * 10), 4)}${nToHS(Math.round(v[2] * 10), 4)}00000000`
      }).join('')
    } else {
      const newNodeList = cloneDeep(state.cloudData).map((item, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            if (color.h !== undefined && color.s !== undefined && color.v !== undefined) {
              item.color = `#${getBrightOpacity(color.v)}${ColorUtils.hsv2hex(color.h, Math.max(color.s, 10), 100).slice(1)}`
            } else {
              item.color = getHexByTemp(color.t as number, color.b)
            }
          } else {
            item.color = `#20${whiteHex.slice(1)}`
          }
        }
        return item
      })
      const newHex = avgSplit(state.cloudHex, 20).map((hex, idx) => {
        if (idxList.includes(idx)) {
          if (Object.keys(color).length) {
            if (color.h !== undefined && color.s !== undefined && color.v !== undefined) {
              return `${nToHS(color.h, 4)}${nToHS(color.s * 10, 4)}${nToHS(color.v * 10, 4)}00000000`
            } else {
              return `000000000000${nToHS(color.b as number * 10, 4)}${nToHS(color.t as number * 10, 4)}`
            }
          } else {
            return '00000000000000000000'
          }
        }
        return hex
      }).join('')
      state.cloudData = newNodeList
      state.cloudHex = newHex
    }
  }, [state.ledNum, state.cloudData, state.cloudHex])

  return [hsv, setDrawToolFn, state.cloudData, setColorFn]
}


export function useBrightness(): [number, (v: number) => Promise<Result<any>>] {
  const [bright, setBright]: [number, (v: number) => Promise<Result<any>>] = useDp(dpKC.bright_value.code)
  const setBrightFn = (v: number) => {
    return setBright(v * 10)
  }
  return [Math.round(bright / 10), setBrightFn]
}

export function useTemperature(): [number, (v: number) => Promise<Result<any>>] {
  const [temp, setTemp]: [number, (v: number) => Promise<Result<any>>] = useDp(dpKC.temp_value.code)
  const setTempFn = (v: number) => {
    return setTemp(v * 10)
  }
  return [Math.round(temp / 10), setTempFn]
}
// 这两款strip 灯没有放开 light_pixel
const lightPixelMap = {
  'fgzre3xmrav5cykj': 20,
  '29onzre8htt3mnal': 20,
  'p1larkg5qorapphb': 20,
  'clilldwdqzsp1r15': 4
}

export function useLightPixel(): number {
  const pixel = useDp(dpKC.light_pixel.code)[0] as number
  const devInfo = useDeviceInfo()
  const [lightPixel, setLightPixel] = useState(pixel || 20)
  useEffect(() => {
    if (devInfo.productId && lightPixelMap.hasOwnProperty(devInfo.productId)) {
      setLightPixel(lightPixelMap[devInfo.productId])
    }
  }, [devInfo.productId])
  return Math.min(lightPixel, 20)
}

export function useLightLength(): number {
  return useDp(dpKC.light_length.code)[0] as number
}

export function useLightPixelNumber(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.lightpixel_number_set.code)
}

export function useCountDowns(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.countdown.code)
}

export function useSyncScreen(): [boolean, (v: boolean) => Promise<Result<any>>] {
  return useDp(dpKC.sync_on_screen.code)
}

export function useUpperStrip(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.lamp_top.code)
}

export function useUnderStrip(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.lamp_bottom.code)
}

export function useLeftStrip(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.lamp_left.code)
}

export function useRightStrip(): [number, (v: number) => Promise<Result<any>>] {
  return useDp(dpKC.lamp_right.code)
}


export function isSupportBrightness(): boolean {
  return SupportUtils.isSupportDp(dpKC.bright_value.key)
}

export function isSupportTemperature(): boolean {
  return SupportUtils.isSupportDp(dpKC.temp_value.key)
}

export function isSupportColor(): boolean {
  return SupportUtils.isSupportDp(dpKC.colour_data.key)
}

export function isSupportMood(): boolean {
  return SupportUtils.isSupportDp(dpKC.dreamlight_scene_mode.key)
}

export function isSupportMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.music_data.key)
}

export function isSupportDreamMusic(): boolean {
  return SupportUtils.isSupportDp(dpKC.dreamlightmic_music_data.key)
}

export function isSupportTimer(): boolean {
  return SupportUtils.isSupportDp(dpKC.countdown.key)
}

export function isSupportSyncWithScreen(): boolean {
  return SupportUtils.isSupportDp(dpKC.sync_on_screen.key)
}

export function isSupportPixelNumberSet(): boolean {
  return SupportUtils.isSupportDp(dpKC.lightpixel_number_set.key)
}

const displayFlagPids = ['uodzotak0iagw5xs']
const reverseFlagPids = ['clilldwdqzsp1r15']

export function useAdvancedData(): AdvancedData[] {
  const advanceData: AdvancedData[] = []
  const deviceId = useDeviceId()
  const deviceInfo = useDeviceInfo()
  const [workMode] = useWorkMode()
  const [switchLed] = useSwitch()
  const [countdown] = useCountDowns()
  const [timeSchedule, setTimeSchedule] = useTimeSchedule()
  const ledNum = useLightPixel()
  const [syncScreen] = useSyncScreen()
  const [flagMode, setFlagMode] = useFlagMode()
  const paintDp = usePaintDp()
  const state = useReactive({
    timeScheduleStatus: timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable,
    ledNum
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status)
          setTimeSchedule(status)
        }
      })

      getFlagMode(deviceId).then(res => {
        if (res.success && res.data) {
          const flagData = parseJSON(res.data)
          setFlagMode(flagData)
        }
      })
    }
  }, [deviceId])

  useUpdateEffect(() => {
    if (workMode !== WorkMode.Colour && flagMode?.flagMode) {
      setFlagMode({
        ...flagMode,
        flagMode: false,
      })
      saveFlagMode(deviceId, JSON.stringify({
        ...flagMode,
        flagMode: false
      })).then()
    }

    if (workMode !== WorkMode.Music) {
      MusicManager.close()
    }
  }, [workMode])

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable
  }, [timeSchedule])

  useUpdateEffect(() => {
    state.ledNum = ledNum
  }, [ledNum])

  const manualDataDp2Obj = useCallback((dps: Record<string, any>) => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.StripLight,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: isSupportColor() ? true : false,
          activeKey: 1,
          colorDiskActiveKey: 0,
          colors: []
        },
      },
      isManual: !(
        dps.hasOwnProperty(getGlobalParamsDp('scene_data')) || dps.hasOwnProperty(getGlobalParamsDp('mix_light_scene')) || dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))
      ),
      mood: undefined,
    };
    if (dps.hasOwnProperty(getGlobalParamsDp('paint_colour_data')) || dps.hasOwnProperty(getGlobalParamsDp('paint_color'))){
      const paintDp = getGlobalParamsDp('paint_colour_data') ? dps[getGlobalParamsDp('paint_colour_data')] : dps[getGlobalParamsDp('paint_color')]
      const paintColor = Buffer.from(paintDp, 'base64').toString('hex')
      const paintData: any = drawToolParse(paintColor)
      deviceState.deviceData.deviceData = {
        ...deviceState.deviceData.deviceData,
        ...paintData,
        s: paintData.hasOwnProperty('s') ? Math.trunc(paintData.s) / 10 : 100,
        v: paintData.hasOwnProperty('v') ? Math.trunc(paintData.v) / 10 : 100,
        brightness: paintData.hasOwnProperty('bright') ? Math.trunc(paintData.bright) / 10 : 100,
        temperature: paintData.hasOwnProperty('temp') ? Math.trunc(paintData.temp) / 10 : 0,
        colors: paintData?.colors || [],
        activeKey: paintData.adjustCode ?? 1,
        colorDiskActiveKey: paintData.colorDiskActiveKey ?? 0,
      }
    }

    if (dps.hasOwnProperty(getGlobalParamsDp('dreamlight_scene_mode'))) {
      const mood = stripDp2Obj(Buffer.from(dps[getGlobalParamsDp('dreamlight_scene_mode')], 'base64').toString('hex'));
      deviceState.mood = cloneDeep(mood);
    }

    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (deviceState: DeviceStateType, applyForList: ApplyForItem[]) => {
      const { deviceData, isManual, mood } = deviceState;
      const manualDps = {};
      if (!isManual && mood) {
        manualDps[getGlobalParamsDp('dreamlight_scene_mode')] = Buffer.from(stripObj2Dp(mood as MoodInfo), 'hex').toString(
          'base64'
        );
        manualDps[getGlobalParamsDp('switch_led')] = true;
        manualDps[getGlobalParamsDp('work_mode')] = WorkMode.Scene;
      } else {
        const device = deviceData.deviceData as StripLightData
        applyForList.forEach(apply => {
          manualDps[apply.dp] = apply.enable;
        });
        if (manualDps[getGlobalParamsDp('switch_led')]) {
          const paint_color = drawToolFormat({
            ...device,
            temp: device.temperature,
            bright: device.brightness,
            adjustCode: device.activeKey ?? 1,
            version: 0,
            daubType: 0,
            effect: 1,
            num: ledNum
          })
          if (getGlobalParamsDp('paint_colour_data')){
            manualDps[getGlobalParamsDp('paint_colour_data')] = Buffer.from(paint_color, 'hex').toString('base64')
          }
          if (getGlobalParamsDp('paint_color')){
            manualDps[getGlobalParamsDp('paint_color')] = Buffer.from(paint_color, 'hex').toString('base64')
          }
          manualDps[getGlobalParamsDp('work_mode')] = device.activeKey === 0 ? WorkMode.White : WorkMode.Colour;
        }
      }

      return manualDps;
    },
    []
  );

  if (!displayFlagPids.includes(deviceInfo.productId)) {
    advanceData.push({
      title: I18n.getLang('Feature_devicepanel_flags'),
      dp: { key: 'flag', code: 'flag' },
      icons: res.flag_icon,
      statusColor: getAdvancedStatusColor(flagMode?.flagMode && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      router: {
        key: RouterKey.ui_biz_flag_page,
        params: {
          isSupportColor: isSupportColor(),
          isStripLight: true,
          switchLedCode: dpKC.switch_led.code,
          workModeCode: dpKC.work_mode.code,
          drawToolLight: {
            drawToolCode: paintDp.code,
            drawToolObj2dp: (colors) => {
              const newColors = reverseFlagPids.includes(deviceInfo.productId) ? [...colors].reverse() : [...colors]
              const def = {
                version: 0,
                num: ledNum,
                daubType: 1,
                effect: 0,
                adjustCode: 1,
              }
              const distribute = distributeColorsEvenly(newColors, ledNum)
              const hexArray = newColors.slice(0, distribute.length).map((c, idx) => {
                return drawToolFormat({
                  ...def,
                  h: c.h,
                  s: c.s,
                  v: c.v,
                  selected: distribute[idx] as any
                })
              })
              return hexArray
            },
            drawToolDp2Obj: () => {
              return []
            }
          }
        } as FlagPageProps
      }
    })
  }

  if (isSupportMood()) {
    advanceData.push({
      title: I18n.getLang('mesh_device_detail_mode'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Scene && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.dreamlight_scene_mode,
      router: {
        key: RouterKey.ui_biz_mood,
        params: {
          switchLedDp: dpKC.switch_led.code,
          mainDp: dpKC.dreamlight_scene_mode.code,
          mainWorkMode: dpKC.work_mode.code,
          mainSwitch: dpKC.switch_led.code,
          isSupportColor: isSupportColor(),
          isStripLight: true,
          featureId: dpKC.dreamlight_scene_mode.key
        } as MoodPageParams,
      },
    })
  }

  const lightApplyFor: ApplyForItem[] = [
    {
      type: 'light',
      name: I18n.getLang('Onoff_button_socket'),
      key: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
      dp: getGlobalParamsDp('switch_led'),
      enable: true,
    },
  ];

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: lightApplyFor,
        isSupportBrightness: isSupportBrightness(),
        isSupportColor: isSupportColor(),
        isSupportTemperature: isSupportTemperature(),
        isSupportMood: isSupportMood(),
        isStripLight: true,
        featureId: 'dreamlight_scene_mode',
        manualDataDp2Obj,
        manualDataObj2Dp
      } as TimeSchedulePageParams
    }
  })

  if (isSupportMusic()) {
    advanceData.push({
      title: I18n.getLang('devicemusic_headline_text'),
      statusColor: getAdvancedStatusColor(workMode === WorkMode.Music && switchLed ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.music_data,
      router: {
        key: RouterKey.ui_biz_music,
        params: {
          switch_led: dpKC.switch_led.code,
          work_mode: dpKC.work_mode.code,
          music_data: dpKC.music_data.code,
          isMixRGBWLamp: false,
          dreamMusicDp: isSupportDreamMusic() ? dpKC.dreamlightmic_music_data.code : undefined
        } as MusicPageRouterParams,
      },
    })
  }

  if (isSupportTimer()) {
    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: countdown > 0 ? [I18n.formatValue(switchLed ? 'ceiling_fan_feature_2_light_text_min_off' : 'ceiling_fan_feature_2_light_text_min_on', timeFormat(countdown, true))] : [],
      statusColor: getAdvancedStatusColor(countdown > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: dpKC.countdown,
      router: {
        key: RouterKey.ui_biz_timer,
        params: {
          dps: [
            {
              label: I18n.getLang('timeschedule_add_schedule_nightlight_plug_selectionfield_text2'),
              value: 'lighting',
              dpId: dpKC.countdown.code,
              enableDp: dpKC.switch_led.code,
              cloudKey: 'lightingInfo',
              stringOn: 'timer_ceiling_fan_lighting_switched_on_text',
              stringOff: 'timer_ceiling_fan_lighting_switched_off_text',
            },
          ],
        },
      },
    })
  }

  if (isSupportSyncWithScreen()) {
    advanceData.push({
      title: I18n.getLang('strip_lights_headline_text'),
      dp: dpKC.sync_on_screen,
      statusColor: getAdvancedStatusColor(syncScreen ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      router: {
        key: RouterKey.sync_screen
      }
    })
  }

  if (isSupportPixelNumberSet()) {
    advanceData.push({
      title: I18n.getLang('striplight_lengthtitle'),
      dp: dpKC.lightpixel_number_set,
      statusColor: '',
      router: {
        key: RouterKey.strip_light_length
      }
    })
  }
  return advanceData
}

function distributeColorsEvenly(colors: any[], num: number){
  const colorLen = colors.length
  const colorDistribution = colors.map(() => Math.floor(num / colorLen))
  const overDistribution = num >= colorLen ? num % colorLen : 0
  const finalDistribution = colorDistribution.map((dist, idx) => {
    if (overDistribution !== 0) {
      // 判断余数是否为奇数
      const isEven = overDistribution % 2 === 0
      const midIdx = Math.floor(colorLen / 2)
      let startIdx = midIdx
      let endIdx = midIdx
      const m = Math.floor(overDistribution / 2)
      if (overDistribution > 1) {
        startIdx = midIdx - m
        endIdx = midIdx + m - (isEven ? 1 : 0)
      }
      if (idx >= startIdx && idx <= endIdx) {
        return dist + 1
      }
      return dist
    }
    if (idx <= num - 1 && dist === 0) {
      return 1
    }
    return dist
  }).filter(v => v !== 0)
  const getReduceIndex = (numList: number[], idx: number) => {
    return numList.reduce((pre, cur, index) => {
      if (idx > index) {
        return pre + cur
      }
      return pre
    }, 0)
  }
  console.log(finalDistribution, '< --- finalDistribution')
  const distributeIndex = finalDistribution.map((n, idx) => {
    const index = getReduceIndex(finalDistribution, idx)
    return range(index, index + n)
  })
  return distributeIndex
}

