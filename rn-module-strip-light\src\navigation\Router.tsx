import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import SyncScreenPage from 'pages/syncScreen/SyncScreenPage'
import StripLightLengthPage from 'pages/stripLightLength/StripLightLengthPage'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import MusicPageRouters from '@ledvance/ui-biz-bundle/src/modules/music/Router'
import FlagPageRouters from '@ledvance/ui-biz-bundle/src/modules/flags/Router'
import MoodPageRouters from '@ledvance/ui-biz-bundle/src/newModules/mood/Router'
import SelectPageRouters from '@ledvance/ui-biz-bundle/src/newModules/select/Route'

export const RouterKey = {
  main: 'main',
  sync_screen: 'sync_screen',
  strip_light_length: 'strip_light_length',
  flag_page: 'flag_page',
  flag_page_edit: 'flag_page_edit',
  ...ui_biz_routerKey,
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.sync_screen,
    component: SyncScreenPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.strip_light_length,
    component: StripLightLengthPage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...TimeSchedulePageRouters,
  ...TimerPageRouters,
  ...MusicPageRouters,
  ...FlagPageRouters,
  ...MoodPageRouters,
  ...SelectPageRouters
]
