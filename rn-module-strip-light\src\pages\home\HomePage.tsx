import React, { useCallback, useEffect } from 'react'
import { ScrollView, View } from 'react-native'
import Page from '@ledvance/base/src/components/Page'
import { useDeviceId, useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import { NativeApi } from '@ledvance/base/src/api/native'
import {
  WorkMode,
  dpKC,
  isSupportBrightness,
  isSupportColor,
  isSupportPixelNumberSet,
  isSupportSyncWithScreen,
  isSupportTemperature,
  useAdvancedData,
  useBrightness,
  useColorData,
  useLightPixel,
  usePaintColorData,
  useSwitch,
  useSyncScreen,
  useTemperature,
  useWorkMode,
} from '../../hooks/FeatureHooks'
import {useCreation, useReactive, useThrottleFn, useUpdateEffect} from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import { useNavigation } from '@react-navigation/core'
import DrawToolView from '@ledvance/base/src/components/DrawToolView'
import { range } from 'lodash'
import LampSwitchCard from 'components/LampSwitchCard'
import I18n from '@ledvance/base/src/i18n'
import { RouterKey } from 'navigation/Router'
import { hsv2Hex, mapFloatToRange } from '@ledvance/base/src/utils'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import { ColorList } from '@ledvance/ui-biz-bundle/src/modules/timeSchedule/components/ColorList'
import { StorageUtils } from '@tuya/tuya-panel-lamp-sdk/lib/utils'
import { getGlobalParamsDp, showDialog } from '@ledvance/base/src/utils/common'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import { Utils } from 'tuya-panel-kit';
import ThemeType from '@ledvance/base/src/config/themeType'
import { useIsFocused } from '@react-navigation/core'
import { useGestureControl } from '@ledvance/base/src/models/modules/NativePropsSlice'
import { sendAppEvent } from '@ledvance/base/src/api/native'
import { useDpResponseValidator } from '@ledvance/base/src/hooks/Hooks'

const { withTheme } = Utils.ThemeUtils
type AdjustType = 1 | 2 | 3
const HomePage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const devId = useDeviceId()
  const familyName = useFamilyName()
  const [switchLed, setSwitchLed] = useSwitch()
  const [brightness] = useBrightness()
  const [temperature] = useTemperature()
  const [hsv] = useColorData()
  const ledNum = useLightPixel()
  const [painData, setPainData, ledColorList, setColorFn] = usePaintColorData()
  const [syncScreen] = useSyncScreen()
  const [workMode] = useWorkMode()
  const isFocused = useIsFocused()
  const [gestureSwitch] = useGestureControl('switch')
  const [gestureHue] = useGestureControl('hue')
  const [gestureBrightness] = useGestureControl('brightness')
  const advanceData = useAdvancedData()
  const navigation = useNavigation()
  const { sendDpWithTimestamps, onDpResponse } = useDpResponseValidator();
  const tabList = useCreation(() => {
    const isSupportWhite = isSupportBrightness() || isSupportTemperature()

    const tabs = [
      { key: 1, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_color_text') },
      { key: 0, title: I18n.getLang('add_new_static_mood_lights_schedule_switch_tab_white_text') },
      { key: 3, title: I18n.getLang('add_new_dynamic_mood_strip_lights_schedule_switch_tab_combination_text') },
    ]
    if (!isSupportWhite) {
      return tabs.filter(tab => tab.key !== 0)
    }
    if (!isSupportColor()) {
      return tabs.filter(tab => tab.key === 0)
    }
    return tabs
  }, [])

  const state = useReactive({
    ...hsv,
    brightness,
    temperature,
    loading: false,
    adjustType: 0,
    touchIdx: undefined as undefined | number,
    touchIdxList: '',
    activeKey: painData.adjustCode === 2 ? 1 : painData.adjustCode,  // 0 white, 1 color 3 combination
    colorDiskActiveKey: painData.colorDiskActiveKey,
    colorDisk: [] as string[],
    flag: Symbol(),
  })

  useEffect(() => {
    sendAppEvent('GestureControl', { enabled: isFocused })
  }, [isFocused])

  useEffect(() => {
    if (isSupportSyncWithScreen() && syncScreen) {
      navigation.navigate(RouterKey.sync_screen)
    }
  }, [syncScreen])

  useEffect(() => {
    const getSetNumberStatus = async () => {
      const storage = await StorageUtils.getDevItem(dpKC.lightpixel_number_set.key)
      if (storage) return
      const nativeRes = await NativeApi.getJson(devId, dpKC.lightpixel_number_set.key)
      if (!nativeRes.success && nativeRes.msg?.includes('Other Exceptions')) {
        NativeApi.putJson(devId, dpKC.lightpixel_number_set.key, '1').then()
        StorageUtils.setDevItem(dpKC.lightpixel_number_set.key, 1).then()
        return showDialog({
          method: 'confirm',
          title: I18n.getLang('striplight_lengthadaptationtext'),
          cancelText: I18n.getLang('bt_shs_google_button_cancel_enabling'),
          confirmText: I18n.getLang('striplight_adaptbutton'),
          subTitle: '',
          onConfirm: (_, { close }) => {
            close()
            navigation.navigate(RouterKey.strip_light_length)
          }
        })
      }
    }

    if (isSupportPixelNumberSet() && switchLed) {
      getSetNumberStatus().then()
    }
  }, [switchLed])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('colour_data'))
    if (!shouldBlock) {
      state.h = hsv.h
      state.s = hsv.s
      state.v = hsv.v
    }
  }, [hsv])

  useUpdateEffect(() => {
    state.activeKey = painData.adjustCode === 2 ? 1 : painData.adjustCode
    state.colorDiskActiveKey = painData.colorDiskActiveKey
    state.touchIdxList = ''
    state.touchIdx = undefined
  }, [painData])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('bright_value'))
    if (!shouldBlock) {
      state.brightness = brightness
    }
  }, [brightness])

  useUpdateEffect(() => {
    const shouldBlock = onDpResponse(getGlobalParamsDp('temp_value'))
    if (!shouldBlock) {
      state.temperature = temperature
    }
  }, [temperature])

  useUpdateEffect(() => {
    if (workMode === WorkMode.Music || workMode === WorkMode.Scene) {
      state.adjustType = 0
    }
  }, [workMode])

  useEffect(() => {
    if (isFocused && gestureSwitch !== undefined) {
      setSwitchLed(gestureSwitch).then()
    }
  }, [isFocused, gestureSwitch])

  useEffect(() => {
    const isColorMode = state.activeKey === 1
    if (!isFocused || !isColorMode || state.adjustType !== 0 || gestureHue === undefined) {
      return
    }
    state.h = gestureHue
    setColorFn({ h: state.h, s: state.s, v: state.v }, range(ledNum))
    run()
  }, [isFocused, state.activeKey, state.adjustType, gestureHue])

  useEffect(() => {
    if (!isFocused || gestureBrightness === undefined) {
      return
    }
    const isColorMode = state.activeKey === 1
    if (isColorMode) {
      state.v = gestureBrightness
    } else {
      state.brightness = gestureBrightness
    }
    run()
  }, [isFocused, state.activeKey, gestureBrightness])

  const sendCommand = (idxList?: string) => {
    const subscript = idxList || state.touchIdxList
    const params: any = state.adjustType !== 2 ? (
      state.activeKey === 3 ?
        {
          colors: state.colorDisk
        } :
        state.activeKey === 0 ?
          {
            bright: state.brightness,
            temp: state.temperature
          } :
          {
            h: state.h,
            s: state.s,
            v: state.v,
            selected: subscript ? JSON.parse(subscript) : []
          }
    ) :
      {
        selected: subscript ? JSON.parse(subscript) : []
      }
    if (params.hasOwnProperty('bright')) {
      sendDpWithTimestamps(getGlobalParamsDp('bright_value'), async () => {}).then()
      sendDpWithTimestamps(getGlobalParamsDp('temp_value'), async () => {}).then()
    }
    if (params.hasOwnProperty('h')) {
      sendDpWithTimestamps(getGlobalParamsDp('colour_data'), async () => {}).then()
    }
    setPainData({
      ...params,
      daubType: state.adjustType,
      adjustCode: state.activeKey
    }).then()
  }

  const { run } = useThrottleFn((idxList?: string) => {
    sendCommand(idxList)
  }, { wait: 500 })

  useUpdateEffect(() => {
    sendCommand()
  }, [state.flag])

  const getBlockColor = useCallback(() => {
    if (state.activeKey === 1) {
      const s = Math.round(mapFloatToRange(state.s / 100, 30, 100))
      return hsv2Hex(state.h, s, 100)
    }
    if (state.activeKey === 0) {
      return cctToColor(state.temperature)
    }
    return props.theme?.card.background
  }, [state.activeKey, state.h, state.s, state.v, state.brightness, state.temperature])

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}>
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          <DrawToolView
            adjustType={state.adjustType + 1 as AdjustType}
            stripStyle={'ONLY_LINE'}
            setAdjustType={(t) => {
              if (state.adjustType !== t - 1) {
                state.adjustType = t - 1
                state.touchIdx = undefined
              }
            }}
            hidLampAdjustView={true}
            hideColorize={state.activeKey !== 1}
            hideDisableLight={state.activeKey !== 1}
            nodes={ledColorList}
            fixCount={5}
            nodeTouch={(idx) => {
              if (idx !== state.touchIdx && state.adjustType !== 0) {
                state.touchIdx = idx
                const color = state.adjustType === 2 ? {} : state.activeKey === 0 ? { b: state.brightness, t: state.temperature } : { h: state.h, s: state.s, v: state.v }
                setColorFn(color, [idx])
              }
            }}
            fingerUp={(idxList) => {
              if ((idxList !== state.touchIdxList) && state.adjustType !== 0) {
                state.touchIdxList = idxList
                sendCommand(idxList)
              }
            }}
            switchLed={switchLed}
            showEnable={true}
            setEnable={async (enable: boolean) => {
              state.loading = true
              await setSwitchLed(enable)
              state.loading = false
            }}
            isColorMode={state.activeKey === 1}
            setIsColorMode={() => { }}
            blockColor={getBlockColor()}
            h={state.h} s={state.s} v={state.v}
            onHSVChange={() => { }}
            onHSVChangeComplete={() => { }}
            temperature={state.temperature}
            brightness={state.brightness}
            onCCTChange={() => { }}
            onCCTChangeComplete={() => { }}
            onBrightnessChange={() => { }}
            onBrightnessChangeComplete={() => { }}
            hideLedNum={true}
            setLedNum={() => { }}
          >
            <LampSwitchCard
              lampTabs={tabList}
              onColorDiskChange={(color, idx) => {
                state.colorDisk = color
                setColorFn(color, [])
                state.colorDiskActiveKey = idx
                state.flag = Symbol()
              }}
              colorDiskActiveKey={state.colorDiskActiveKey}
              activeKey={state.activeKey}
              onActiveKeyChange={(v) => {
                state.adjustType = 0
                state.activeKey = Number(v)
                state.colorDiskActiveKey = state.colorDiskActiveKey ?? 0
                state.colorDisk = ColorList[state.colorDiskActiveKey]
                const color = Number(v) === 3 ? state.colorDisk : Number(v) === 0 ? { b: state.brightness, t: state.temperature } : { h: state.h, s: state.s, v: state.v }
                const idx = Number(v) === 3 ? [] : range(ledNum)
                setColorFn(color, idx)
                state.flag = Symbol()
              }}
              isSupportTemperature={isSupportTemperature()}
              isSupportBrightness={isSupportBrightness()}
              h={state.h} s={state.s} v={state.v}
              onHSVChange={() => { }}
              onHSVChangeComplete={async (h, s, v) => {
                state.h = h
                state.s = s
                state.v = v
                state.touchIdx = undefined
                if (state.adjustType === 0) {
                  setColorFn({ h: state.h, s: state.s, v: state.v }, range(ledNum))
                  state.flag = Symbol()
                }
              }}
              colorTemp={state.temperature}
              brightness={state.brightness}
              onCCTChange={(cct) => {
                state.temperature = cct
              }}
              onCCTChangeComplete={(temperature) => {
                state.temperature = temperature
                state.touchIdx = undefined
                if (state.adjustType === 0) {
                  setColorFn({ b: state.brightness, t: state.temperature }, range(ledNum))
                  state.flag = Symbol()
                }
              }}
              onBrightnessChange={(bright) => {
                state.brightness = bright
              }}
              onBrightnessChangeComplete={brightness => {
                state.brightness = brightness
                state.touchIdx = undefined
                if (state.adjustType === 0) {
                  setColorFn({ b: state.brightness, t: state.temperature }, range(ledNum))
                  state.flag = Symbol()
                }
              }}
            />
          </DrawToolView>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
