import React, { useEffect } from 'react'
import { View, Text, StyleSheet, BackHandler } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { useReactive } from 'ahooks'
import { Slider, Utils } from 'tuya-panel-kit'
import {useDeviceInfo, useDp} from '@ledvance/base/src/models/modules/NativePropsSlice'
import Page from '@ledvance/base/src/components/Page'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import InfoText from '@ledvance/base/src/components/InfoText'
import res from '@ledvance/base/src/res'
import Card from '@ledvance/base/src/components/Card'
import {dpKC, useLightLength, useLightPixelNumber, useWorkMode} from 'hooks/FeatureHooks'
import DeleteButton from '@ledvance/base/src/components/DeleteButton'
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils
const minimumValue = 100

function useLightPixel(): number {
  return useDp<number, any>(dpKC.light_pixel.code)[0]
}

const StripLightLengthPage = (props: { theme?: ThemeType }) => {
  const lightLength = useLightLength()
  const originPixel = useLightPixel()
  const [lightPixel, setLightPixel] = useLightPixelNumber()
  const [workMode, setWorkMode] = useWorkMode()
  const devInfo = useDeviceInfo()
  const navigation = useNavigation()
  const state = useReactive({
    ratio: originPixel / (lightLength / 100) / 10,
    isSlicePixel: false,
    initPixel: lightPixel * 10 || 100,
    lightPixel: lightPixel * 10 || 100,
    flag: Symbol()
  })

  useEffect(() => {
    if (lightPixel) {
      state.lightPixel = Math.round((lightPixel / state.ratio) * 10)
    }
  }, [lightPixel])

  const onGoBack = async () => {
    if (state.isSlicePixel) {
      setLightPixel(Math.round((state.initPixel / 10)  * state.ratio)).then(() =>{
        setTimeout(() =>{
          setWorkMode(workMode).then()
        }, 10)
      })
      navigation.goBack()
    } else {
      navigation.goBack()
    }
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onGoBack)
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onGoBack)
    }
  }, [])

  return (
    <Page
      backText={devInfo.name}
      headlineText={I18n.getLang('striplight_lengthtitle')}
      onBackClick={onGoBack}
    >
      <View style={styles.stripLightLengthContainer}>
        <InfoText
          icon={res.ic_info}
          text={I18n.getLang('striplight_lengthadaptationtext')} />
        <Spacer />
      </View>
      <Card
        style={[styles.stripLightLengthContainer]}
        containerStyle={{
          paddingHorizontal: cx(16),
          width: '100%'
        }}
      >
        <Spacer />
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ color: props.theme?.global.fontColor }}>{I18n.getLang('striplight_actuallength')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ color: props.theme?.icon.primary, fontSize: cx(16), marginRight: cx(5) }}>{(state.lightPixel / 100).toFixed(2)}</Text>
            <Text style={{ color: props.theme?.global.secondFontColor }}>m</Text>
          </View>
        </View>
        <Spacer height={cx(20)} />
        <Slider.Horizontal
          theme={{
            trackRadius: 16,
            trackHeight: 46,
            thumbSize: 20,
            thumbRadius: 20,
            thumbTintColor: '#F84803',
            minimumTrackTintColor: '#F84803',
            maximumTrackTintColor: '#E5E5E5',
          }}
          trackStyle={{
            height: 35,
            borderRadius: 8,
          }}
          value={state.lightPixel}
          style={{ marginBottom: 10 }}
          stepValue={10}
          thumbStyle={{
            shadowOffset: {
              width: 0,
              height: 0,
            },
            shadowOpacity: 0,
            shadowRadius: 0,
            elevation: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          type="parcel"
          canTouchTrack={true}
          useNoun={true}
          maximumValue={lightLength}
          minimumValue={minimumValue}
          minNounStyle={{ backgroundColor: 'white' }}
          maxNounStyle={{ backgroundColor: '#F84803' }}
          onSlidingComplete={async v => {
            state.lightPixel = v
            state.isSlicePixel = true
            await setLightPixel(Math.round((v / 10) * state.ratio))
          }}
          onValueChange={(v) => {
            state.lightPixel = v
          }}
        />
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: -8 }}>
          <Text style={{ color: props.theme?.global.secondFontColor }}>1.00 m</Text>
          <Text style={{ color: props.theme?.global.secondFontColor }}>{(lightLength / 100).toFixed(2)} m</Text>
        </View>
        <Spacer />
      </Card>
      <Spacer height={cx(30)} />
      <View style={[styles.stripLightLengthContainer, { flexDirection: 'row' }]}>
        <DeleteButton
          text={I18n.getLang('bt_shs_google_button_cancel_enabling')}
          onPress={onGoBack}
          style={{ width: cx(150),  backgroundColor: props.theme?.button.cancel, flex: 1, marginRight: cx(20) }}
        />
        <DeleteButton
          text={I18n.getLang('auto_scan_system_wifi_confirm')}
          onPress={() => {
            setWorkMode(workMode).then()
            navigation.goBack()
          }}
          style={{ width: cx(150), backgroundColor: props.theme?.button.primary, flex: 1 }}
        />
      </View>
    </Page>
  )
}

const styles = StyleSheet.create({
  stripLightLengthContainer: {
    marginHorizontal: cx(24),
  }
})
export default withTheme(StripLightLengthPage)
