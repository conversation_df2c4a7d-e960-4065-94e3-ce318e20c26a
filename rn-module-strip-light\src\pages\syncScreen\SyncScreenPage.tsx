import React, { useRef, useEffect } from 'react'
import { View, Text, SafeAreaView, Animated, StyleSheet, Easing, Image } from 'react-native'
import {useNavigation} from '@react-navigation/native'
import { useReactive, useUpdateEffect } from 'ahooks'
import { Utils } from 'tuya-panel-kit'
import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import Page from '@ledvance/base/src/components/Page'
import LdvSlider from '@ledvance/base/src/components/ldvSlider'
import DeleteButton from '@ledvance/base/src/components/DeleteButton'
import Spacer from '@ledvance/base/src/components/Spacer'
import { showDialog } from '@ledvance/base/src/utils/common'
import { useLeftStrip, useRightStrip, useSyncScreen, useUnderStrip, useUpperStrip } from 'hooks/FeatureHooks'
import res from './res'
import I18n from '@ledvance/base/src/i18n'
import { NativeApi } from '@ledvance/base/src/api/native'
import ThemeType from "@ledvance/base/src/config/themeType";
const { convertX: cx } = Utils.RatioUtils
const {withTheme} = Utils.ThemeUtils

const SyncScreenPage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const navigation = useNavigation()
  const [syncScreen, setSyncScreen] = useSyncScreen()
  const [upperStrip, setUpperStrip] = useUpperStrip()
  const [underStrip, setUnderStrip] = useUnderStrip()
  const [leftStrip, setLeftStrip] = useLeftStrip()
  const [rightStrip, setRightStrip] = useRightStrip()
  const spinValue = useRef(new Animated.Value(0)).current;
  const state = useReactive({
    upperStrip,
    underStrip,
    leftStrip,
    rightStrip,
    flag: Symbol()
  })

  useUpdateEffect(() => {
    state.upperStrip = upperStrip
  }, [upperStrip])

  useUpdateEffect(() => {
    state.underStrip = underStrip
  }, [underStrip])

  useUpdateEffect(() => {
    state.leftStrip = leftStrip
  }, [leftStrip])

  useUpdateEffect(() => {
    state.rightStrip = rightStrip
  }, [rightStrip])


  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  useEffect(() => {
    const spinAnimation = Animated.timing(spinValue, {
      toValue: 1,
      duration: 2000,
      easing: Easing.linear,
      useNativeDriver: true,
    });

    const loopedAnimation = Animated.loop(spinAnimation);

    if(syncScreen){
      loopedAnimation.start();
    }else{
      loopedAnimation.stop();
    }

    return () => loopedAnimation.stop();
  }, [syncScreen]);

  const styles = StyleSheet.create({
    syncingContainer: {
      flex: 1,
      alignItems: 'center'
    },
    syncingView: {
      // borderColor: props.theme?.card.border,
      // borderWidth: cx(4),
      alignItems: 'center',
      justifyContent: 'center'
    }
  })

  return (
    <Page
      style={{ position: 'relative' }}
      backText={syncScreen ? familyName : devInfo.name}
      headlineText={I18n.getLang('strip_lights_headline_text')}
      onBackClick={syncScreen ? NativeApi.back : undefined}
    >
      {!syncScreen ?
        <><LdvSlider
          title={I18n.getLang('strip_lights_top_strip_light_text')}
          style={{ marginHorizontal: cx(8) }}
          value={state.upperStrip}
          min={1}
          max={8}
          subTitleStr={state.upperStrip}
          onValueChange={(v) => {
            state.upperStrip = v
          }}
          onSlidingComplete={async (v) => {
            await setUpperStrip(v)
          }}
        />
          <Spacer height={cx(10)} />
          <LdvSlider
            title={I18n.getLang('strip_lights_bottom_strip_light_text')}
            style={{ marginHorizontal: cx(8) }}
            value={state.underStrip}
            min={9}
            max={16}
            subTitleStr={state.underStrip}
            onValueChange={(v) => {
              state.underStrip = v
            }}
            onSlidingComplete={async (v) => {
              await setUnderStrip(v)
            }}
          />
          <Spacer height={cx(10)} />
          <LdvSlider
            title={I18n.getLang('strip_lights_left_strip_light_text')}
            style={{ marginHorizontal: cx(8) }}
            value={state.leftStrip}
            min={1}
            max={8}
            subTitleStr={state.leftStrip}
            onValueChange={(v) => {
              state.leftStrip = v
            }}
            onSlidingComplete={async (v) => {
              await setLeftStrip(v)
            }}
          />
          <Spacer height={cx(10)} />
          <LdvSlider
            title={I18n.getLang('strip_lights_right_strip_light_text')}
            style={{ marginHorizontal: cx(8) }}
            value={state.rightStrip}
            min={9}
            max={16}
            subTitleStr={state.rightStrip}
            onValueChange={(v) => {
              state.rightStrip = v
            }}
            onSlidingComplete={async (v) => {
              await setRightStrip(v)
            }}
          />
          <Spacer />
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              style={{ backgroundColor: props.theme?.button.primary }}
              text={I18n.getLang('strip_lights_start_sync_text')}
              onPress={async () => {
                await setSyncScreen(true)
              }}
            />
          </View></> :
        <View style={[
          styles.syncingContainer,
          // { top: cx(60) }
        ]}>
          <Spacer height={cx(50)} />
          <SafeAreaView style={{ alignItems: 'center', justifyContent: 'center' }}>
            <Animated.View
              style={[
                styles.syncingView,
                {
                  transform: [{ rotate: spin }]
                }
              ]}
            >
              <Image source={res.screen_sync_loading} width={cx(200)} height={cx(200)} />
            </Animated.View>
            <Text style={{ color: props.theme?.global.fontColor, position: 'absolute' }}>{I18n.getLang('strip_lights_await_sync_text')}</Text>
          </SafeAreaView>
          <Spacer height={cx(50)} />
          <DeleteButton
            style={{ width: 'auto', paddingHorizontal: cx(10)}}
            text={I18n.getLang('cancel_dialog_exit_sync_title')}
            onPress={() => {
              showDialog({
                method: 'confirm',
                title: I18n.getLang('cancel_dialog_exit_sync_title'),
                subTitle: I18n.getLang('cancel_dialog_exit_sync_description'),
                onConfirm: async (_, { close }) => {
                  await setSyncScreen(false)
                  navigation.goBack()
                  close()
                }
              })
            }}
          />
        </View>
      }
    </Page>
  )
}

export default withTheme(SyncScreenPage)
