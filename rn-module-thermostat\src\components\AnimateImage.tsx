import React, { useRef } from "react";
import { Animated, TouchableWithoutFeedback, View, StyleSheet } from "react-native";
import { Utils } from 'tuya-panel-kit'
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

interface AnimatedImageProps {
  theme?: ThemeType
  source: number | string
  duration?: number
  onPress: () => void
}

const AnimateImage = (props: AnimatedImageProps) => {
  const rotation = useRef(new Animated.Value(0)).current;
  const startRotation = () => {
    rotation.setValue(0); // 重置旋转角度

    Animated.timing(rotation, {
      toValue: 3, // 目标值为1，表示旋转一圈（360度）
      duration: props.duration ?? 4000, // 动画持续3秒钟
      useNativeDriver: true, // 启用原生驱动
    }).start();
  };

  // 创建旋转插值
  const rotateInterpolate = rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const styles = StyleSheet.create({
    image: {
      width: cx(28),
      height: cx(28),
      tintColor: props.theme?.global.brand
    },
  });

  const rotateStyle = {
    transform: [{ rotate: rotateInterpolate }],
  };

  return (
    <View>
      <TouchableWithoutFeedback onPress={() => {
        props.onPress()
        startRotation()
      }}>
        <Animated.Image
          source={props.source} // 替换为你的图片URL
          style={[styles.image, rotateStyle]}
        />
      </TouchableWithoutFeedback>
    </View>
  );
}

export default withTheme(AnimateImage)
