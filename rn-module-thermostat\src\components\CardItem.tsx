import Card from '@ledvance/base/src/components/Card';
import Spacer from '@ledvance/base/src/components/Spacer';
import React, { PropsWithChildren } from 'react';
import { View, Text, StyleSheet, ViewProps } from 'react-native';
import { IconFont, SwitchButton, Utils } from 'tuya-panel-kit';
import ThemeType from "@ledvance/base/src/config/themeType";
const { convertX: cx } = Utils.RatioUtils;
const {withTheme} = Utils.ThemeUtils

interface CardItemProps extends PropsWithChildren<ViewProps> {
  theme?: ThemeType
  title: string;
  content: string;
  describe?: Element
  showEnable?: boolean
  enable?: boolean
  onEnableChange?: (v: boolean) => void
  onPress: () => void;
}

const CardItem = (props: CardItemProps) => {

  const styles = StyleSheet.create({
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      paddingVertical: cx(16),
      maxWidth: cx(220),
    },
    itemContent: {
      color: props.theme?.global.secondFontColor,
      maxWidth: cx(100),
      fontSize: cx(14),
    },
    item: {
      marginHorizontal: cx(24),
    },
    itemContainerStyle: {
      paddingHorizontal: cx(10),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
  })

  return (
    <Card style={styles.item} onPress={props.onPress}>
      <View style={styles.itemContainerStyle}>
        <Text style={styles.itemTitle}>{props.title}</Text>
        <View style={{ flex: 1 }} />
        <Text style={styles.itemContent} numberOfLines={1}>
          {props.content}
        </Text>
        <Spacer width={cx(2)} />
        <IconFont name={'arrow'} color={props.theme?.global.brand} size={cx(18)} />
        {props.showEnable && <>
          <Spacer width={cx(5)} />
          <SwitchButton
            value={props.enable}
            thumbStyle={{ elevation: 0 }}
            onValueChange={(v) => {
              props.onEnableChange && props.onEnableChange(v)
            }}
          />
        </>}
      </View>
      {props.children && props.children}
    </Card>
  );
};

export default withTheme(CardItem)
