import React, { useRef } from "react";
import { Text, View, Image, TouchableOpacity, StyleSheet, TextInput } from 'react-native'
import Card from "@ledvance/base/src/components/Card";
import Spacer from "@ledvance/base/src/components/Spacer";
import { TabBar, Utils } from 'tuya-panel-kit';
import res from "@ledvance/base/src/res";
import { useReactive, useUpdateEffect } from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import { ModeType } from "hooks/FeatureHooks";
import { exchangeNumber, localeNumber } from "@ledvance/base/src/utils/common";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface ThermostatCardProps {
  theme?: ThemeType
  tempValue: number
  currentTemp: number
  showMode?: boolean
  disableControl?: boolean
  activeMode?: ModeType
  isRapidHeating?: boolean  // 快速升温
  rapidHeating?: React.ReactNode
  onActiveKeyChange?: (v: ModeType) => void
  onTempChange: (v: number) => void
}

const Tabs = [
  { key: 'auto', title: I18n.getLang('thermostat_automatictab') },
  { key: 'manual', title: I18n.getLang('thermostat_powermode') },
  { key: 'holiday', title: I18n.getLang('thermostat_vacationmode') }
]

function getLocaleNumber(n: number) {
  return localeNumber(n, Number.isInteger(n) ? 0 : 1)
}

const ThermostatCard = (props: ThermostatCardProps) => {
  const intervalRef = useRef<any>(null);
  const timeoutRef = useRef<any>(null);
  const state = useReactive({
    isKeyboard: false,
    tempValue: getLocaleNumber(props.tempValue) as any
  })

  useUpdateEffect(() => {
    state.tempValue = getLocaleNumber(props.tempValue)
  }, [props.tempValue])

  const startIncrement = () => {
    const temp = Math.min(Number(exchangeNumber(state.tempValue)) + 0.5, 29.5)
    state.tempValue = getLocaleNumber(temp)
    timeoutRef.current = setTimeout(() => {
      intervalRef.current = setInterval(() => {
        const temp = Math.min(Number(exchangeNumber(state.tempValue)) + 0.5, 29.5)
        state.tempValue = getLocaleNumber(temp)
      }, 150); // 调整100为增加速度
    }, 500)
  };

  const stopIncrement = () => {
    clearTimeout(timeoutRef.current);
    clearInterval(intervalRef.current);
    props.onTempChange(Number(exchangeNumber(state.tempValue)))
  };

  const startDecrement = () => {
    const temp = Math.max(Number(exchangeNumber(state.tempValue)) - 0.5, 0.5)
    state.tempValue = getLocaleNumber(temp)
    timeoutRef.current = setTimeout(() => {
      intervalRef.current = setInterval(() => {
        const temp = Math.max(Number(exchangeNumber(state.tempValue)) - 0.5, 0.5)
        state.tempValue = getLocaleNumber(temp)
      }, 150); // 调整100为减少速度
    }, 500)
  };

  const stopDecrement = () => {
    clearTimeout(timeoutRef.current);
    clearInterval(intervalRef.current);
    props.onTempChange(Number(exchangeNumber(state.tempValue)))
  };

  const styles = StyleSheet.create({
    contactCard: {
      marginHorizontal: cx(24),
      paddingHorizontal: cx(16),
    },
    contentText: {
      color: props.theme?.global.fontColor,
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
  })

  return (
    <Card style={styles.contactCard}>
      <Spacer />
      <Text style={[styles.contentText, { fontSize: cx(16) }]}>
        {I18n.getLang('thermostat_title')}
      </Text>
      {props.showMode &&
        <>
          <Spacer height={cx(5)} />
          <TabBar
            type='radio'
            tabs={Tabs}
            style={{ borderRadius: cx(8), backgroundColor: props.theme?.segment.background, height: cx(40), marginHorizontal: cx(16) }}
            tabTextStyle={{ color: props.theme?.segment.fontColor }}
            tabActiveTextStyle={{ color: props.theme?.segment.fontColor, fontWeight: 'bold' }}
            activeColor={props.theme?.segment.active}
            activeKey={props.activeMode}
            onChange={props.onActiveKeyChange && props.onActiveKeyChange}
          />
          <Spacer height={cx(5)} />
        </>
      }
      <View style={{ alignItems: 'center' }}>
        <Spacer />
        {!props.disableControl && !props.isRapidHeating ? <TouchableOpacity
          onPressIn={startIncrement}
          onPressOut={stopIncrement}
        >
          <Image
            style={{
              width: cx(60),
              height: cx(60),
              tintColor: props.theme?.global.brand
            }}
            source={{ uri: res.arrow_temp_up}}
          />
        </TouchableOpacity> : <Spacer height={cx(35)} />}
        <Spacer height={cx(16)} />
        {props.isRapidHeating ?
          props.rapidHeating :
          <TouchableOpacity onPress={() => {
            if (props.disableControl) return
            state.isKeyboard = !state.isKeyboard
          }}>
            {state.isKeyboard ? <TextInput
              autoFocus={true}
              style={{ fontSize: cx(30), textAlign: 'center', color: props.theme?.global.brand, borderColor: props.theme?.icon.disable, borderWidth: cx(4), minWidth: cx(120), minHeight: cx(73.5) }}
              value={`${state.tempValue}`}
              keyboardType="numeric"
              maxLength={4}
              onChangeText={(t: string) => {
                const numericValue = t.replace(',', '.')
                const idx = numericValue.indexOf('.')
                if (t === '') {
                  state.tempValue = t
                }
                if (numericValue[0] === '.'
                  || (numericValue.length === 2 && numericValue[0] === '0' && numericValue[1] !== '.')
                  || numericValue.split('').filter(item => item === '.').length > 1
                  || (idx === -1 && numericValue.length > 2) || (idx !== -1 && numericValue.length > idx + 2)
                  || (idx !== -1 && numericValue[idx + 1] !== undefined && numericValue[idx + 1] !== '5' && numericValue[idx + 1] !== '0')
                  || Number(numericValue) < 0 || Number(numericValue) > 29.5
                ) {
                  return
                }
                state.tempValue = t
              }}
              onBlur={() => {
                if (state.tempValue) {
                  const temp = exchangeNumber(state.tempValue)
                  if (Number(temp) < 0.5 || Number(temp) > 29.5) {
                    state.tempValue = getLocaleNumber(props.tempValue)
                  } else {
                    state.tempValue = getLocaleNumber(Number(temp))
                    props.onTempChange(Number(temp))
                  }
                } else {
                  state.tempValue = getLocaleNumber(props.tempValue)
                }
                state.isKeyboard = false
              }}
            /> :
              <Text style={[styles.contentText, { fontSize: cx(50), alignSelf: 'center', color: props.theme?.global.brand }]}>{`${state.tempValue}℃`}</Text>
            }
          </TouchableOpacity>
        }
        <Spacer height={cx(16)} />
        {!props.disableControl && !props.isRapidHeating ? <TouchableOpacity
          onPressIn={startDecrement}
          onPressOut={stopDecrement}
        >
          <Image
            style={{
              width: cx(60),
              height: cx(60),
              tintColor: props.theme?.global.brand
            }}
            source={{ uri: res.arrow_temp_down}}
          />
        </TouchableOpacity> : <Spacer height={cx(35)} />}
        <Spacer />
        <Text style={{ color: props.theme?.global.fontColor, fontSize: cx(14) }}>{`${I18n.getLang('thermostat_tempcurrent')}: ${props.currentTemp}℃`}</Text>
      </View>
      <Spacer height={cx(24)} />
    </Card>
  )
}

export default withTheme(ThermostatCard);
