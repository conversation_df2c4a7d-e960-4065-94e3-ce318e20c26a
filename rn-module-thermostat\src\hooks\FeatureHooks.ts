import I18n from "@ledvance/base/src/i18n";
import { useDeviceId, useDp } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Result } from "@ledvance/base/src/models/modules/Result";
import { getGlobalParamsDp } from "@ledvance/base/src/utils/common";
import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard';
import { RouterKey } from '../navigation/Router'
import { useChildLock } from "@ledvance/ui-biz-bundle/src/newModules/childLock/ChildLockPage";
import { useEffect, useState } from "react";
import { getFeature, putFeature } from "@ledvance/base/src/api/native";
export type ModeType = 'auto' | 'manual' | 'holiday'

export function useBatteryPercentage(): number {
  return useDp<number, any>(getGlobalParamsDp('battery_percentage'))[0] || 0;
}

export function useTempCurrent() {
  return useDp<number, any>(getGlobalParamsDp('temp_current'))[0] / 10
}

export function useManualTemp(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('temp_set'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useAutoTemp(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('auto_temp'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useComfortTemp(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('comfort_temp'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useEcoTemp(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('eco_temp'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useWindowTemp(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('window_temp'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useTempDrift(): [number, (v: number) => Promise<Result<any>>] {
  const [tempValue, setTempValue] = useDp<number, any>(getGlobalParamsDp('temp_drift'))
  const setTempValueFn = (temp: number) => {
    return setTempValue(Math.round(temp * 10))
  }
  return [(tempValue ?? 0) / 10, setTempValueFn]
}

export function useFeatureChildLock(): [boolean, (v: boolean) => void]{
  const devId = useDeviceId()
  const [childLock, setChildLock] = useState(false)
  useEffect(() =>{
    getFeature(devId, 'child_lock').then(res =>{
      if (res.result && res.data){
        setChildLock(res.data)
      }
    })
  }, [])

  const setChildLockFn = (childLock: boolean) =>{
    putFeature(devId, 'child_lock', childLock).then(res =>{
      if (res.result){
        setChildLock(childLock)
      }
    })
  }
  return [childLock, setChildLockFn]
}

export function useWindowTime() {
  return useDp<number, any>(getGlobalParamsDp('windows_time'))
}

export function useWorkMode(): [ModeType, (v: ModeType) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('mode'))
}

export function useRapid() {
  return useDp<boolean, any>(getGlobalParamsDp('rapid'))
}

export function useRapidTime() {
  return useDp<number, any>(getGlobalParamsDp('rapid_time'))
}

export function useRefresh() {
  return useDp<boolean, any>(getGlobalParamsDp('refresh'))
}

interface AdvancedProps {
  sceneStatusId: number
  setSceneStatusId: (v: number) => Promise<any>
  setFeatureChildLock: (v: boolean) => void
}
export function useAdvancedData(props: AdvancedProps): AdvancedData[] {
  const [childLock] = useChildLock(getGlobalParamsDp('child_lock'))
  const { sceneStatusId, setSceneStatusId, setFeatureChildLock} = props
  const res: AdvancedData[] = []

  res.push({
    title: I18n.getLang('thermostat_scene'),
    statusColor: getAdvancedStatusColor(sceneStatusId !== -1 ? AdvancedStatus.Enable : AdvancedStatus.Disable),
    dp: { key: '', code: '' },
    router: {
      key: RouterKey.mood,
      params: {
        sceneStatusId,
        setSceneStatusId
      }
    },
  })

  res.push({
    title: I18n.getLang('thermostat_automode'),
    dp: { key: '', code: '2' },
    router: {
      key: RouterKey.auto_mode,
    },
  })

  res.push({
    title: I18n.getLang('thermostat_vacationplan'),
    dp: { key: 'holiday_set', code: getGlobalParamsDp('holiday_set') },
    router: {
      key: RouterKey.vacation_mode,
    },
  })

  res.push({
    title: I18n.getLang('sockets_specific_settings_child_lock'),
    statusColor: getAdvancedStatusColor(childLock ? AdvancedStatus.Enable : AdvancedStatus.Disable),
    dp: { key: 'child_lock', code: getGlobalParamsDp('child_lock') },
    router: {
      key: RouterKey.ui_biz_child_lock,
      params: {
        childLockCode: getGlobalParamsDp('child_lock'),
        descriptionText: I18n.getLang('thermostat_childlock_overview_description_text'),
        switchTriggerEvent: (v) => {
          setFeatureChildLock(v)
        }
      }
    },
  })

  res.push({
    title: I18n.getLang('contact_sensor_specific_settings'),
    dp: { key: '', code: 'setting' },
    router: {
      key: RouterKey.settings,
    },
  })

  res.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.history,
    },
  })

  return res
}
