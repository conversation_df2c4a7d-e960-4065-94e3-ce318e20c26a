import { NavigationRoute } from 'tuya-panel-kit'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import HomePage from '../pages/home/<USER>'
import SettingPage from '../pages/setting/SettingPage'
import PowerModePage from '../pages/powerMode/PowerModePage'
import VacationModePage from '../pages/vacationMode/VacationModePage'
import AutoModePage from '../pages/autoMode/AutoModePage'
import AutoModeEditPage from '../pages/autoMode/AutoModeEditPage'
import AutoModeRepeat from '../pages/autoMode/AutoModeRepeat'
import TriggerTimeEdit from '../pages/autoMode/TriggerTimeEdit'
import IconSelect from "@ledvance/ui-biz-bundle/src/newModules/biorhythm/IconSelect";
import WindowReminderPage from 'pages/setting/WindowReminderPage'
import HistoryPage from 'pages/history/HistoryPage'
import MoodPage from 'pages/mood/MoodPage'
import AddMoodPage from 'pages/mood/AddMoodPage'
import MoodPageEdit from 'pages/mood/MoodPageEdit'
import ChildLockRouters from '@ledvance/ui-biz-bundle/src/newModules/childLock/Router'

export const RouterKey = {
  main: 'main',
  settings: 'Settings',
  auto_mode: 'auto_mode',
  auto_mode_edit: 'auto_mode_edit',
  auto_mode_repeat: 'auto_mode_repeat',
  trigger_time_edit: 'trigger_time_edit',
  power_mode: 'power_mode',
  vacation_mode: 'vacation_mode',
  window_reminder: 'window_reminder',
  history: 'history',
  history_detail: 'history_detail',
  history_chart: 'history_chart',
  mood: 'mood',
  mood_add: 'mood_add',
  mood_edit: 'mood_edit',
  ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.settings,
    component: SettingPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.window_reminder,
    component: WindowReminderPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.auto_mode,
    component: AutoModePage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.auto_mode_edit,
    component: AutoModeEditPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.auto_mode_repeat,
    component: AutoModeRepeat,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.trigger_time_edit,
    component: TriggerTimeEdit,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.bi_biz_biological_icon_select,
    component: IconSelect,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.power_mode,
    component: PowerModePage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.vacation_mode,
    component: VacationModePage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.history,
    component: HistoryPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.mood,
    component: MoodPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.mood_add,
    component: AddMoodPage,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.mood_edit,
    component: MoodPageEdit,
    options: {
      gesture: true,
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  ...ChildLockRouters
]