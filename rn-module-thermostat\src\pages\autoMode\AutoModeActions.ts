import { getFeature, putFeature } from "@ledvance/base/src/api/native"
import I18n from "@ledvance/base/src/i18n"
import { useDeviceId, useDp } from "@ledvance/base/src/models/modules/NativePropsSlice"
import { Result } from "@ledvance/base/src/models/modules/Result"
import { getGlobalParamsDp, hex2Int, spliceByStep } from "@ledvance/base/src/utils/common"
import { to16 } from "@tuya/tuya-panel-lamp-sdk/lib/utils"
import { cloneDeep } from "lodash"
import { useCallback, useEffect, useMemo, useState } from "react"

export interface ItemType {
  enable: boolean
  timeIdx: number
  tempIdx: number
  time: number
  iconId: number
}
export interface AutoModeItem {
  nodes: ItemType[]
}
export interface AutoModeUIItem extends AutoModeItem {
  name: string
}
interface featureAutoModeItem {
  name: string
  nodes: {
    iconId: number
    enable: boolean
  }[]
}

export const weeks = [
  I18n.getLang('bio_ryhthm_default_weekday1_text'),
  I18n.getLang('bio_ryhthm_default_weekday2_text'),
  I18n.getLang('bio_ryhthm_default_weekday3_text'),
  I18n.getLang('bio_ryhthm_default_weekday4_text'),
  I18n.getLang('bio_ryhthm_default_weekday5_text'),
  I18n.getLang('bio_ryhthm_default_weekday6_text'),
  I18n.getLang('bio_ryhthm_default_weekday7_text'),
]

const nodeColors = ['#1A37DF', '#194BAF', '#2E89E1', '#3DB9F7', '#4EC39D', '#A6E454', '#FEFF00', '#FF8200', '#C70000', '#9B0000']

export const getNodeColorByTemp = (temperature: number) =>{
  const index = Math.floor((temperature - 0.5) / 3); // 每个颜色覆盖3度
  return nodeColors[index];
}


export const tempList = Array.from({ length: 49 }, (_, v) => ({
  label: `${(v + 1) * 0.5}`,
  value: `${(v + 1) * 0.5}`,
}))



const autoModeFeatureId = 'AutoMode'

export const useAutoMode = (disableFeature?: boolean): [AutoModeUIItem[], (v: AutoModeUIItem, idx: number) => Promise<Result<any>>, boolean] => {
  const autoModeDp = [
    getGlobalParamsDp('monday'),
    getGlobalParamsDp('thursday'),
    getGlobalParamsDp('wednesday'),
    getGlobalParamsDp('tuesday'),
    getGlobalParamsDp('friday'),
    getGlobalParamsDp('saturday'),
    getGlobalParamsDp('sunday')
  ]
  const devId = useDeviceId()
  const autoModeData = autoModeDp.map(dp => useDp<string, any>(dp))
  const autoModeDpList = useMemo(() => autoModeData.map(item => item[0]), [JSON.stringify(autoModeData)])
  const [autoModeUiList, setAutoModeUiList] = useState<AutoModeUIItem[]>([])
  const [loading, setLoading] = useState(true)
  const formatterFn = useCallback(() => {
    return autoModeDpList.map((item, idx) => ({
      ...dp2Obj(item),
      index: idx,
      name: I18n.getLang('thermostat_automode') + (idx + 1)
    }))
  }, [JSON.stringify(autoModeDpList)])

  useEffect(() => {
    if (disableFeature) {
      setAutoModeUiList(formatterFn())
      return
    }
    let timer = setTimeout(() => {
      getFeature(devId, autoModeFeatureId).then(res => {
        setLoading(false)
        if (res.result) {
          if (res.data) {
            const featureData: featureAutoModeItem[] = res.data
            const uiData = autoModeDpList.map((item, idx) => {
              return ({
                nodes: dp2Obj(item).nodes.map((node, index) => ({ ...node, iconId: featureData[idx]?.nodes?.[index]?.iconId ?? node.iconId, enable: featureData[idx]?.nodes[idx]?.enable ?? true })),
                index: idx,
                name: featureData && featureData[idx] ? featureData[idx].name : `${I18n.getLang('thermostat_automode')}` + (idx + 1)
              })
            })
            setAutoModeUiList(uiData)
          } else {
            setAutoModeUiList(formatterFn())
          }
        } else {
          setAutoModeUiList(formatterFn())
        }
      })
    }, 200)
    return () => clearTimeout(timer)
  }, [JSON.stringify(autoModeDpList)])

  const setAutoModeFn = async (autoMode: AutoModeUIItem, idx: number, repeatIds?: number[]) => {
    const autoModeDp = obj2Dp(autoMode, idx + 1)
    const cloneUiList = cloneDeep(autoModeUiList)
    cloneUiList[idx] = autoMode
    if (repeatIds){
      cloneUiList.forEach((_, index)=>{
        cloneUiList[index] = {
          ...autoMode,
          name: idx === index ? autoMode.name : cloneUiList[index].name
        }
      })
    }
    const featureList: featureAutoModeItem[] = cloneUiList.map(item => ({
      name: item.name,
      nodes: item.nodes.map(node => ({ iconId: node.iconId, enable: node.enable }))
    }))
    const res = await putFeature(devId, autoModeFeatureId, featureList)
    if (res.result) {
      setAutoModeUiList(cloneUiList)
      if (repeatIds){
        repeatIds.forEach((id)=>{
          if (id !== idx){
            autoModeData[id][1](obj2Dp(autoMode, id + 1))
          }
        })
        return {success: true}
      }else{
        return autoModeData[idx][1](autoModeDp)
      }
    } else {
      return {
        success: false
      }
    }
  }
  return [autoModeUiList, setAutoModeFn, loading]
}

const dp2Obj = (dp: string): AutoModeItem => {
  if (!dp || dp.length < 6) return {
    nodes: [
      { timeIdx: 0, time: 0, tempIdx: 34, iconId: 11, enable: true }
    ],

  }
  const autoMode = spliceByStep(dp, 4).filter(item => item.slice(0, 2) !== '60').map((item, idx) => {
    const timeIdx = hex2Int(item.slice(0, 2))
    const tempIdx = hex2Int(item.slice(2, 4))
    return {
      timeIdx: idx === 0 ? 0 : timeIdx,
      time: idx === 0 ? 0 : idToTime(timeIdx),
      tempIdx,
      iconId: idx + 1,
      enable: true
    }
  })
  return {
    nodes: autoMode
  }
}

const obj2Dp = (autoMode: AutoModeItem, idx: number) => {
  const nodeHex = autoMode.nodes.map((item, index) => {
    const timeHex = to16(index === 0 ? idx : item.timeIdx)
    const tempHex = to16(item.tempIdx)
    return timeHex + tempHex
  })
  while (nodeHex.length < 9) {
    nodeHex.push('6022')
  }
  return nodeHex.join('')
}

function idToTime(id: number) {
  const totalMinutes = id * 15; // 每个ID对应的分钟数
  const hours = Math.floor(totalMinutes / 60); // 计算小时
  const minutes = totalMinutes % 60; // 计算剩余的分钟
  return hours * 60 + minutes;
}

export function timeToId(time: number) {
  return Math.trunc(time / 15)
}
