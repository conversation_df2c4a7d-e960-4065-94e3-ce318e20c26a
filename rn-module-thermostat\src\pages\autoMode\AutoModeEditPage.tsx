import React, { useMemo } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { Utils } from 'tuya-panel-kit';
import Page from '@ledvance/base/src/components/Page';
import { useNavigation } from '@react-navigation/core';
import TextField from '@ledvance/base/src/components/TextField';
import {
  useSystemTimeFormate,
} from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import I18n from '@ledvance/base/src/i18n';
import Card from '@ledvance/base/src/components/Card';
import Spacer from '@ledvance/base/src/components/Spacer';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import { RouterKey } from 'navigation/Router';
import { useReactive } from 'ahooks';
import { AutoModeUIItem, getNodeColorByTemp, ItemType, timeToId } from './AutoModeActions';
import { cloneDeep, difference, head, isEqual, range } from 'lodash';
import { convertMinutesTo12HourFormat } from '@ledvance/base/src/utils/common';
import iconList from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/iconListData';
import { Result } from '@ledvance/base/src/models/modules/Result';
import TimeCircular from './circular/TimeCircular';
import { Plan } from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/BiorhythmBean';
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface AutoModeEditParams {
  autoModeItem: AutoModeUIItem
  setAutoMode: (v: AutoModeUIItem, idx: number) => Promise<Result<any>>
  index: number
}

const AutoModeEditPage = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation();
  const params = useParams<AutoModeEditParams>();
  const is24Hour = useSystemTimeFormate();
  const state = useReactive({
    autoModeItem: cloneDeep(params.autoModeItem),
    loading: false
  })

  const onAutoModeNodeEdit = (autoModeNodes: ItemType[]) => {
    state.autoModeItem.nodes = cloneDeep(autoModeNodes)
  }

  const planList = useMemo(() => {
    const newPlan: Plan[] = state.autoModeItem.nodes.map((item, index) => {
      return {
        ...item,
        index,
        icon: iconList[item.iconId].icon,
        name: '',
        brightness: 100,
        colorTemperature: item.tempIdx < 35 ? 100 : 0,
        action: [
          {
            uri: 'model/attribute/set/LightCtrl/ColorTemperature',
            startValue: `${item.tempIdx < 35 ? 6500 : 2700}`,
          },
          {
            uri: 'model/attribute/set/LightCtrl/Brightness',
            startValue: `${100}`,
          },
        ]
      }
    })
    return newPlan
  }, [JSON.stringify(state.autoModeItem)])

  const checkAutoModeDataChanged = useMemo(() => {
    return isEqual(params.autoModeItem, state.autoModeItem)
  }, [JSON.stringify(state.autoModeItem), JSON.stringify(params.autoModeItem)])

  const canSaveAutoMode = useMemo(() => {
    return state.autoModeItem.name.length > 0 && state.autoModeItem.name.length < 33 && !checkAutoModeDataChanged
  }, [state.autoModeItem.name, checkAutoModeDataChanged])

  const newIconId = useMemo(() => {
    const idRange = range(1, 24);
    const ids = state.autoModeItem.nodes.map(item => item.iconId)
    const iconId: number = head(difference(idRange, ids)) || 1;
    return iconId
  }, [JSON.stringify(state.autoModeItem.nodes)])

  const styles = StyleSheet.create({
    cardContainer: {
      marginHorizontal: cx(24),
    },
    normalText: {
      fontSize: cx(14),
      color: props.theme?.global.fontColor,
    },
  });


  return (
    <Page
      backText={I18n.getLang('thermostat_automode')}
      rightButtonIcon={canSaveAutoMode ? res.ic_check : res.ic_uncheck}
      headlineText={I18n.getLang('thermostat_editauto')}
      backDialogTitle={I18n.getLang('manage_user_unsaved_changes_dialog_headline')}
      backDialogContent={I18n.getLang('cancel_dialog_leave_unsaved_bio_rhythm_note')}
      showBackDialog={!checkAutoModeDataChanged}
      loading={state.loading}
      rightButtonIconClick={async () => {
        if (state.loading || !canSaveAutoMode) return
        state.loading = true
        const res = await params.setAutoMode(state.autoModeItem, params.index)
        state.loading = false
        if (res.success) {
          navigation.navigate(RouterKey.auto_mode)
        }
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Text style={[styles.normalText, styles.cardContainer]}>
          {I18n.getLang('thermostat_schedule')}
        </Text>
        <Spacer />
        <TextField
          style={{ marginHorizontal: cx(24) }}
          value={state.autoModeItem.name}
          showError={state.autoModeItem.name.length > 32}
          maxLength={33}
          errorText={I18n.getLang('add_new_dynamic_mood_alert_text')}
          placeholder={I18n.getLang('add_new_trigger_time_inputfield_value_text')}
          onChangeText={(t: string) => {
            state.autoModeItem.name = t
          }}
        />
        <Spacer />
        <View style={{ alignItems: 'center' }}>
          <TimeCircular
            planEdit={true}
            planList={planList}
            onPanMoved={(id, time) => {
              state.autoModeItem.nodes = state.autoModeItem.nodes.map((item, idx) => {
                return {
                  ...item,
                  time: idx === id ? time : item.time,
                  timeIdx: idx === id ? timeToId(time) : item.timeIdx
                }
              })
            }}
            isSupportTemperature={false}
            replaceStatus={true}
          />
        </View>
        <Spacer />
        <Text style={styles.cardContainer}>{ }</Text>
        <View style={[styles.cardContainer, { flexDirection: 'row', justifyContent: 'space-between' }]}>
          <Text
            style={[styles.normalText, { fontSize: cx(16), fontWeight: 'bold' }]}
          >
            {I18n.getLang('bio_ryhthm_default_subheadline_text')}
          </Text>
          {state.autoModeItem.nodes.length < 9 && <TouchableOpacity
            onPress={() => {
              navigation.navigate(RouterKey.trigger_time_edit, {
                isAdd: true,
                node: {
                  enable: true,
                  time: 0,
                  timeIdx: 0,
                  tempIdx: 34,
                  iconId: newIconId
                },
                autoModeNodes: state.autoModeItem.nodes.map(item => item),
                onAutoModeNodeEdit
              })
            }}
          >
            <Image source={{ uri: res.device_panel_schedule_add}} style={{ height: cx(24), width: cx(24), tintColor: props.theme?.global.fontColor }} />
          </TouchableOpacity>}
        </View>
        <FlatList
          data={state.autoModeItem.nodes}
          renderItem={({ item, index }) => {
            return (
              <Card
                style={[styles.cardContainer, { paddingHorizontal: cx(16) }]}
                onPress={() => {
                  navigation.navigate(RouterKey.trigger_time_edit, {
                    isAdd: false,
                    noDeletion: index === 0,
                    node: item,
                    autoModeNodes: state.autoModeItem.nodes.filter((_, idx) => index !== idx).map(item => item),
                    onAutoModeNodeEdit
                  })
                }}
              >
                <Spacer height={cx(16)} />
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Image
                      source={{ uri: iconList[item.iconId - 1]?.icon ?? iconList[0].icon}}
                      style={{ width: cx(24), height: cx(24), tintColor: props.theme?.global.fontColor, marginRight: cx(5) }}
                    />
                    <Text style={[styles.normalText, { fontSize: cx(16) }]}>{`${convertMinutesTo12HourFormat(item.time, is24Hour)} - ${index === state.autoModeItem.nodes.length - 1 ? convertMinutesTo12HourFormat(1440, is24Hour) : convertMinutesTo12HourFormat(state.autoModeItem.nodes[index + 1].time, is24Hour)}`}</Text>
                  </View>
                  {/* {index !== 0 && <SwitchButton
                    value={item.enable}
                    thumbStyle={{ elevation: 0 }}
                    onValueChange={(v) => {
                      if (index === 0) return
                      item.enable = v
                    }} />} */}
                </View>
                <Spacer height={cx(5)} />
                <Text style={[styles.normalText, { color: props.theme?.global.secondFontColor }]}>{`${item.tempIdx * 0.5}℃`}</Text>
                <Spacer height={cx(5)} />
                <View style={{ backgroundColor: getNodeColorByTemp(item.tempIdx * 0.5), borderRadius: cx(6), height: cx(24) }} />
                <Spacer height={cx(16)} />
              </Card>
            );
          }}
          keyExtractor={item => `${item.time}`}
          nestedScrollEnabled={true}
          ItemSeparatorComponent={() => <Spacer height={cx(16)} />}
          ListHeaderComponent={() => <Spacer height={cx(16)} />}
          ListFooterComponent={() => <Spacer />}
        />
        <Spacer />
      </ScrollView>
    </Page>
  );
};

export default withTheme(AutoModeEditPage);
