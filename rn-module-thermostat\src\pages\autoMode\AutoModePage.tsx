import React, { useCallback } from 'react';
import { ScrollView, Text, StyleSheet, View, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/core';
import { Utils } from 'tuya-panel-kit';
import Page from '@ledvance/base/src/components/Page';
import I18n from '@ledvance/base/src/i18n';
import { useDeviceInfo, useSystemTimeFormate } from '@ledvance/base/src/models/modules/NativePropsSlice';
import Spacer from '@ledvance/base/src/components/Spacer';
import { useReactive, useUpdateEffect } from 'ahooks';
import Card from '@ledvance/base/src/components/Card';
import { RouterKey } from 'navigation/Router';
import { AutoModeUIItem, useAutoMode, weeks } from './AutoModeActions';
import { convertMinutesTo12HourFormat } from '@ledvance/base/src/utils/common';
import DeleteButton from '@ledvance/base/src/components/DeleteButton';
import ThemeType from "@ledvance/base/src/config/themeType";


const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

const AutoModePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo();
  const navigation = useNavigation();
  const is24Hour = useSystemTimeFormate()
  const [autoMode, setAutoMode, loading] = useAutoMode()
  const state = useReactive({
    loading,
  });

  useUpdateEffect(() => {
    state.loading = loading
  }, [loading])


  const getTriggerTimes = useCallback((autoMode: AutoModeUIItem) => {
    return autoMode.nodes.map(item => convertMinutesTo12HourFormat(item.time, is24Hour)).join(', ') + `, ${convertMinutesTo12HourFormat(1440, is24Hour)}`
  }, [is24Hour])

  const styles = StyleSheet.create({
    overviewDescription: {
      color: props.theme?.global.fontColor,
      marginHorizontal: cx(24),
    },
    cardItemText: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    addBtn: {
      height: cx(40),
      width: 'auto',
      minWidth: cx(150),
      paddingHorizontal: cx(16),
      backgroundColor: props.theme?.button.primary,
    },
  });

  const autoModeCard = (item: AutoModeUIItem, index: number) => {
    return (
      <View>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingRight: cx(24) }}>
          <Text style={{ marginHorizontal: cx(24), color: props.theme?.global.fontColor, fontWeight: 'bold', fontSize: cx(16) }}>{weeks[index]}</Text>
          <DeleteButton
            style={{ minWidth: cx(60), height: cx(30), width: 'auto', paddingHorizontal: cx(5), backgroundColor: props.theme?.button.primary }}
            textStyle={{ fontSize: cx(14), fontWeight: 'normal' }}
            onPress={() => {
              navigation.navigate(RouterKey.auto_mode_repeat, {
                autoModeItem: item,
                setAutoMode,
                index
              })
            }}
            text={I18n.getLang('message_repeat')} />
        </View>
        <Spacer height={cx(10)} />
        <Card
          style={{ marginHorizontal: cx(24), paddingHorizontal: cx(16) }}
          onPress={() => {
            navigation.navigate(RouterKey.auto_mode_edit, {
              autoModeItem: item,
              setAutoMode,
              index
            })
          }}
        >
          <Spacer height={cx(16)} />
          <Text style={[styles.cardItemText, { fontWeight: 'bold' }]}>{item.name}</Text>
          <Spacer height={cx(10)} />
          <Text style={[styles.cardItemText, { lineHeight: cx(16) }]}>{`${I18n.getLang('thermostat_triggertimes')}: ${getTriggerTimes(item)}`}</Text>
          <Spacer height={cx(20)} />
        </Card>
      </View>
    )
  }

  return (
    <Page
      backText={deviceInfo.name}
      onBackClick={navigation.goBack}
      headlineText={I18n.getLang('thermostat_automode')}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <FlatList
          data={autoMode}
          renderItem={({ item, index }) => autoModeCard(item, index)}
          keyExtractor={(_, index) => `${index}`}
          ItemSeparatorComponent={() => <Spacer height={cx(25)} />}
          ListHeaderComponent={<Spacer height={cx(10)} />}
          ListFooterComponent={<Spacer />}
        />
      </ScrollView>
    </Page>
  );
};

export default withTheme(AutoModePage);
