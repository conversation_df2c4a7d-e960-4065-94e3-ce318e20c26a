import React, { useMemo } from 'react';
import { FlatList, ScrollView, View, Text } from 'react-native';
import Page from '@ledvance/base/src/components/Page';
import { AutoModeUIItem, weeks } from './AutoModeActions';
import { useReactive } from 'ahooks';
import { cloneDeep, filter, isEqual, map, reduce } from 'lodash';
import { Checkbox, Utils } from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import res from '@ledvance/base/src/res';
import I18n from '@ledvance/base/src/i18n';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { useNavigation } from '@react-navigation/core';
import { RouterKey } from 'navigation/Router';
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface AutoModeRepeatParams {
  autoModeItem: AutoModeUIItem
  setAutoMode: (v: AutoModeUIItem, idx: number, repeatIds?: number[]) => Promise<Result<any>>
  index: number
}

const AutoModeRepeat = (props: { theme?: ThemeType }) => {
  const params = useParams<AutoModeRepeatParams>()
  const navigation = useNavigation();
  const initRepeatData = useMemo(() => {
    return weeks.map((week, idx) => ({
      title: week,
      checked: false,
      disabled: params.index === idx,
    }));
  }, []);

  const state = useReactive({
    repeatData: cloneDeep(initRepeatData),
    loading: false
  });

  const checkAutoModeDataChanged = useMemo(() =>{
    return !isEqual(initRepeatData, state.repeatData)
  }, [JSON.stringify(state.repeatData)])

  return (
    <Page
      backText={I18n.getLang('thermostat_automode')}
      headlineText={I18n.getLang('message_repeat')}
      rightButtonIcon={checkAutoModeDataChanged ? res.ic_check : res.ic_uncheck}
      backDialogTitle={I18n.getLang('manage_user_unsaved_changes_dialog_headline')}
      showBackDialog={checkAutoModeDataChanged}
      loading={state.loading}
      rightButtonIconClick={async () =>{
        if (state.loading || !checkAutoModeDataChanged) return
        state.loading = true
        const repeatIds = reduce(state.repeatData, (acc, item, index) => {
          if (item.checked) {
            acc.push(index);
          }
          return acc;
        }, [] as number[]);
        const res = await params.setAutoMode(params.autoModeItem, params.index, repeatIds)
        console.log(res, '< --- res --- >')
        state.loading = false
        if (res.success){
          navigation.navigate(RouterKey.auto_mode)
        }
      }}
    >
      <ScrollView>
        <FlatList
          data={state.repeatData}
          renderItem={({ item, index }) => {
            return (
              <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginHorizontal: cx(24)}}>
                <Text style={{fontSize: cx(16), color: props.theme?.global.fontColor}}>{item.title}</Text>
                {item.disabled ? undefined : (
                  <Checkbox
                    size={cx(24)}
                    color={props.theme?.global.brand}
                    checked={item.checked}
                    onChange={v => {
                      state.repeatData = state.repeatData.map((item, idx) => {
                        if (idx === index) {
                          return {
                            ...item,
                            checked: v,
                          };
                        }
                        return item;
                      });
                    }}
                  />
                )}
              </View>
            );
          }}
          keyExtractor={item => `${item.title}`}
          ItemSeparatorComponent={() => <Spacer height={cx(25)} />}
          ListHeaderComponent={<Spacer height={cx(10)} />}
          ListFooterComponent={<Spacer />}
        />
      </ScrollView>
    </Page>
  );
};

export default withTheme(AutoModeRepeat);
