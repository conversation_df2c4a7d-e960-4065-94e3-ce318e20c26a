import React, { useMemo } from 'react';
import { ScrollView, TouchableOpacity, View, Text, Image, StyleSheet } from 'react-native'
import Page from '@ledvance/base/src/components/Page';
import { Utils } from 'tuya-panel-kit';
import I18n from '@ledvance/base/src/i18n';
import { useSystemTimeFormate } from '@ledvance/base/src/models/modules/NativePropsSlice';
import res from '@ledvance/base/src/res';
import { useNavigation } from '@react-navigation/native'
import { useReactive } from 'ahooks';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import iconList from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/iconListData';
import { RouterKey } from 'navigation/Router';
import Spacer from '@ledvance/base/src/components/Spacer';
import { ItemType, timeToId } from './AutoModeActions';
import { cloneDeep, isEqual } from 'lodash';
import ThermostatCard from 'components/ThermostatCard';
import { useTempCurrent } from 'hooks/FeatureHooks';
import LdvPickerView from '@ledvance/base/src/components/ldvPickerView'
import DeleteButton from "@ledvance/base/src/components/DeleteButton";
import { showDialog } from '@ledvance/base/src/utils/common';
import ThemeType from "@ledvance/base/src/config/themeType";

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils;

export interface TriggerTimeEditParams {
  isAdd: boolean
  noDeletion: boolean
  node: ItemType
  autoModeNodes: ItemType[]
  onAutoModeNodeEdit: (v: ItemType[]) => void
}

const TriggerTimeEdit = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation()
  const params = useParams<TriggerTimeEditParams>()
  const currentTemp = useTempCurrent()
  const state = useReactive({
    node: cloneDeep(params.node),
    loading: false
  })

  const getImgId = useMemo(() => {
    return iconList[state.node.iconId - 1]?.icon ?? iconList[0].icon
  }, [state.node.iconId])

  const styles = StyleSheet.create({
    cardContainer: {
      marginHorizontal: cx(24),
    },
    normalText: {
      fontSize: cx(14),
      color: props.theme?.global.fontColor,
    },
    rowFlex: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    disabledCover: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      left: 0,
      top: 0,
      zIndex: 999,
      backgroundColor: props.theme?.icon.disable,
      opacity: 0.1
    },
  })

  const checkAutoModeDataChanged = useMemo(() => {
    return isEqual(params.node, state.node)
  }, [JSON.stringify(state.node), params.node])

  const canSaveNodeData = useMemo(() => {
    return !params.autoModeNodes.find(node => node.time === state.node.time) && (params.isAdd || !checkAutoModeDataChanged)
  }, [state.node.time, checkAutoModeDataChanged])

  return (
    <Page
      backText={I18n.getLang('thermostat_editauto')}
      loading={state.loading}
      backDialogTitle={I18n.getLang('manage_user_unsaved_changes_dialog_headline')}
      backDialogContent={I18n.getLang('cancel_dialog_leave_unsaved_bio_rhythm_note')}
      showBackDialog={!checkAutoModeDataChanged}
      rightButtonIcon={canSaveNodeData ? res.ic_check : res.ic_uncheck}
      headlineText={I18n.getLang(
        params.isAdd ? 'add_new_trigger_time_headline_text' : 'edit_trigger_time_headline_text'
      )}
      rightButtonIconClick={() => {
        if (!canSaveNodeData) return
        state.loading = true
        const v = [...params.autoModeNodes, state.node].sort((a, b) => a.time - b.time)
        params.onAutoModeNodeEdit(v)
        navigation.navigate(RouterKey.auto_mode_edit)
        state.loading = true
      }}
    >
      <ScrollView>
        <Spacer height={cx(10)} />
        <View style={{marginHorizontal: cx(24), position: 'relative'}}>
          {params.noDeletion && <View style={styles.disabledCover}/>}
          <LdvPickerView
            hour={Math.trunc(state.node.time / 60).toString().padStart(2, '0')}
            minute={(state.node.time % 60).toString().padStart(2, '0')}
            setHour={(h) => {
              const min = state.node.time % 60
              state.node.time = Number(h) * 60 + min
              state.node.timeIdx = timeToId(Number(h) * 60 + min)
            }}
            setMinute={(m) => {
              const h = Math.trunc(state.node.time / 60)
              state.node.time = h * 60 + Number(m)
              state.node.timeIdx = timeToId(h * 60 + Number(m))
            }}
            minutesStep={15}
          />
        </View>

        <Spacer height={cx(10)} />
        <View style={[styles.cardContainer, styles.rowFlex, { justifyContent: 'space-between' }]}>
          <Text style={styles.normalText}>{I18n.getLang('add_new_trigger_time_text')}</Text>
          <TouchableOpacity
            style={styles.rowFlex}
            onPress={() => {
              navigation.navigate(RouterKey.bi_biz_biological_icon_select, {
                backText: I18n.getLang(params.isAdd ? 'add_new_trigger_time_headline_text' : 'edit_trigger_time_headline_text'),
                id: state.node.iconId,
                setIcon: (id) => {
                  state.node.iconId = id
                },
                iconIdList: params.autoModeNodes.map(item => item.iconId),
              })
            }}
          >
            <Image
              style={{ width: cx(24), height: cx(24), tintColor: props.theme?.global.fontColor }}
              source={{ uri: getImgId ?? 10}}
            />
            <Spacer width={cx(5)} />
            <Image style={{ width: cx(16), height: cx(16), tintColor: props.theme?.global.fontColor }} source={{ uri: res.biorhythom_select_right_icon}} />
          </TouchableOpacity>
        </View>
        <Spacer />
        <Text style={[styles.itemTitle, styles.cardContainer]}>
          {I18n.getLang('timeschedule_add_schedule_subheadline2_text')}
        </Text>
        <Spacer height={cx(16)} />
        <ThermostatCard
          currentTemp={currentTemp}
          tempValue={state.node.tempIdx * 0.5}
          onTempChange={(v) => {
            state.node.tempIdx = Math.trunc(v / 0.5)
          }}
        />
        <Spacer height={cx(30)} />
        {!params.isAdd && !params.noDeletion &&
          <View style={{ marginHorizontal: cx(24) }}>
            <DeleteButton
              text={I18n.getLang('edit_trigger_time_button_delete_text')}
              onPress={() => {
                showDialog({
                  method: 'confirm',
                  title: I18n.getLang('cancel_dialog_delete_item_bio_rhythm_titel'),
                  subTitle: I18n.getLang('cancel_dialog_delete_item_wakeupschedule_description'),
                  onConfirm: async (_, { close }) => {
                    close()
                    state.loading = true
                    params.onAutoModeNodeEdit(params.autoModeNodes)
                    navigation.navigate(RouterKey.auto_mode_edit)
                    state.loading = false
                  }
                })
              }} />
            <Spacer />
          </View>
        }
      </ScrollView>
    </Page>
  );
};

export default withTheme(TriggerTimeEdit);
