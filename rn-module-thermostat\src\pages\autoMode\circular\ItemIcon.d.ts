import { PureComponent } from "react";
import { PanResponderInstance } from "react-native";
export default class ItemIcon extends PureComponent<any> {
    lastX: any;
    lastY: any;
    _panResponder: PanResponderInstance;
    state: any;
    constructor(props: any);
    componentWillMount(): void;
    onMoveShouldSetPanResponder(): boolean;
    onPanResponderMove(evt: any, gestureState: any): void;
    onPanResponderEnd(evt: any, gestureState: any): void;
    handlerData(_evt: any, ges: any, moving: any): void;
    getPointValues(locationX: any, locationY: any): {
        angle: number;
        maxX: number;
        maxY: number;
        length: number;
    };
    getPointAngle(x: any, y: any): number;
    render(): any;
}
