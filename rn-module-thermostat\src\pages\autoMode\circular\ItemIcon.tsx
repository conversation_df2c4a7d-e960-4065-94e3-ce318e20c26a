import React, { PureComponent } from "react";
import { Image, PanResponder, PanResponderInstance, StyleSheet, View } from "react-native";

const timeIconSize = 40; //时间图标ding
const deviationSize = 0;

export default class ItemIcon extends PureComponent<any> {
    lastX: any;
    lastY: any;
    _panResponder: PanResponderInstance;
    state: any;

    constructor(props) {
        super(props);

        this.onMoveShouldSetPanResponder = this.onMoveShouldSetPanResponder.bind(this);
        this.onPanResponderMove = this.onPanResponderMove.bind(this);
        this.onPanResponderEnd = this.onPanResponderEnd.bind(this);
        const { leftValeu, topValue, circularRadius } = this.props;

        this.lastX = leftValeu;
        this.lastY = topValue;
        this.state = { radius: circularRadius };
    }

    componentWillMount() {
        this._panResponder = PanResponder.create({

            onMoveShouldSetPanResponder: this.onMoveShouldSetPanResponder,
            onPanResponderMove: this.onPanResponderMove,
            onPanResponderRelease: this.onPanResponderEnd
        });
    }

    //触摸点开始移动的时候，是否响应触摸交互
    onMoveShouldSetPanResponder() {
        return true;
    }

    // 最近一次的移动距离为gestureState.move{X,Y}
    onPanResponderMove(evt, gestureState) {

        this.handlerData(evt, gestureState, true);

    }

    // 停止移动
    onPanResponderEnd(evt, gestureState) {
        this.handlerData(evt, gestureState, false);
    }

    handlerData(_evt, ges, moving) {

        //此时视图的坐标
        let locationX = this.lastX + ges.dx + deviationSize;
        let locationY = this.lastY + ges.dy + deviationSize;

        // 获取在父元素的实际坐标点值
        let result = this.getPointValues(locationX, locationY);
        let maxX = result.maxX;
        let maxY = result.maxY;

        this.setState({
            offset: {
                x: maxX,
                y: maxY
            }
        });

        const { onPanMoving, onPanMoveEnd } = this.props;
        onPanMoving && onPanMoving(maxX, maxY, result.angle);

        if (!(!!moving)) {
            // 记录滑动前 图表的位置
            if (this.state.offset) {
                this.lastX = this.state.offset.x;
                this.lastY = this.state.offset.y;
            }

            onPanMoveEnd && onPanMoveEnd();
        }
    }

    // 获取实际坐标的 角度 坐标值 步长
    getPointValues(locationX, locationY) {
        //半径
        let radius = this.state.radius;
        // 求斜边的长度
        let offsetX = Math.abs(radius - locationX);
        let offsetY = Math.abs(radius - locationY);
        // 斜边长度
        let length = Math.sqrt(offsetX * offsetX + offsetY * offsetY);

        //求角度
        let angle = this.getPointAngle(radius - locationX, radius - locationY);

        // 最终的坐标
        let maxX = locationX - deviationSize;
        let maxY = locationY - deviationSize;

        //超出边界: 在圆环滑动
        if (length !== radius) {
            let maxOffsetX = radius * (locationX - radius) / length;
            let maxOffsetY = radius * (radius - locationY) / length;
            //   maxX = radius + maxOffsetX - deviationSize + 1.5
            //   maxY = radius - maxOffsetY - deviationSize + 1.5
            maxX = radius + maxOffsetX - deviationSize;
            maxY = radius - maxOffsetY - deviationSize;
            length = radius;
        }

        return {
            angle: angle,
            maxX: maxX,
            maxY: maxY,
            length: length
        };
    }

    getPointAngle(x, y) {
        // 此时的角度是从左边为 0°开始 顺时针 到右边为 180° 逆时针到最右边为 -180°
        let angle = Math.atan2(y, x) * (180 / Math.PI);
        //改为正常坐标的角度（表盘中心为圆形）
        if (angle > 0) {
            //第四象限
            if (angle < 90) {
                angle = 270 + angle;
            } else {
                //第一象限
                angle = angle - 90;
            }
        } else if (angle == 0) {
            angle = 270;
        } else if (angle < 0) {
            //第三象限
            if (angle > -90) {
                angle = 270 + angle;

            } else {
                //第二象限
                angle = 90 + (180 + angle);
            }
        }

        return angle;
    }
    render() {
        const { leftValeu, topValue, imageSource } = this.props;
        return (
            <View style={[styles.timeIconView, { left: leftValeu, top: topValue, alignItems: 'center', justifyContent: 'center' }]} {...this._panResponder.panHandlers}>
                <Image source={imageSource}
                    style={{
                        width: 30,
                        height: 30,
                        marginLeft: 0,
                        tintColor: '#474e5d'
                    }} />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    timeIconView: {
        position: "absolute",
        height: timeIconSize,
        width: timeIconSize,
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#FFFFFF",
        borderRadius: Math.round(timeIconSize / 2)
    }
});