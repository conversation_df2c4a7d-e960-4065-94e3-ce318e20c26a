import { Component } from 'react';
export default class Progress extends Component<any> {
    timeIconList: any[];
    timer: any;
    state: any;
    constructor(props: any);
    UNSAFE_componentWillReceiveProps(nextProps: any): void;
    componentWillUnmount(): void;
    render(): any;
    playTimeChangeCallBack(id: number, time: string): void;
    getTimeIntVale(time: any): number;
    createFormTime(totalMinutes: any): string;
    checkTimesFormat(newTimeMinutes: any): {
        ret: boolean;
        failMinutes: number;
    } | {
        ret: boolean;
        failMinutes?: undefined;
    };
    getColorTempStringVale(colorTempInt: any): {
        rgbColor: number[];
        normalColor: string;
    };
}
