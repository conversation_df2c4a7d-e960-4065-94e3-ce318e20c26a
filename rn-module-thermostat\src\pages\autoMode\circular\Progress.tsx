import React, { Component } from 'react'
import { StyleSheet, View } from 'react-native'

import ItemIcon from './ItemIcon'
import Svg, { Circle } from 'react-native-svg'
import iconList from '@ledvance/ui-biz-bundle/src/newModules/biorhythm/iconListData'
import { cctToColor } from '@ledvance/base/src/utils/cctUtils'
import { getNodeColorByTemp } from '../AutoModeActions'
import { ColorUtils } from '@tuya/tuya-panel-lamp-sdk/lib/utils'

export default class Progress extends Component<any> {
  timeIconList: any[]
  timer: any
  state: any

  constructor(props) {
    super(props)
    const { timeIconList } = this.props

    this.timeIconList = timeIconList.map(item => ({ ...item }))
    this.state = {
      iconDataList: this.timeIconList,
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps) {
      const { timeIconList, planEdit } = nextProps
      if (timeIconList.length !== this.state.iconDataList.length || planEdit) {
        this.timeIconList = timeIconList.map(item => ({ ...item }))
        this.setState({ iconDataList: this.timeIconList })
      }
    }
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  }

  render() {
    //radius 大圆半径,annularHeight 环宽
    const { radius, annularHeight } = this.props
    const { iconDataList } = this.state
    //换算颜色
    if (iconDataList && iconDataList.length > 0) {
      //排序从小到大
      let min
      for (let i = 0; i < iconDataList.length; i++) {
        for (let j = i; j < iconDataList.length; j++) {
          const iData = iconDataList[i].newStartTime ? iconDataList[i].newStartTime : iconDataList[i].time
          const jData = iconDataList[j].newStartTime ? iconDataList[j].newStartTime : iconDataList[j].time
          if (iData > jData) {
            min = iconDataList[j]
            iconDataList[j] = iconDataList[i]
            iconDataList[i] = min
          }
        }
      }
      //取出色温、亮度
      iconDataList.forEach(ele => {
        const color = getNodeColorByTemp(ele.tempIdx * 0.5)
        ele.colorTemp = color
        const rgbString = ColorUtils.hex2RgbString(color)
        ele.rgbColor = rgbString.match(/\d+/g)?.map(Number) ?? [0,0,0]
      })
    }

    //有多少个时间点就分为几段弧
    let linearGradientList: any[] = []
    for (let c = 0; c < iconDataList.length; c++) {
      let tmpIndex = c + 1
      if (tmpIndex > iconDataList.length - 1) {
        tmpIndex = 0
      }
      let statRGBColer = iconDataList[c].rgbColor
      let iconDataRgbClr = iconDataList[tmpIndex].rgbColor

      let startTimes = iconDataList[c].time
      let endTimes = iconDataList[tmpIndex].time

      let totalMins = Math.abs(endTimes - startTimes)
      if (tmpIndex === 0) {
        totalMins = 1440 - startTimes + endTimes
      }

      let circleLength = (Math.PI * 2) / (24 * 60) * (totalMins)
      let timeAngle = 360 / (24 * 60) * startTimes

      // console.log('--- 开始时间 ---------->： ',iconDataList[c].startTime,'分钟数：',startTimes);
      // console.log('--- 结束时间 ---------->： ',iconDataList[tmpIndex].startTime,'分钟数：',endTimes);
      // console.log('--- 分段时长 ----------> ',totalMins);
      // console.log('--- 弧长 ----------> ',circleLength);

      linearGradientList.push({
        startRbg: statRGBColer,
        endRgb: this.props.gradient && statRGBColer || iconDataRgbClr,
        circleLength: circleLength,
        startAngle: timeAngle,
        brightness: iconDataList[c].brightness
      })
    }

    //console.log('--- 分段数据 ----------> ',linearGradientList)
    let aCircleList: any[] = []
    for (let l = 0; l < linearGradientList.length; l++) {
      const elem = linearGradientList[l]
      let startRGB = elem.startRbg
      let endRGB = elem.endRgb
      const brightness = elem.brightness || 0;
      //兼容不支持色温的灯
      if (!startRGB || !endRGB) {
        startRGB = [122, 122, 122]
        endRGB = [122, 122, 122]
      }

      //计算各时间段颜色变化值
      const arcArr: () => any = () => {
        let progressLength = (this.props.radius - 18) * elem.circleLength //每段的弧长
        const step = 60 // 设置到100则已经比较难看出来颜色断层
        const gradientColor = (startRGB, endRGB, step) => {
          let startR = startRGB[0]
          let startG = startRGB[1]
          let startB = startRGB[2]
          let endR = endRGB[0]
          let endG = endRGB[1]
          let endB = endRGB[2]
          let sR = (endR - startR) / step // 总差值
          let sG = (endG - startG) / step
          let sB = (endB - startB) / step
          let colorArr: any[] = []
          for (let i = 0; i < step; i++) {
            let color = Number(brightness) === 0 ? 'rgb(0, 0, 0)' : (this.props.isSupportTemperature && cctToColor(1)) || `rgb(${parseInt(sR * i + startR, 10)},
                  ${parseInt(sG * i + startG, 10)},
                  ${parseInt(sB * i + startB, 10)},
                  ${Number(brightness) / 100}
                  )`

            colorArr.push(color)
          }
          return colorArr
        }

        let colorArr = gradientColor(startRGB, endRGB, step)
        // 计算每个步进中的弧长
        let arcLengthArr = colorArr.map((color, index) => ({
          arcLength: [index * (progressLength / step), 10000000],
          color: color,
          arcAngle: elem.startAngle,
        }))

        //circleTotalLength = circleTotalLength + progressLength;
        arcLengthArr.reverse() //数组倒序
        return arcLengthArr
      }

      aCircleList = aCircleList.concat(arcArr())
    }
    let radiusValue = (this.props.radius).toString() //圆心坐标
    let circleR = (this.props.radius - 22).toString() // 半径长度 （不包含宽度值）
    const circleView = aCircleList.map((d, index) => {
      return (
        <Circle
          key={`c${index}`}
          cx={radiusValue}
          cy={radiusValue}
          r={circleR}
          stroke={d.color}
          strokeDasharray={d.arcLength}
          strokeWidth="44"
          rotation={-(90 - d.arcAngle)}
          originX={radiusValue}
          originY={radiusValue}
          //strokeOpacity = {1.0}
          strokeLinecap="butt"
          fill="none"
        />
      )
    })

    const setImg = (id) => {
      const imgIcon = iconList?.find(val => val?.id === id)?.icon
      return imgIcon
    }

    const roundToNearest15 = (num) =>{
      const rounded = Math.round(num / 15) * 15;
      return rounded === 0 ? 15 : rounded;
    }

    return (
      <View /*style = {{ backgroundColor: '#000000'}}*/>

        <Svg width={this.props.radius * 2} height={this.props.radius * 2}>

          {iconDataList.length > 0 ? circleView :
            <Circle
              cx={radiusValue}
              cy={radiusValue}
              r={circleR}
              strokeWidth="44"
              stroke={'#DEDEDE'}
              fill={'none'} />}
        </Svg>

        {/*----- 环上图标 ------*/}
        {this.timeIconList && this.timeIconList.map((item) => {

          const { time } = item

          //时间对应的弧度
          let timeAngle = (Math.PI * 2) / (24 * 60) * time
          let newRadius = radius - annularHeight + 22

          //1.5 为解决将圆当作一个点处理代理的误差
          let leftValeu = newRadius + (Math.sin(timeAngle) * newRadius) + 1.5
          let topValue = newRadius - (Math.cos(timeAngle) * newRadius) + 1.5
          const type = typeof item?.icon === 'string'

          return (
            <ItemIcon
              key={item.index}
              leftValeu={leftValeu}
              topValue={topValue}
              imageSource={{ uri: setImg(item?.iconId)} || type && { uri: item?.icon } || item?.icon}
              circularRadius={newRadius}
              onPanMoving={(_xValue, _yValue, angle) => {
                if (item.index === 0){
                  return
                }
                if (this.timer) {
                  clearTimeout(this.timer)
                  this.timer = null
                }

                //角度转换为表盘时间
                let angleValue = angle
                let angleMins = 24 * 60 / 360//每度多少分钟
                let totalMins = Math.abs(angleMins * angleValue)
                item.time = totalMins

                this.setState({ iconDataList: [].concat(iconDataList) })
              }}

              onPanMoveEnd={() => {
                const { ret, failMinutes } = this.checkTimesFormat(item.time)
                if (!ret) {
                  let changeMinutes = (failMinutes || 0) + 60
                  if (changeMinutes > 60 * 24) {
                    changeMinutes = 30
                  }
                  item.time = changeMinutes
                  this.setState({ iconDataList: [].concat(iconDataList) }, () => {
                    this.playTimeChangeCallBack(item.index, roundToNearest15(Math.round(changeMinutes)))
                  })
                } else {
                  this.playTimeChangeCallBack(item.index, roundToNearest15(Math.round(item.time)))
                }
              }}
            />
          )
        })}

        {/*----- 环内视图 ------*/}
        <View
          style={[
            styles.contentView,
            {
              left: (this.props.annularHeight),
              top: this.props.annularHeight,
              width: (this.props.radius - this.props.annularHeight) * 2,
              height: (this.props.radius - this.props.annularHeight) * 2,
              borderRadius: this.props.radius,
            },
          ]}>
          {this.props.children}
        </View>
      </View>
    )
  }

  //时间调整回调业务层
  playTimeChangeCallBack(id: number, time: number) {
    const { onPanMoved } = this.props

    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }

    //延时0.5秒后更新计划
    this.timer = setTimeout(() => {
      onPanMoved && onPanMoved(id, time)
    }, 50)
  }

  //判断时间是否合法
  checkTimesFormat(newTimeMinutes) {
    for (const elem of this.timeIconList) {
      const { time } = elem
      if (time === newTimeMinutes) {
        continue
      }
      if (Math.abs(time - newTimeMinutes) < 15) {
        return { ret: false, failMinutes: time }
      }
    }

    return { ret: true }
  }

  getColorTempStringVale(colorTempInt) {
    // 蓝色：80CBFF  黄色：FFCD66
    let limitBottomColor = '#FFCD66'
    let limitTopColor = '#80CBFF'

    if (parseInt(colorTempInt) >= 100) {
      return { rgbColor: [128, 203, 255], normalColor: limitTopColor }
    }

    if (parseInt(colorTempInt) <= 0) {
      return { rgbColor: [255, 205, 102], normalColor: limitBottomColor }
    }

    let redStat = parseInt('FF', 16)
    let redEnd = parseInt('80', 16)
    let redStep = Math.round(redStat + ((redEnd - redStat) / 100) * colorTempInt)

    let gStart = parseInt('CD', 16)
    let gEnd = parseInt('CB', 16)
    let gStep = Math.round(gStart + ((gEnd - gStart) / 100) * colorTempInt)

    let bStart = parseInt('66', 16)
    let bEnd = parseInt('FF', 16)
    let bStep = Math.round(bStart + ((bEnd - bStart) / 100) * colorTempInt)

    let colorVale = '#' + redStep.toString(16) + gStep.toString(16) + bStep.toString(16)
    // console.log('色温值 -----------> colorVale: ',colorVale);

    let colorRGBAry = [redStep, gStep, bStep]
    return { rgbColor: colorRGBAry, normalColor: colorVale }
  }
}

const styles = StyleSheet.create({
  contentView: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 130,
  },
  angleView: {
    height: 260,
    width: 260,
    borderRadius: 130,
  },
})
