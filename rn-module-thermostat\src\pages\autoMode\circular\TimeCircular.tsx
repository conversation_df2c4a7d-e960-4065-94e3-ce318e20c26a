import React from 'react'
import {Image, StyleSheet, View} from 'react-native'
import Progress from './Progress'
import res from '@ledvance/base/src/res'
import { useCreation } from 'ahooks'
import { useSystemTimeFormate } from '@ledvance/base/src/models/modules/NativePropsSlice'
import thermometer from '../circular/thermometer.png'

interface TimeCircularProps {
  planList: any[]
  onPanMoved?: (id: number, time: number) => void
  planEdit?: Boolean
  replaceStatus?: Boolean
  gradient?: Boolean
  isSupportTemperature: boolean
}

const TimeCircular = (
  props: TimeCircularProps = {
    planList: [],
    planEdit: false,
    replaceStatus: false,
    gradient: false,
    isSupportTemperature: false,
  },
) => {
  const {planList, onPanMoved, planEdit, replaceStatus, isSupportTemperature} = props
  const styles = dynamicStyleSheet
  const is24Hour = useSystemTimeFormate()
  const enabledPlanList = planList.filter(plan => plan.enable)
  const timeDialPic = useCreation(() =>{
    if(is24Hour){
      return res.ic_warning_amber_new
    }else{
      return res.ic_warning_amber_new_12
    }
  }, [is24Hour, replaceStatus])

  return (
    <View style={styles.container}>
      <Progress
        radius={130}
        annularHeight={44}
        timeIconList={enabledPlanList}
        planEdit={planEdit}
        gradient={true}
        isSupportTemperature={isSupportTemperature}
        onPanMoved={(id: number, time: number) => {
          onPanMoved && onPanMoved(id, time)
        }}>
        <Image source={timeDialPic} style={{width: 172, height: 172}}/>
        <Image source={thermometer} style={{width: 50, height: 120, position: 'absolute'}}/>
      </Progress>
    </View>
  )
}

const dynamicStyleSheet = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
})

export default TimeCircular
