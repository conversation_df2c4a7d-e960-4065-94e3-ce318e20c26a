import React, { useMemo } from 'react';
import { View } from 'react-native';
import ECharts from '@ledvance/react-native-echarts-pro';
import I18n from "@ledvance/base/src/i18n";
import { Utils } from 'tuya-panel-kit'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const BarChartWithTouch = ({ data, height, width, theme }) => {
  const dataX = data?.map(item => { return item.chartTitle });
  const dataY = data?.map(item => { return item.value });
  console.log(data, '< --- data --- >')
  const option = useMemo(() => (
    {
      title: {
        text: I18n.getLang('thermostat_unit'),
        textStyle: {
          fontSize: cx(14),
          color: theme.global.secondFontColor,
        },
        top: cx(10),
      },
      tooltip: {
        show: true,
        triggerOn: 'click',
        trigger: 'axis',
      },
      grid: {
        width,
        right: "20%",
        left: '10%'
      },
      xAxis: {
        data: dataX,
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: theme.dialog.subTitleFontColor,
          interval: Number(`${data?.length > 6 ? 1 : 0}`),
        }
      },
      yAxis: {
        min: data?.length === 0 && 0.6 || 0
      },
      series: [
        {
          name: '℃',
          type: 'line',
          data: dataY,
          smooth: true,
          showSymbol: true,
          color: '#F49431',
          selectedMode: "single",
        }
      ],
      dataZoom: {
        start: 0,
        type: "inside",
        maxValueSpan: 12
      },
      customMapData: {}
    }
  ), [JSON.stringify(dataX), JSON.stringify(dataY)])

  return (
    <View style={{ flex: 1, paddingLeft: cx(16) }}>
      <ECharts
        key={JSON.stringify(option)}
        option={option}
        height={height}
      />
    </View>
  );
};

export default withTheme(BarChartWithTouch);
