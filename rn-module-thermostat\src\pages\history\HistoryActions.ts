import { loopsText, monthFormat, monthFormatShort } from "@ledvance/base/src/utils/common";
import { DateType } from "@ledvance/ui-biz-bundle/src/newModules/energyConsumption/co2Data";
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import { TYSdk } from "tuya-panel-kit";
import { OverviewItem } from "./HistoryPage";
import RNFetchBlob from 'rn-fetch-blob'
import { Platform } from "react-native";
import { openDownloadFile } from "@ledvance/base/src/api/native";
import I18n from "@ledvance/base/src/i18n";

type TimeType = 'minute' | 'hour' | 'day' | 'month' | 'week';
type DataType = 'sum' | 'minux' | 'max' | 'avg';
interface Params {
  // 统计设备每15分钟上报的历史数据
  startMinute?: string; // 201805290000
  endMinute?: string; // 201805292359

  // 统计设备每小时上报的历史数据，只支持 avg 和 max
  date?: string; // 20180529

  // 统计设备每天上报的历史数据
  startDay?: string; // 20180529
  endDay?: string; // 20180531

  // 传入week的范围，返回这week范围内的数据
  startWeek?: string; // 20180531
  endWeek?: string; // 20180531

  // 设备的某个dp点的按月（一年）的统计结果
  startMonth?: string; // 20180531
  endMonth?: string; // 20180531

  auto?: 1 | 2;
  type?: DataType;
}
// 接口信息
const ApiConfig = {
  minute: 'tuya.m.dp.path.15minute.list',
  hour: 'tuya.m.dp.rang.stat.hour.list',
  day: 'tuya.m.dp.rang.stat.day.list',
  week: 'tuya.m.dp.rang.stat.week.list',
  month: 'tuya.m.dp.rang.stat.month.list',
};

const fetch = function (a: string, postData: any, v: string = '1.0') {
  return new Promise((resolve, reject) => {
    TYSdk.native.apiRNRequest(
      {
        a,
        postData,
        v,
      },
      (d: any) => {
        const data = typeof d === 'string' ? JSON.parse(d) : d;
        resolve(data);
      },
      (err: any) => {
        const e = typeof err === 'string' ? JSON.parse(err) : err;
        reject(e);
      }
    );
  });
};

export const getDataWithMonth = async (devId: string, dpId: string, dateStr: string, type?: DataType) => {
  const date = dayjs(dateStr)
  const currentYear = dayjs().get('year')
  const startMonth = date.startOf('year').format('YYYYMM')
  const endMonth = currentYear.toString() === dateStr ? dayjs().format('YYYYMM') : date.endOf('year').format('YYYYMM')
  const res: any = await fetch(ApiConfig.month, { devId, dpId, auto: 1, startMonth, endMonth, type: type ?? 'avg' })
  if (res && res.result) {
    const yearsList = Object.keys(res.result)
    yearsList.sort((a, b) => parseInt(b) - parseInt(a))
    const monthList = yearsList.map(year => {
      const yearStr = year.slice(0, 4)
      const monthStr = year.slice(4, 6)
      const curMonth = res.result[year]
      return {
        key: `${monthFormat(Number(monthStr))} ${yearStr}`,
        value: (Number(curMonth) || 0).toFixed(2),
        chartTitle: `${monthFormatShort(monthStr)}\n${yearStr}`
      }
    })
    return monthList.filter(m => Number(m.value) > 0)
  }
  return []
}

function getDate(input: string) {
  const now = dayjs();
  const inputDate = dayjs(input + '01'); // 解析输入的年月，格式为YYYYMMDD

  // 判断输入的年月是否为当前年月
  if (inputDate.format('YYYYMM') === now.format('YYYYMM')) {
    return now.format('YYYYMMDD'); // 返回当天日期
  } else {
    return inputDate.endOf('month').format('YYYYMMDD'); // 返回那个月的最后一天
  }
}

export const getDataWithDay = async (devId: string, dpId: string, dateStr: string, type?: DataType) => {
  const startDay = dateStr + '01'
  const endDay = getDate(dateStr)
  console.log(startDay,endDay, '< --- dateStr')
  const res: any = await fetch(ApiConfig.day, { devId, dpId, auto: 1, startDay, endDay, type: type ?? 'avg' })
  console.log(res, '< --- res')
  if (res && res.result) {
    const dayList = res.result
    const dayData = Object.keys(dayList).filter(v => Number(dayList[v]) > 0).map(time => {
      // 提取年、月和日
      const year = time.slice(0, 4);
      const month = time.slice(4, 6);
      const day = time.slice(6, 8);

      // 格式化为 'YYYY/MM/DD' 格式
      const formattedDate = `${year}/${month}/${day}`
      const dateStr = `${day}/${month}/${year}`
      const dateObj = dayjs(formattedDate, "YYYY/MM/DD");
      const dayOfWeek = dateObj.day() % 7;
      const key = `${dateStr} (${loopsText[dayOfWeek]})`
      return {
        key,
        value: Number(dayList[time] || 0).toFixed(2),
        chartTitle: `${Number(key?.split('/')[0])}\n${loopsText[dayOfWeek]}`
      }
    })
    return cloneDeep(dayData).reverse().filter(item => Number(item.value) > 0)
  }
  return []
}

export const getDataWithHour = async (devId: string, dpId: string, date: string, type?: DataType) => {
  const isOver7Days = overDays(date, 7)
  if (isOver7Days) return []
  const res: any = await fetch(ApiConfig.hour, { devId, dpId, auto: 1, date, type: type ?? 'avg' })
  const list: any[] = []
  const resData = Object.keys(res)?.map(val => {
    return { key: Number(val?.slice(8, 10)), value: Number(res[val]) }
  })
  for (let i = 0; i <= 23; i++) {
    const hourData = resData?.find(val => val?.key === i)
    const hourKey = hourData?.key || i
    const hourValue = Number(hourData?.value) || 0
    list.push({
      key: `${hourKey.toString().padStart(2, '0')}:00`, value: hourValue, chartTitle: `${hourKey}:00`
    })
  }
  return list
}

export const getDataWithDateType = async (dateType: DateType, devId: string, dpId: string, date: string, type?: DataType) => {
  let res: OverviewItem[] = []
  switch (dateType) {
    case DateType.Year:
      res = await getDataWithMonth(devId, dpId, date, type)
      break
    case DateType.Month:
      res = await getDataWithDay(devId, dpId, date, type)
      break
    case DateType.Day:
      res = await getDataWithHour(devId, dpId, date, type);
      break
  }
  return res
}

export function overDays(date, days) {
  console.log(date, ', --- date')
  // 处理输入，格式化为 dayjs 对象
  let parsedDate;
  
  if (typeof date === 'string') {
    if (date.length === 4) {
      // 传入的是年份，返回该年的最后一天
      parsedDate = dayjs(`${date}-12-31`);
    } else if (date.length === 6) {
      // 传入的是年份和月份，返回该月的最后一天
      parsedDate = dayjs(`${date}-01`).endOf('month');
    } else{
      parsedDate = dayjs(date)
    }
  } else {
    // 如果 date 不是字符串，尝试直接解析为 dayjs 对象
    parsedDate = dayjs(date);
  }

  // 计算当前日期减去指定天数的日期
  const comparisonDate = dayjs().subtract(days, 'day');

  // 返回布尔值，判断 parsedDate 是否早于 comparisonDate
  return parsedDate.isBefore(comparisonDate);
}