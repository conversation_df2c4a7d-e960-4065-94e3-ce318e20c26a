import React, {useCallback, useEffect, useMemo} from "react";
import {ScrollView, View, Image, TouchableOpacity} from 'react-native';
import Page from "@ledvance/base/src/components/Page";
import { useDeviceId, useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Utils } from 'tuya-panel-kit';
import Spacer from "@ledvance/base/src/components/Spacer";
import { useReactive, useUpdateEffect } from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import { exportCsvFile, getGlobalParamsDp, loopsText, monthFormat } from "@ledvance/base/src/utils/common";
import { getDataWithDateType, overDays } from "./HistoryActions";
import res from "@ledvance/base/src/res";
import DateSwitch from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/component/DateSwitch'
import DateTypeItem from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/component/DateTypeItem'
import DateSelectedItem from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/component/DateSelectedItem'
import { DateType } from "@ledvance/ui-biz-bundle/src/newModules/energyConsumption/co2Data";
import dayjs from "dayjs";
import BarChart from "./BarChart";
import InfoText from "@ledvance/base/src/components/InfoText";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx, width } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

export interface OverviewItem {
  key: string
  value: number | string
  chartTitle: string
}

const HistoryPage = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const devId = useDeviceId()
  const state = useReactive({
    dateType: DateType.Day,
    date: dayjs().format('YYYYMMDD'),
    headlineText: dayjs().format('DD/MM/YYYY'),
    overviewList: [] as OverviewItem[],
    loading: false,
    over365Days: false,
    over7Days: false
  });

  useEffect(() => {
    state.over365Days = overDays(state.date, 365);
    state.over7Days = overDays(state.date, 7);
    updateHeadlineText(dayjs(state.date));
    state.loading = true
    getDataWithDateType(state.dateType, devId, getGlobalParamsDp('temp_current'), state.date).then(res=> {
      state.loading = false
      state.overviewList = res
    })

  }, [state.date])

  const updateHeadlineText = useCallback((date: dayjs.Dayjs) => {
    const year = date.year().toString();
    const month = (date.month() + 1).toString().padStart(2, '0');
    const day = date.date().toString().padStart(2, '0');
    switch (state.dateType) {
      case DateType.Year:
        state.headlineText = year;
        break
      case DateType.Month:
        state.headlineText = `${monthFormat(month)} ${year}`;
        break
      case DateType.Day:
        state.headlineText = `${day}/${month}/${year}`;
        break
    }
  }, [state.dateType, state.headlineText]);

  useUpdateEffect(() => {
    const date = dayjs();
    const year = date.year().toString();
    const month = (date.month() + 1).toString().padStart(2, '0');
    const day = date.date().toString().padStart(2, '0');
    const dayOfWeek = date.day() % 7;
    switch (state.dateType) {
      case DateType.Year:
        state.date = year;
        state.headlineText = year;
        break
      case DateType.Month:
        state.date = `${year}${month}`
        state.headlineText = `${monthFormat(month)} ${year}`;
        break
      case DateType.Day:
        state.date = `${year}${month}${day}`
        state.headlineText = `${day}/${month}/${year}  ${loopsText[dayOfWeek]}`;
        break
    }
  }, [state.dateType]);

  const emptyDataTip = useMemo(() => {
    if (state.over365Days) {
      return I18n.getLang('energyconsumption_Daylimit')
    }
    if (state.dateType === DateType.Day && state.over7Days) {
      return I18n.getLang('energyconsumption_hourlylimit')
    }
    return I18n.getLang('history_overview_empty_information_text')
  }, [state.dateType, state.over365Days, state.over7Days]);

  return (
    <Page
      backText={devInfo.name}
      loading={state.loading}
      headlineContent={
        <View style={{width: '100%',flexDirection:'row'}}>
          <DateSwitch
            style={{flex: 1}}
            date={state.date}
            dateType={state.dateType}
            headlineText={state.headlineText}
            onDateChange={(date) => {
              state.date = date;
            }}/>
          <TouchableOpacity
            style={{width: cx(30)}}
            onPress={() => {
              const headers = [I18n.getLang('date'), `${I18n.getLang('thermostat_tempcurrent')} (℃)`]
              const functionName = `${I18n.getLang('thermostat_tempcurrent')}`
              const values = state.overviewList.map(item => [item.key, item.value])
              exportCsvFile(headers, values, functionName)
            }}>
            <Image
              style={{
                width: cx(24),
                height: cx(24),
                tintColor: props.theme?.global.brand,
                position: 'absolute',
                right: 0,
                top: cx(10)
              }}
              source={{ uri: state.overviewList?.length ? res.download_icon : undefined}}/>
          </TouchableOpacity>
        </View>
      }
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer />
        <View style={{ flexDirection: 'row', marginHorizontal: cx(24) }}>
          <DateTypeItem
            style={{ flex: 1 }}
            dateType={state.dateType}
            onDateTypeChange={(dateType) => {
              state.dateType = dateType;
            }} />
          <DateSelectedItem
            style={{ flex: 1, marginStart: cx(10), marginBottom: cx(15) }}
            dateType={state.dateType}
            date={state.date}
            onDateChange={date => {
              state.date = date;
            }}
          />
        </View>
        {
          !!state.overviewList.length ?
            <BarChart height={cx(400)} data={state.overviewList} width={width - cx(80)} /> :
            <View style={{ flex: 1, alignItems: 'center', marginHorizontal: cx(16) }}>
              <Spacer />
              <Image
                style={{ width: cx(200), height: cx(200) }}
                source={{ uri: res.energy_consumption_empty}} />
              <InfoText
                text={emptyDataTip}
                icon={res.co2_icon}
                iconStyle={{ tintColor: props.theme?.global.fontColor }}
                textStyle={{ color: props.theme?.global.fontColor, flex: 0 }}
              />
            </View>
        }
      </ScrollView>
    </Page>
  );
};


export default withTheme(HistoryPage);
