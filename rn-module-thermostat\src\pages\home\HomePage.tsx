import {ScrollView, View, Image, Text, StyleSheet, TouchableOpacity, FlatList} from 'react-native';
import React, {useCallback, useEffect, useMemo} from 'react';
import {useDeviceId, useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice';
import BatteryPercentageView from "@ledvance/base/src/components/BatteryPercentageView";
import {Utils} from 'tuya-panel-kit';
import Spacer from '@ledvance/base/src/components/Spacer';
import res from '@ledvance/base/src/res';
import {
  useBatteryPercentage,
  useAdvancedData,
  useTempCurrent,
  useManualTemp,
  useWorkMode,
  useAutoTemp,
  useRapid,
  useRapidTime,
  useFeatureChildLock
} from 'hooks/FeatureHooks';
import Page from '@ledvance/base/src/components/Page';
import {NativeApi} from '@ledvance/base/src/api/native';
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import ThermostatCard from 'components/ThermostatCard';
import { useReactive} from 'ahooks';
import {useVacationMode} from 'pages/vacationMode/VacationModeActions';
import {cloneDeep, debounce} from 'lodash';
import I18n from '@ledvance/base/src/i18n';
import {useSceneStatusId} from '@ledvance/ui-biz-bundle/src/newModules/mood/MoodActions';
import {SceneStatusType} from '@ledvance/base/src/utils/interface';
import {useChildLock} from '@ledvance/ui-biz-bundle/src/newModules/childLock/ChildLockPage';
import {getGlobalParamsDp} from '@ledvance/base/src/utils/common';
import ThemeType from "@ledvance/base/src/config/themeType";
import {getRemoteMoodList} from "../mood/MoodPageActions";
import Card from "@ledvance/base/src/components/Card";
import {getIconByTemp, MoodInfo} from "../mood/MoodInfo";
import { useLdvEventListener } from '@ledvance/base/src/eventEmitter/hooks';
import { ldvEventEmitter } from "@ledvance/base/src/eventEmitter";

const {convertX: cx} = Utils.RatioUtils;
const {withTheme} = Utils.ThemeUtils

function HomePage(props: { theme?: ThemeType }) {
  const deviceInfo = useDeviceInfo();
  const devId = useDeviceId()
  const [manualTemp, setManualTemp] = useManualTemp()
  const [autoTemp, setAutoTemp] = useAutoTemp()
  const [{temp}] = useVacationMode()
  const [sceneStatusId, setSceneStatusId] = useSceneStatusId({
    isSupportSceneStatus: true,
    sceneStatusType: SceneStatusType.Mood
  })
  const [featureChildLock, setFeatureChildLock] = useFeatureChildLock()
  const advancedData = useAdvancedData({sceneStatusId, setSceneStatusId, setFeatureChildLock});
  const batteryPercentage = useBatteryPercentage();
  const [workMode, setWorkMode] = useWorkMode()
  const [childLock, setChildLock] = useChildLock(getGlobalParamsDp('child_lock'))
  const [rapid] = useRapid()
  const [rapidTime] = useRapidTime()
  const currentTemp = useTempCurrent()
  const state = useReactive({
    loading: false,
    sceneStatusId,
    originMoods: [] as MoodInfo[],
    refresh: Symbol()
  })

  useLdvEventListener(ldvEventEmitter, 'refreshThermostatMood', () => {
    state.refresh = Symbol()
  })

  const thermostatTemp = useMemo(() => {
    return workMode === 'auto' ? autoTemp : workMode === 'manual' ? manualTemp : temp
  }, [workMode, manualTemp, autoTemp, temp])

  useEffect(() => {
    state.sceneStatusId = sceneStatusId
  }, [sceneStatusId]);

  useEffect(() => {
    getRemoteMoodList(devId, false).then(res => {
      if (res.success && Array.isArray(res.data)) {
        state.originMoods = cloneDeep(res.data)
      }
    })
  }, [state.refresh]);

  const debounceOnTempChange = useCallback(
    debounce(async (v) => {
      state.loading = true;
      if (workMode === 'auto') {
        await setAutoTemp(v);
      } else {
        await setManualTemp(v);
      }
      if (state.sceneStatusId !== -1) {
        setSceneStatusId(-1).then()
      }
      state.loading = false;
    }, 500),
    [workMode, setAutoTemp, setManualTemp, state.sceneStatusId]
  );

  const setTempAndMode = (mood: MoodInfo) =>{
    if (workMode === 'auto'){
      setAutoTemp(mood.temp).then()
    }
    if (workMode === 'manual'){
      setManualTemp(mood.temp).then()
    }
    if (workMode === 'holiday'){
      setWorkMode('manual').then()
      setManualTemp(mood.temp).then()
    }
  }

  const setMood = (item: MoodInfo) => {
    if (item.id === sceneStatusId) {
      return
    }
    state.sceneStatusId = item.id
    setTempAndMode(item)
    setSceneStatusId(item.id).then()
  }

  const styles = StyleSheet.create({
    thermostatText: {
      fontSize: cx(50),
      alignSelf: 'center',
      color: props.theme?.global.brand,
      fontWeight: 'bold'
    },
    moodCard: {
      marginHorizontal: cx(24),
      paddingHorizontal: cx(10),
      paddingTop: cx(10)
    },
    moodRow: {
      justifyContent: 'space-around',
      marginBottom: cx(10)
    },
    moodItem: {
      flex: 1,
      width: cx(70),
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: cx(8),
      borderColor: props.theme?.card.background,
    },
    selected: {
      borderWidth: 1,
      borderRadius: cx(8),
      borderColor: props.theme?.icon.primary,
    }
  })

  return (
    <Page
      backText={useFamilyName()}
      onBackClick={NativeApi.back}
      headlineText={deviceInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(deviceInfo.devId);
      }}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={{flex: 1}}>
          <Spacer height={cx(4)}/>
          <BatteryPercentageView value={batteryPercentage} />
          <Spacer/>
          <ThermostatCard
            tempValue={thermostatTemp}
            currentTemp={currentTemp}
            disableControl={workMode === 'holiday'}
            showMode={true}
            activeMode={workMode}
            isRapidHeating={rapid}
            rapidHeating={
              <View>
                <Text
                  style={[styles.thermostatText, {fontSize: cx(16)}]}>{I18n.getLang('timer_ceiling_fan_headline_text')}</Text>
                <Text style={styles.thermostatText}>{`${rapidTime} S`}</Text>
              </View>
            }
            onTempChange={debounceOnTempChange}
            onActiveKeyChange={async (v) => {
              state.loading = true
              if (state.sceneStatusId !== -1) {
                setSceneStatusId(-1).then()
              }
              if (v === 'holiday' && !childLock) {
                await setChildLock(true)
              }
              if (workMode === 'holiday' && v !== 'holiday') {
                await setChildLock(featureChildLock)
              }
              await setWorkMode(v)
              state.loading = false
            }}
          />
          {
            state.originMoods.length > 0 &&
              <View>
                  <Spacer height={cx(16)}/>
                  <Card style={styles.moodCard}>
                      <FlatList
                          data={state.originMoods}
                          numColumns={4}
                          keyExtractor={(item) => `${item.id}`}
                          columnWrapperStyle={styles.moodRow}
                          renderItem={(item) => {
                            return <TouchableOpacity onPress={() => setMood(item.item)}>
                              <View style={[styles.moodItem, item.item.id === state.sceneStatusId ? styles.selected : null]}>
                                <Image source={getIconByTemp(item.item.temp)} style={{width: cx(24), height: cx(24)}}/>
                                <Text style={{color: props.theme?.global.fontColor, fontSize: cx(12)}}>{item.item.name}</Text>
                              </View>
                            </TouchableOpacity>
                          }}
                        />
                  </Card>
              </View>
          }
          <Spacer height={cx(16)}/>
          <AdvanceList advanceData={advancedData}/>
        </View>
      </ScrollView>
    </Page>
  );
}

export default withTheme(HomePage)
