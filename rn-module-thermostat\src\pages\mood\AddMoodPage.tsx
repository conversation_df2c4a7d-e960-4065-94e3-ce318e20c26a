import React, { useCallback } from "react";
import { FlatList, StyleSheet, Text } from 'react-native'
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { Utils } from "tuya-panel-kit";
import ThemeType from "@ledvance/base/src/config/themeType";
import RecommendModeItem from "./RecommendModeItem";
import { defRecommendMoodList, MoodInfo, RecommendMood } from "./MoodInfo";
import { difference, head, range } from "lodash";
import { RouterKey } from "navigation/Router";
import { useNavigation } from '@react-navigation/native';
import { useParams } from "@ledvance/base/src/hooks/Hooks";
import { Result } from "@ledvance/base/src/models/modules/Result";
import Spacer from "@ledvance/base/src/components/Spacer";

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

export interface AddMoodPageParams {
  isStatic: boolean;
  modeIds: number[];
  nameRepeat: (mood: MoodInfo) => boolean
  modDeleteMood: (mode: 'add' | 'edit' | 'del', currentMood: MoodInfo) => Promise<Result<any>>;
}

const AddMoodPage = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation();
  const params = useParams<AddMoodPageParams>()

  const onMoodItemClick = useCallback(
    (moodItem: RecommendMood, index: number) => {
      const idRange = range(0, 256);
      const id: number = head(difference(idRange, params.modeIds)) || 0;
      const url = RouterKey.mood_edit;
      const currentMood = {
        ...moodItem,
        id,
        name: index === 0 ? '' : moodItem.name
      }
      navigation.navigate(url, {
        ...params,
        mode: 'add',
        currentMood,
      });
    },
    [JSON.stringify(params)]
  );

  const styles = StyleSheet.create({
    root: {
      flex: 1,
      flexDirection: 'column',
    },
    desc: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      marginHorizontal: cx(24),
      marginTop: cx(12),
    },
  })

  return (
    <Page
      backText={I18n.getLang('thermostat_scene')}
      headlineText={I18n.getLang('routines_add_scence_doalog_headline')}
    >
      <Text style={styles.desc}>
        {I18n.getLang('thermostat_scenedescription')}
      </Text>
      <FlatList
        style={{ flex: 1 }}
        data={defRecommendMoodList}
        renderItem={({ item, index }) => {
          return (
            <RecommendModeItem
              title={item.name}
              mood={item}
              onPress={() => {
                onMoodItemClick(item, index);
              }}
            />
          );
        }}
        ItemSeparatorComponent={() => <Spacer />}
        ListHeaderComponent={() => <Spacer />}
        ListFooterComponent={() => <Spacer />}
        keyExtractor={item => item.name}
      />
    </Page>
  )
}

export default withTheme(AddMoodPage);