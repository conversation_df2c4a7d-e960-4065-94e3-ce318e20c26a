import React from 'react';
import { Image, StyleSheet, Text, View, ViewProps, ViewStyle } from 'react-native';
import { SwitchButton, Utils } from 'tuya-panel-kit';
import Card from '@ledvance/base/src/components/Card';
import Spacer from '@ledvance/base/src/components/Spacer';
import MoodColorsLine from '@ledvance/base/src/components/MoodColorsLine';
import { getIconByTemp, MoodInfo } from './MoodInfo';
import { getNodeColorByTemp } from 'pages/autoMode/AutoModeActions';
import ThemeType from "@ledvance/base/src/config/themeType";

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils;

interface MoodItemProps extends ViewProps {
  theme?: ThemeType
  enable: boolean;
  mood: MoodInfo;
  style?: ViewStyle;
  onPress?: () => void;
  onSwitch: (enable: boolean) => void;
}

const MoodItem = (props: MoodItemProps) => {
  const { mood } = props;

  const styles = StyleSheet.create({
    card: {
      marginHorizontal: cx(24),
    },
    headline: {
      flexDirection: 'row',
      marginHorizontal: cx(16),
    },
    headText: {
      flex: 1,
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
      lineHeight: cx(20),
    },
    gradientItem: {
      flexDirection: 'row',
    },
    gradientItemIconView: {
      width: cx(24),
      height: cx(24),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#aaa',
      borderRadius: cx(8),
    },
    gradientItemIcon: {
      width: cx(16),
      height: cx(16),
      tintColor: props.theme?.global.background,
    },
    gradient: {
      borderRadius: cx(8),
    },
    moodTypeItem: {
      flexDirection: 'row',
    },
    moodTypeLabel: {
      marginStart: cx(16),
      paddingHorizontal: cx(12.5),
      backgroundColor: props.theme?.tag.background,
      borderRadius: cx(8),
    },
    moodTypeLabelText: {
      height: cx(16),
      color: '#000000DD',
      fontSize: cx(10),
      textAlignVertical: 'center',
      fontFamily: 'helvetica_neue_lt_std_roman',
      lineHeight: cx(16),
    },
  });

  const lightColors = [getNodeColorByTemp(mood.temp)]

  return (
    <Card style={[styles.card, props.style]} onPress={props.onPress}>
      <View>
        <Spacer height={cx(16)} />
        <View style={styles.headline}>
          <Text style={styles.headText}>{mood.name}</Text>
          <SwitchButton
            thumbStyle={{ elevation: 0 }}
            value={props.enable}
            onValueChange={props.onSwitch}
          />
        </View>
        <Spacer />
        <View style={styles.headline}>
          <MoodColorsLine
            nodeStyle={{ borderColor: props.theme?.icon.disable, borderWidth: 1 }}
            width={cx(260)}
            type={'separate'}
            colors={lightColors}
          />
          <Spacer width={cx(10)} />
          <Image
            source={getIconByTemp(mood.temp)}
            style={{ width: cx(24), height: cx(24) }}
          />
        </View>
        <Spacer height={cx(16)} />
      </View>
    </Card>
  );
};

export default withTheme(MoodItem);

