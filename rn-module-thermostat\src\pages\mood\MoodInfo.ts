import I18n from "@ledvance/base/src/i18n";
import res from "./res";

export interface RecommendMood {
  name: string
  temp: number
  tempIdx: number
}

export interface MoodInfo extends RecommendMood{
  id: number
}

export const defMoodList: MoodInfo[] = [
  {
    id: 4,
    name: I18n.getLang('thermostat_maximum', 'Maximum'),
    temp: 29.5,
    tempIdx: 59
  },
  {
    id: 1,
    name: I18n.getLang('thermostat_warm'),
    temp: 21,
    tempIdx: 42
  },
  {
    id: 2,
    name: I18n.getLang('thermostat_cool'),
    temp: 18,
    tempIdx: 36
  },
  {
    id: 3,
    name: I18n.getLang('thermostat_minimum', 'Minimum'),
    temp: 0.5,
    tempIdx: 1
  },
]

export const defRecommendMoodList: RecommendMood[] = [
  {
    name: I18n.getLang('add_new_dynamic_mood_field_headline_text1'),
    temp: 17,
    tempIdx: 34
  },
  ...defMoodList
]

export function getIconByTemp(temp: number) {
  if (temp <= 20.5) {
    return temp === 0.5 ? res.ic_mood_cold : res.ic_mood_cool;
  } else{
    return temp === 29.5 ? res.ic_mood_hot : res.ic_mood_warm;
  }
}
