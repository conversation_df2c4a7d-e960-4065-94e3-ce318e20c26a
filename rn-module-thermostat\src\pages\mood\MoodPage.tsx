import React, { useCallback, useEffect, useMemo } from "react";
import { FlatList, View, TouchableOpacity, Image } from 'react-native';
import Spacer from '@ledvance/base/src/components/Spacer';
import InfoText from '@ledvance/base/src/components/InfoText';
import { Utils } from "tuya-panel-kit";
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { useParams } from "@ledvance/base/src/hooks/Hooks";
import { useDeviceId, useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { useNavigation } from '@react-navigation/core';
import res from "@ledvance/base/src/res";
import { useReactive, useUpdateEffect } from "ahooks";
import { showDialog } from "@ledvance/base/src/utils/common";
import ModeItem from "./ModeItem";
import { RouterKey } from "navigation/Router";
import { cloneDeep, map } from "lodash";
import { getRemoteMoodList, saveRemoteMoodList } from "./MoodPageActions";
import { MoodInfo } from "./MoodInfo";
import { useAutoTemp, useManualTemp, useWorkMode } from "hooks/FeatureHooks";
import ThemeType from "@ledvance/base/src/config/themeType";
import { ldvEventEmitter } from '@ledvance/base/src/eventEmitter'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface MoodPageParams {
  sceneStatusId: number
  setSceneStatusId: (v: number) => Promise<any>
}

interface ClassicModeState {
  originMoods: MoodInfo[]
  loading: boolean
  sceneStatusId: number
}

const MAX_MOOD_COUNT = 255

const MoodPage = (props: { theme?: ThemeType }) => {
  const params = useParams<MoodPageParams>();
  const deviceInfo = useDeviceInfo();
  const devId = useDeviceId();
  const navigation = useNavigation();
  const [workMode, setWorkMode] = useWorkMode()
  const [, setAutoTemp] = useAutoTemp()
  const [, setManualTemp] = useManualTemp()

  const state = useReactive<ClassicModeState>({
    originMoods: [],
    loading: false,
    sceneStatusId: params.sceneStatusId
  })

  useEffect(() => {
    getModeList()
  }, [])

  useUpdateEffect(() => {
    state.sceneStatusId = params.sceneStatusId
  }, [params.sceneStatusId])

  const getModeList = (isRefresh?: boolean) => {
    state.loading = true
    getRemoteMoodList(devId, isRefresh).then(res => {
      state.loading = false
      if (res.success && Array.isArray(res.data)) {
        state.originMoods = cloneDeep(res.data)
      }
    }).catch(() => {
      state.loading = false
    })
  }

  const modeIds = useMemo(() => {
    const ids: number[] = map(state.originMoods, 'id')
    return ids
  }, [JSON.stringify(state.originMoods)])

  const nameRepeat = useCallback((mood: MoodInfo) => {
    return !!state.originMoods.filter(m => m.id !== mood.id).find(n => n.name === mood.name)
  }, [JSON.stringify(state.originMoods)])


  const navigationRoute = (mode: 'add' | 'edit', currentMood?: MoodInfo) => {
    const path = mode === 'add' ? RouterKey.mood_add : RouterKey.mood_edit
    navigation.navigate(path, {
      mode,
      currentMood,
      modeIds,
      nameRepeat,
      modDeleteMood
    })
  }

  const modDeleteMood = async (mode: 'add' | 'edit' | 'del' | 'set', currentMood: MoodInfo) => {
    let newScene: MoodInfo[] = [];
    if (mode === 'add') {
      newScene = [currentMood, ...state.originMoods]
    }
    if (mode === 'del') {
      newScene = state.originMoods.filter(item => item.id !== currentMood.id)
    }
    if (mode === 'set') {
      state.sceneStatusId = currentMood.id
      setTempAndMode(currentMood)
      return params.setSceneStatusId(currentMood.id)
    }
    if (mode === 'edit') {
      newScene = state.originMoods.map(item => {
        if (item.id === currentMood.id) {
          return currentMood
        }
        return item
      })
    }
    const mood = mode === 'del' ? (newScene.length === 0 ? undefined : newScene[0]) : currentMood
    const res = await saveRemoteMoodList(devId, newScene)
    if (res.success) {
      if (mode === 'add' || mode === 'del') {
        ldvEventEmitter.emit('refreshThermostatMood')
      }
      state.originMoods = cloneDeep(newScene)
      if (mood) {
        if (mode === 'del') {
          if (params.sceneStatusId !== -1 && (params.sceneStatusId === currentMood.id)) {
            setTempAndMode(mood)
            await params.setSceneStatusId(mood.id)
            state.sceneStatusId = mood.id
          }
          return { success: true }
        } else {
          setTempAndMode(currentMood)
          await params.setSceneStatusId(mood.id)
          state.sceneStatusId = mood.id
          return { success: true }
        }
      } else {
        await params.setSceneStatusId(-1)
        state.sceneStatusId = -1
        return {
          success: true
        }
      }
    }
    return { success: false }
  }

  const setTempAndMode = (mood: MoodInfo) =>{
    if (workMode === 'auto'){
      setAutoTemp(mood.temp).then()
    }
    if (workMode === 'manual'){
      setManualTemp(mood.temp).then()
    }
    if (workMode === 'holiday'){
      setWorkMode('manual').then()
      setManualTemp(mood.temp).then()
    }
  }

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('thermostat_scene')}
      headlineIcon={state.originMoods.length < MAX_MOOD_COUNT ? res.add : undefined}
      onHeadlineIconClick={() => {
        navigationRoute('add')
      }}
      loading={state.loading}
    >
      <TouchableOpacity style={{ alignItems: 'flex-end', paddingRight: cx(24) }}
        onPress={() => {
          showDialog({
            method: 'confirm',
            title: I18n.getLang('mood_resetbutton'),
            subTitle: I18n.getLang('reset_mooddescription'),
            onConfirm: (_, { close }) => {
              close()
              ldvEventEmitter.emit('refreshThermostatMood')
              getModeList(true)
            }
          })
        }}
      >
        <Image source={{ uri: res.ic_refresh}} style={{ width: cx(24), height: cx(24), tintColor: props.theme?.global.fontColor }} />
      </TouchableOpacity>
      <Spacer height={cx(10)} />
      {state.originMoods.length >= MAX_MOOD_COUNT && (
        <View style={{ marginHorizontal: cx(24) }}>
          <Spacer height={cx(10)} />
          <InfoText
            icon={res.ic_warning_amber}
            text={I18n.getLang('mood_overview_warning_max_number_text')}
            contentColor={props.theme?.global.warning}
          />
          <Spacer height={cx(6)} />
        </View>
      )}
      <FlatList
        data={state.originMoods}
        renderItem={({ item }) => {
          return (
            <ModeItem
              enable={state.sceneStatusId === item.id}
              mood={item}
              onPress={() => {
                navigationRoute('edit', item);
              }}
              onSwitch={async _ => {
                state.loading = true;
                await modDeleteMood('set', item);
                state.loading = false;
              }}
            />
          );
        }}
        ListHeaderComponent={() => <Spacer height={cx(10)} />}
        ItemSeparatorComponent={() => <Spacer />}
        ListFooterComponent={() => <Spacer />}
        keyExtractor={item => `${item.id}`}
      />
    </Page>
  )
}

export default withTheme(MoodPage)
