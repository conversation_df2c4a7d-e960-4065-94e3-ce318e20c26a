import { NativeApi } from "@ledvance/base/src/api/native"
import { defMoodList, MoodInfo } from "./MoodInfo"
import { parseJSON } from "@tuya/tuya-panel-lamp-sdk/lib/utils"

const MoodListPutJsonId = 'MoodList'

export const getRemoteMoodList = async (devId: string, isRefresh?: boolean) =>{
  if (isRefresh){
    const res = await NativeApi.putJson(devId, MoodListPutJsonId, JSON.stringify(defMoodList))
    if (res.success){
      return {
        success: true,
        data: defMoodList
      }
    }
  }
  const res = await NativeApi.getJson(devId, MoodListPutJsonId)
  const isNormalData = Array.isArray(parseJSON(res?.data))
  if (res.success && isNormalData) {
    return {
      success: true,
      data: JSON.parse(res.data)
    }
  } else {
    if (res.msg?.includes('资源未找到') || !isNormalData) {
      const res = await NativeApi.putJson(devId, MoodListPutJsonId, JSON.stringify(defMoodList))
      if (res.success) {
        return {
          success: true,
          data: defMoodList
        }
      }
      return { success: false }
    }
    return { success: false }
  }
}

export const saveRemoteMoodList = (devId: string, modeList: MoodInfo[]) =>{
  return NativeApi.putJson(devId, MoodListPutJsonId, JSON.stringify(modeList))
}