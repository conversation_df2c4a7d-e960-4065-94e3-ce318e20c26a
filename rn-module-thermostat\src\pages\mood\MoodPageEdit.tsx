import React, { useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import Page from '@ledvance/base/src/components/Page';
import I18n from '@ledvance/base/src/i18n';
import res from '@ledvance/base/src/res';
import { useNavigation } from '@react-navigation/native';
import { useParams } from '@ledvance/base/src/hooks/Hooks';
import { MoodInfo } from './MoodInfo';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { useReactive, useUpdateEffect } from 'ahooks';
import { Utils } from 'tuya-panel-kit';
import ThemeType from '@ledvance/base/src/config/themeType';
import TextField from '@ledvance/base/src/components/TextField';
import Spacer from '@ledvance/base/src/components/Spacer';
import DeleteButton from '@ledvance/base/src/components/DeleteButton';
import { showDialog } from '@ledvance/base/src/utils/common';
import { RouterKey } from 'navigation/Router';
import ThermostatCard from 'components/ThermostatCard';
import { useTempCurrent } from 'hooks/FeatureHooks';
import { cloneDeep, isEqual } from 'lodash';

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils;

export interface MoodPageEditParams {
  mode: 'add' | 'edit';
  currentMood: MoodInfo;
  nameRepeat: (mood: MoodInfo) => boolean;
  modDeleteMood: (mode: 'add' | 'edit' | 'del', currentMood: MoodInfo) => Promise<Result<any>>;
}

const MoodPageEdit = (props: { theme?: ThemeType }) => {
  const navigation = useNavigation();
  const params = useParams<MoodPageEditParams>();
  const currentTemp = useTempCurrent();
  const state = useReactive({
    mood: cloneDeep(params.currentMood),
    loading: false,
  });

  const nameRepeat = useMemo(() => {
    return params.nameRepeat(state.mood);
  }, [state.mood.name]);

  const checkMoodChanged = useMemo(() => {
    return isEqual(state.mood, params.currentMood)
  }, [JSON.stringify(state.mood), JSON.stringify(params.currentMood)])

  const canSaveMoodData = useMemo(() => {
    return state.mood.name.length > 0 && state.mood.name.length < 33 && !nameRepeat && (params.mode === 'add' || !checkMoodChanged)
  }, [nameRepeat, state.mood.name, checkMoodChanged, params.mode])

  useUpdateEffect(() =>{
    console.log(state.mood)
  }, [JSON.stringify(state.mood)])
  return (
    <Page
      backText={I18n.getLang('thermostat_scene')}
      headlineText={I18n.getLang(params.mode === 'add' ? 'routines_add_scence_doalog_headline' : 'thermostat_editscene')}
      showBackDialog={!checkMoodChanged}
      backDialogTitle={I18n.getLang(
        params.mode === 'add'
          ? 'thermostat_cancelscene'
          : 'manage_user_unsaved_changes_dialog_headline'
      )}
      backDialogContent={I18n.getLang(
        params.mode === 'add'
          ? 'thermostat_infocancelscene'
          : 'thermostat_inputlosteditscene'
      )}
      rightButtonIcon={canSaveMoodData ? res.ic_check : res.ic_uncheck}
      loading={state.loading}
      rightButtonIconClick={async () => {
        if(state.loading || !canSaveMoodData) return
        state.loading = true;
        const res = await params.modDeleteMood(params.mode, state.mood)
        state.loading = false;
        if (res.success) {
          navigation.navigate(RouterKey.mood)
        }
      }}
    >
      <ScrollView style={{ flex: 1 }} nestedScrollEnabled={true}>
        <TextField
          style={{ marginHorizontal: cx(24) }}
          value={state.mood.name}
          placeholder={I18n.getLang('edit_static_mood_inputfield_topic_text')}
          onChangeText={text => {
            state.mood.name = text;
          }}
          maxLength={33}
          showError={state.mood.name.length > 32 || nameRepeat}
          tipColor={nameRepeat ? props.theme?.global.error : undefined}
          tipIcon={nameRepeat ? { uri: res.ic_text_field_input_error } : undefined}
          errorText={I18n.getLang(
            nameRepeat ? 'string_light_pp_field_sm_add_error1' : 'add_new_dynamic_mood_alert_text'
          )}
        />
        <Spacer />
        <ThermostatCard
          currentTemp={currentTemp}
          tempValue={state.mood.tempIdx * 0.5}
          onTempChange={v => {
            state.mood.tempIdx = Math.trunc(v / 0.5);
            state.mood.temp = v
          }}
        />
        <Spacer height={cx(30)} />
        {params.mode === 'edit' && <View style={{ marginHorizontal: cx(24) }}>
          <DeleteButton
            text={I18n.getLang('edit_static_mood_button_delete_text')}
            onPress={() => {
              showDialog({
                method: 'confirm',
                title: I18n.getLang('thermostat_deletescene'),
                subTitle: I18n.getLang('strip_light_static_mood_edit_dialog_text'),
                onConfirm: async (_, { close }) => {
                  close();
                  state.loading = true;
                  const res = await params.modDeleteMood('del', state.mood)
                  state.loading = false;
                  if (res.success) {
                    navigation.navigate(RouterKey.mood)
                  }
                },
              });
            }}
          />
          <Spacer />
        </View>}
      </ScrollView>
    </Page>
  );
};

export default withTheme(MoodPageEdit);
