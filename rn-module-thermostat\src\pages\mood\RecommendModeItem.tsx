import React from 'react';
import { StyleSheet, View, Image } from 'react-native';
import { Utils } from 'tuya-panel-kit';
import Card from '@ledvance/base/src/components/Card';
import { CellContent } from '@ledvance/base/src/components/Cell';
import Spacer from '@ledvance/base/src/components/Spacer';
import MoodColorsLine from '@ledvance/base/src/components/MoodColorsLine';
import { getIconByTemp, MoodInfo } from './MoodInfo';
import { getNodeColorByTemp } from 'pages/autoMode/AutoModeActions';
import ThemeType from "@ledvance/base/src/config/themeType";

const cx = Utils.RatioUtils.convertX;
const { withTheme } = Utils.ThemeUtils;

interface RecommendMixMoodItemProps {
  theme?: ThemeType;
  title: string;
  mood: MoodInfo;
  onPress: () => void;
}

const RecommendMixMoodItem = (props: RecommendMixMoodItemProps) => {
  const { mood } = props;

  const styles = StyleSheet.create({
    root: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: cx(24),
    },
    content: {
      height: cx(56),
      marginHorizontal: cx(16),
      width: cx(295),
    },
    title: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    lineStyle: {
      flexDirection: 'row',
      marginHorizontal: cx(16)
    },
  });

  const lightColors = [getNodeColorByTemp(mood.temp)]

  return (
    <Card style={styles.root} onPress={props.onPress}>
      <CellContent
        title={props.title}
        value={''}
        style={styles.content}
        titleStyle={styles.title}
        iconStyle={{
          color: props.theme?.global.fontColor,
          size: cx(16),
        }}
      />
      {!!mood.id && (
        <>
          <View style={styles.lineStyle}>
            <MoodColorsLine
              nodeStyle={{ borderColor: props.theme?.icon.disable, borderWidth: 1}}
              width={cx(260)}
              type={'separate'}
              colors={lightColors}
            />
            <Spacer width={cx(10)} />
            <Image source={getIconByTemp(mood.temp)} style={{ width: cx(24), height: cx(24) }} />
          </View>
          <Spacer height={cx(24)} />
        </>
      )}
    </Card>
  );
};

export default withTheme(RecommendMixMoodItem);
