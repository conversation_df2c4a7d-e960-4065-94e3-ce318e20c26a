import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { useNavigation } from '@react-navigation/core';
import { useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Modal, Utils } from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import res from "@ledvance/base/src/res";
import { useReactive, useUpdateEffect } from "ahooks";
import { useManualTemp } from "hooks/FeatureHooks";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

const PowerModePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo()
  const navigation = useNavigation();
  const [manualTemp, setManualTemp] = useManualTemp()

  const state = useReactive({
    manualTemp,
    tempModal: false
  })

  useUpdateEffect(() =>{
    state.manualTemp = manualTemp
  }, [manualTemp])

  const styles = StyleSheet.create({
    overviewDescription: {
      color: props.theme?.global.fontColor,
      marginHorizontal: cx(24),
    },
    itemContainer: {
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      borderRadius: cx(6),
      padding: cx(10)
    },
    touchableItem: {
      paddingHorizontal: cx(10),
      backgroundColor: props.theme?.global.background,
      borderRadius: cx(6),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: cx(40)
    },
    text: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    }
  })

  return (
    <Page
      backText={deviceInfo.name}
      onBackClick={navigation.goBack}
      headlineText={I18n.getLang('thermostat_powermode')}
    >
      <Text style={styles.overviewDescription}>
        {I18n.getLang('thermostat_descriptionrapid')}
      </Text>
      <Spacer />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() =>{
            state.tempModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_settemp')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{`${state.manualTemp} ℃`}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20) }} />
          </View>
        </TouchableOpacity>
      </View>
      <Modal.Picker
        title={I18n.getLang('thermostat_settemp')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        visible={state.tempModal}
        dataSource={getTempItemList()}
        value={`${manualTemp}`}
        label="℃"
        textSize={cx(18)}
        labelOffset={cx(30)}
        onMaskPress={() => {
          state.tempModal = false;
        }}
        onCancel={() =>{
          state.tempModal = false
        }}

        onConfirm={async (v) =>{
          state.tempModal = false
          await setManualTemp(Number(v))
          state.manualTemp = Number(v)
        }}
      />
    </Page>
  )
}

const getTempItemList = () => {
  return Array.from({ length: 59 }, (_, idx) => ({ label: `${(idx + 1) * 0.5}`, value: `${(idx + 1) * 0.5}` }))
}

export default withTheme(PowerModePage)
