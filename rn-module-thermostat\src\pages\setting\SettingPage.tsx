import React, { useMemo } from "react";
import { ScrollView, StyleSheet, Text } from 'react-native';
import Page from "@ledvance/base/src/components/Page";
import { useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Utils, Modal } from 'tuya-panel-kit';
import Spacer from "@ledvance/base/src/components/Spacer";
import { useReactive } from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import CardItem from "components/CardItem";
import { useNavigation } from '@react-navigation/core';
import { RouterKey } from "navigation/Router";
import { useRapid, useRefresh, useTempDrift } from "hooks/FeatureHooks";
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import Card from "@ledvance/base/src/components/Card";
import res from "@ledvance/base/src/res";
import AnimateImage from "components/AnimateImage";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const Setting = (props: { theme?: ThemeType }) => {
  const devInfo = useDeviceInfo()
  const navigation = useNavigation()
  const [tempDrift, setTempDrift] = useTempDrift()
  const [rapid, setRapid] = useRapid()
  const [, setRefresh] = useRefresh()
  const state = useReactive({
    loading: false,
    comfortTempModal: false,
    ecoTempModal: false,
    driftTempModal: false
  });


  const tempItemList = useMemo(() => {
    const tempList1 = Array.from({ length: 111 }, (_, i) => ({ label: `${parseFloat((-5.5 + 0.1 * i).toFixed(1))}`, value: `${parseFloat((-5.5 + 0.1 * i).toFixed(1))}` }))
    const tempList2 = Array.from({ length: 59 }, (_, idx) => ({ label: `${(idx + 1) * 0.5}`, value: `${(idx + 1) * 0.5}` }))
    return state.driftTempModal ? tempList1 : tempList2
  }, [state.comfortTempModal, state.driftTempModal, state.ecoTempModal])

  const styles = StyleSheet.create({
    refreshTitle: {
      fontSize: cx(16),
      fontWeight: 'bold',
      color: props.theme?.global.fontColor
    },
    refreshContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: cx(16)
    },
    refreshImage: {
      width: cx(28),
      height: cx(28),
      tintColor: props.theme?.global.brand
    },
    cardItemDescription: {
      color: props.theme?.global.secondFontColor,
      fontSize: cx(12),
      marginHorizontal: cx(10),
      marginTop: -cx(10)
    }
  })

  return (
    <Page
      backText={devInfo.name}
      headlineText={I18n.getLang('contact_sensor_specific_settings')}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer height={cx(16)} />
        <Card
          containerStyle={styles.refreshContainer}
          style={{ marginHorizontal: cx(24) }}>
          <Text style={styles.refreshTitle}>{I18n.getLang('thermostat_refresh')}</Text>
          <AnimateImage
            onPress={async () => {
              await setRefresh(true)
            }}
            source={{ uri: res.ic_refresh}} />
        </Card>
        <Spacer />
        <Card style={{ marginHorizontal: cx(24) }}>
          <LdvSwitch
            title={I18n.getLang('thermostat_quicktemp')}
            colorAlpha={1}
            enable={rapid}
            setEnable={async (v) => {
              await setRapid(v)
            }}
          />
        </Card>
        <Spacer />
        {/* <CardItem
          title={I18n.getLang('thermostat_comforttemp')}
          content={`${comfortTemp} ℃`}
          showEnable={workMode !== 'holiday'}
          enable={currentModeTemp === comfortTemp}
          onEnableChange={async (v) => {
            if (v) {
              if (workMode === 'auto') {
                await setAutoTemp(comfortTemp)
              } else {
                await setManualTemp(comfortTemp)
              }
            }
          }}
          onPress={() => {
            state.comfortTempModal = true
          }}
        >
          <Text style={styles.cardItemDescription}>{I18n.getLang('thermostat_descriptionrapid')}</Text>
          <Spacer height={cx(16)} />
        </CardItem>
        <Spacer />
        <CardItem
          title={I18n.getLang('thermostat_energysaving')}
          content={`${ecoTemp} ℃`}
          showEnable={workMode !== 'holiday'}
          enable={currentModeTemp === ecoTemp}
          onEnableChange={async (v) => {
            if (v) {
              if (workMode === 'auto') {
                await setAutoTemp(ecoTemp)
              } else {
                await setManualTemp(ecoTemp)
              }
            }
          }}
          onPress={() => {
            state.ecoTempModal = true
          }}
        >
          <Text style={styles.cardItemDescription}>{I18n.getLang('thermostat_descriptionrapid')}</Text>
          <Spacer height={cx(16)} />
        </CardItem>
        <Spacer /> */}
        <CardItem
          title={I18n.getLang('thermostat_openwindow')}
          content=""
          onPress={() => {
            navigation.navigate(RouterKey.window_reminder)
          }}
        />
        <Spacer />
        <CardItem
          title={I18n.getLang('thermostat_drifttemp')}
          content={`${tempDrift} ℃`}
          onPress={() => {
            state.driftTempModal = true
          }}
        >
          <Text style={styles.cardItemDescription}>{I18n.getLang('thermostat_difference')}</Text>
          <Spacer height={cx(16)} />
        </CardItem>
        <Spacer />
      </ScrollView>
      <Modal.Picker
        title={I18n.getLang('thermostat_tempsetting')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        visible={state.driftTempModal || state.comfortTempModal || state.ecoTempModal}
        dataSource={tempItemList}
        label="℃"
        textSize={cx(20)}
        labelOffset={cx(30)}
        onMaskPress={() => {
          state.driftTempModal = false;
          state.comfortTempModal = false
          state.ecoTempModal = false
        }}
        value={`${tempDrift}`}
        onCancel={() => {
          state.driftTempModal = false;
          state.comfortTempModal = false
          state.ecoTempModal = false
        }}
        onConfirm={async (v) => {
          if (state.driftTempModal) {
            await setTempDrift(Number(v))
          }
          state.driftTempModal = false;
        }}
      />
    </Page>
  );
};


export default withTheme(Setting);
