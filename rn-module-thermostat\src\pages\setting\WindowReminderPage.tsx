import React, { useEffect, useMemo } from "react";
import { ScrollView, View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import Page from "@ledvance/base/src/components/Page";
import { Utils, Modal } from 'tuya-panel-kit';
import Spacer from "@ledvance/base/src/components/Spacer";
import { useReactive } from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import res from "@ledvance/base/src/res";
import { useWindowTemp, useWindowTime } from "hooks/FeatureHooks";
import ThemeType from "@ledvance/base/src/config/themeType";


const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const Setting = (props: { theme?: ThemeType }) => {
  const [windowTemp, setWindowTemp] = useWindowTemp()
  const [windowTime, setWindowTime] = useWindowTime()
  const state = useReactive({
    loading: false,
    tempModal: false,
    timeModal: false
  });

  const tempItemList = useMemo(() => {
    return Array.from({ length: 59 }, (_, idx) => ({ label: `${(idx + 1) * 0.5}`, value: `${(idx + 1) * 0.5}` }))
  }, [])

  const timeItemList = useMemo(() => {
    return Array.from({ length:61 }, (_, idx) => ({ label: `${idx}`, value: `${idx}` }))
  }, [])

  const styles = StyleSheet.create({
    itemContainer: {
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      borderRadius: cx(6),
      padding: cx(10)
    },
    touchableItem: {
      paddingHorizontal: cx(10),
      backgroundColor: props.theme?.global.background,
      borderRadius: cx(6),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: cx(40)
    },
    text: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    }
  })

  return (
    <Page
      backText={I18n.getLang('contact_sensor_specific_settings')}
      headlineText={I18n.getLang('thermostat_openwindow')}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <Spacer height={cx(16)} />
        <View style={styles.itemContainer}>
          <TouchableOpacity
            style={styles.touchableItem}
            onPress={() => {
              state.tempModal = true
            }}
          >
            <Text style={styles.text}>{I18n.getLang('thermostat_setscope')}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={styles.text}>{`${windowTemp} ℃`}</Text>
              <Spacer width={cx(5)} />
              <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20), tintColor: props.theme?.card.fontColor }} />
            </View>
          </TouchableOpacity>
          <Spacer height={cx(10)} />
          <TouchableOpacity
            style={styles.touchableItem}
            onPress={() => {
              state.timeModal = true
            }}
          >
            <Text style={styles.text}>{I18n.getLang('thermostat_settime')}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={styles.text}>{`${windowTime} min`}</Text>
              <Spacer width={cx(5)} />
              <Image source={{ uri: res.energy_consumption_right}} style={{ width: cx(20), height: cx(20), tintColor: props.theme?.card.fontColor }} />
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
      <Modal.Picker
        title={I18n.getLang(state.tempModal ? 'thermostat_setscope' : 'thermostat_settime')}
        visible={state.tempModal || state.timeModal}
        dataSource={state.tempModal ? tempItemList : timeItemList}
        label={state.tempModal ? `℃` : 'min'}
        textSize={cx(20)}
        labelOffset={cx(30)}
        onMaskPress={() => {
          state.tempModal = false;
          state.timeModal = false
        }}
        value={`${state.tempModal ? windowTemp : windowTime}`}
        onCancel={() => {
          state.tempModal = false
          state.timeModal = false
        }}
        onConfirm={async (v) => {
          if (state.tempModal){
            setWindowTemp(Number(v))
          }else{
            setWindowTime(Number(v))
          }
          state.tempModal = false
          state.timeModal = false
        }}
      />
    </Page>
  );
};

export default withTheme(Setting);
