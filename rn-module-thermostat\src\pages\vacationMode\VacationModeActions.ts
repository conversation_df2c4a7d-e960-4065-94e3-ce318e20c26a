import { useDp } from '@ledvance/base/src/models/modules/NativePropsSlice';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { getGlobalParamsDp, hex2Int } from '@ledvance/base/src/utils/common';
import { to16 } from '@tuya/tuya-panel-lamp-sdk/lib/utils';
import dayjs from 'dayjs';

interface VacationMode {
  year: number;
  month: number;
  day: number;
  hour: number;
  min: number;
  temp: number;
  rangeTime: number;

  endYear: number;
  endMonth: number;
  endDay: number;
  endHour: number;
  endMin: number;
}

type UseVacationModeType = [VacationMode, (v: VacationMode) => Promise<Result<any>>];
export const useVacationMode = (): UseVacationModeType => {
  const [vacationDp, setVacation] = useDp<string, any>(getGlobalParamsDp('holiday_set'));
  const setVacationFn = (vacation: VacationMode) => {
    return setVacation(obj2Dp(vacation));
  };
  return [dp2Obj(vacationDp), setVacationFn];
};

const getDefVacation = () => {
  const today = dayjs();
  return {
    year: today.year(),
    month: today.month() + 1,
    day: today.date(),
    hour: 0,
    min: 0,
    temp: 21,
    rangeTime: 0,
    // 结束时间
    endYear: today.year(),
    endMonth: today.month() + 1,
    endDay: today.date(),
    endHour: 0,
    endMin: 0,
  };
};

const dp2Obj = (dp: string): VacationMode => {
  if (!dp) return getDefVacation()
  const year = hex2Int(dp.slice(0, 2)) + 2000;
  const month = hex2Int(dp.slice(2, 4));
  const day = hex2Int(dp.slice(4, 6));
  const hour = hex2Int(dp.slice(6, 8));
  const min = hex2Int(dp.slice(8, 10));
  const temp = hex2Int(dp.slice(10, 12)) * 0.5;
  const rangeTime = hex2Int(dp.slice(12, 16));
  const endDate = getEndDate({ year, month, day, hour, min, rangeTime });
  return { year, month, day, hour, min, temp, rangeTime, ...endDate };
};

const obj2Dp = (vacation: VacationMode) => {
  const yearHex = to16(vacation.year - 2000);
  const monthHex = to16(vacation.month);
  const dayHex = to16(vacation.day);
  const hourHex = to16(vacation.hour);
  const minHex = to16(vacation.min);
  const tempHex = to16(vacation.temp / 0.5);
  const ranTimeHex = to16(vacation.rangeTime, 4);
  return yearHex + monthHex + dayHex + hourHex + minHex + tempHex + ranTimeHex;
};

export const getEndDate = (vacation: any) => {
  const start = new Date(
    vacation.year,
    vacation.month - 1, // JavaScript 中的月份从0开始
    vacation.day,
    vacation.hour,
    vacation.min
  );
  const end = new Date(start.getTime() + vacation.rangeTime * 60 * 60 * 1000);
  return {
    endYear: end.getFullYear(),
    endMonth: end.getMonth() + 1,
    endDay: end.getDate(),
    endHour: end.getHours(),
    endMin: end.getMinutes(),
  };
};
