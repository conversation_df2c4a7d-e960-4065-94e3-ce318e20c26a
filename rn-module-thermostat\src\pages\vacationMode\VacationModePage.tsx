import React, { useMemo } from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Page from "@ledvance/base/src/components/Page";
import I18n from "@ledvance/base/src/i18n";
import { useNavigation } from '@react-navigation/core';
import { useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Modal, Utils } from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import res from "@ledvance/base/src/res";
import { useReactive, useUpdateEffect } from "ahooks";
import { useVacationMode } from "./VacationModeActions";
import { showDialog } from "@ledvance/base/src/utils/common";
import { cloneDeep, isEqual } from "lodash";
import ThemeType from "@ledvance/base/src/config/themeType";

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

const VacationModePage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo()
  const navigation = useNavigation();
  const [vacation, setVacation] = useVacationMode()
  const state = useReactive({
    vacation,
    changeData: cloneDeep(vacation),
    startTimeModal: false,
    endTimeModal: false,
    startDateModal: false,
    endDateModal: false,
    tempModal: false,
    loading: false,
    flag: Symbol()
  })

  // useUpdateEffect(() => {
  //   state.vacation = vacation
  //   state.changeData = cloneDeep(vacation)
  // }, [JSON.stringify(vacation)])

  useUpdateEffect(() =>{
    state.changeData = cloneDeep(state.vacation)
  }, [JSON.stringify(state.vacation)])

  const getNewDate = (date: any, isEnd?: boolean) => {
    return new Date(
      date[isEnd ? 'endYear' : 'year'],
      date[isEnd ? 'endMonth' : 'month'] - 1, // JavaScript 中的月份从0开始
      date[isEnd ? 'endDay' : 'day'],
      date[isEnd ? 'endHour' : 'hour'],
      date[isEnd ? 'endMin' : 'min']
    );
  }

  const getRangeTime = (startDate, endDate) => {
    // 计算时间差（毫秒）
    const timeDifference = Math.abs(startDate - endDate);

    // 将时间差转换为小时
    const hoursDifference = Math.round(timeDifference / (1000 * 60 * 60));
    // 判断是否在最大范围内
    return hoursDifference;
  }

  useUpdateEffect(() =>{
    setVacation(state.vacation).then()
  }, [state.flag])

  const startDate = useMemo(() => {
    return `${state.vacation.day}-${state.vacation.month}-${state.vacation.year}`
  }, [state.vacation.year, state.vacation.month, state.vacation.day])

  const startTime = useMemo(() => {
    return `${state.vacation.hour.toString().padStart(2, '0')}:${state.vacation.min.toString().padStart(2, '0')}`
  }, [state.vacation.hour, state.vacation.min])

  const endDate = useMemo(() => {
    return `${state.vacation.endDay}-${state.vacation.endMonth}-${state.vacation.endYear}`
  }, [state.vacation.endYear, state.vacation.endMonth, state.vacation.endDay])

  const endTime = useMemo(() => {
    return `${state.vacation.endHour.toString().padStart(2, '0')}:${state.vacation.endMin.toString().padStart(2, '0')}`
  }, [state.vacation.endHour, state.vacation.endMin])

  const canSaveMode = useMemo(() =>{
    return !isEqual(vacation, state.vacation)
  }, [JSON.stringify(vacation), JSON.stringify(state.vacation)])

  const styles = StyleSheet.create({
    overviewDescription: {
      color: props.theme?.global.fontColor,
      marginHorizontal: cx(24),
    },
    itemContainer: {
      marginHorizontal: cx(24),
      backgroundColor: props.theme?.container.background,
      borderRadius: cx(6),
      padding: cx(10)
    },
    touchableItem: {
      paddingHorizontal: cx(10),
      backgroundColor: props.theme?.global.background,
      borderRadius: cx(6),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: cx(40)
    },
    text: {
      color: props.theme?.global.fontColor,
      fontSize: cx(14)
    },
    arrow: {
      width: cx(20),
      height: cx(20),
      tintColor: props.theme?.global.fontColor
    }
  })

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('thermostat_vacationmode')}
      showBackDialog={canSaveMode}
      backDialogTitle={I18n.getLang('cancel_dialog_leave_unsaved_titel')}
      backDialogContent={I18n.getLang('cancel_dialog_leave_unsaved_vacation_note')}
      rightButtonIcon={canSaveMode ? res.ic_check : res.ic_uncheck}
      loading={state.loading}
      rightButtonIconClick={async () =>{
        if (state.loading || !canSaveMode) return
        const start = getNewDate(state.vacation)
        const end = getNewDate(state.vacation, true)
        if (start > end){
          return showDialog({
            method: 'alert',
            title: I18n.getLang('thermostat_error'),
            onConfirm: (_, { close }) => {
              close()
            }
          })
        }
        if (state.vacation.rangeTime > 2400) {
          return showDialog({
            method: 'alert',
            title: I18n.getLang('thermostat_maxtime'),
            onConfirm: (_, { close }) => {
              close()
            }
          })
        }
        state.loading = true
        const res = await setVacation(state.vacation)
        state.loading = false
        if (res.success){
          navigation.goBack()
        }
      }}
    >
      <Text style={styles.overviewDescription}>
        {I18n.getLang('thermostat_descriptionvacation')}
      </Text>
      <Spacer />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            state.startDateModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_startdate')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{startDate}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={styles.arrow} />
          </View>
        </TouchableOpacity>
        <Spacer height={cx(10)} />
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            state.startTimeModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_starttime')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{startTime}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={styles.arrow} />
          </View>
        </TouchableOpacity>
      </View>
      <Spacer />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            state.endDateModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_enddate')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{endDate}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={styles.arrow} />
          </View>
        </TouchableOpacity>
        <Spacer height={cx(10)} />
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            state.endTimeModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_endtime')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{endTime}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={styles.arrow} />
          </View>
        </TouchableOpacity>
      </View>
      <Spacer />
      <View style={styles.itemContainer}>
        <TouchableOpacity
          style={styles.touchableItem}
          onPress={() => {
            state.tempModal = true
          }}
        >
          <Text style={styles.text}>{I18n.getLang('thermostat_tempsetting')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.text}>{`${state.vacation.temp} ℃`}</Text>
            <Spacer width={cx(5)} />
            <Image source={{ uri: res.energy_consumption_right}} style={styles.arrow} />
          </View>
        </TouchableOpacity>
      </View>

      <Modal.DatePicker
        title={I18n.getLang(state.startDateModal ? 'thermostat_startdate' : 'thermostat_enddate')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        titleTextStyle={{fontSize: cx(16), fontWeight: 'bold'}}
        visible={state.startDateModal || state.endDateModal}
        minDate={new Date(2000, 0, 1)}
        maxDate={new Date(2099, 11, 31)}
        dateSortKeys={['day', 'month', 'year']}
        textSize={cx(20)}
        date={getNewDate(state.vacation, state.endDateModal)}
        onMaskPress={() => {
          state.startDateModal = false;
          state.endDateModal = false;
          state.changeData = cloneDeep(state.vacation)
        }}
        onCancel={() => {
          state.startDateModal = false;
          state.endDateModal = false;
          state.changeData = cloneDeep(state.vacation)
        }}
        onValueChange={(v, index) => {
          const startT = ['day', 'month', 'year']
          const endT = ['endDay', 'endMonth', 'endYear']
          const key = state.startDateModal ? startT[index ?? 0] : endT[index ?? 0]
          state.changeData[key] = Number(v)
        }}
        onConfirm={() => {
          let start = getNewDate(state.vacation)
          let end = getNewDate(state.vacation, true)
          if (state.startDateModal) {
            start = getNewDate(state.changeData)
          } else {
            end = getNewDate(state.changeData, true)
          }
          const rangeTime = getRangeTime(start, end)
          state.vacation = {
            ...state.changeData,
            rangeTime,
          }
          state.startDateModal = false;
          state.endDateModal = false;
        }}
      />

      <Modal.DatePicker
        title={I18n.getLang(state.startTimeModal ? 'thermostat_starttime' : 'thermostat_endtime')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        titleTextStyle={{fontSize: cx(16), fontWeight: 'bold'}}
        mode="time"
        visible={state.startTimeModal || state.endTimeModal}
        textSize={cx(20)}
        date={getNewDate(state.vacation, state.endTimeModal)}
        onMaskPress={() => {
          state.startTimeModal = false;
          state.endTimeModal = false;
          state.changeData = cloneDeep(state.vacation)
        }}
        onCancel={() => {
          state.startTimeModal = false;
          state.endTimeModal = false;
          state.changeData = cloneDeep(state.vacation)
        }}
        onValueChange={(v, index) => {
          const startT = ['hour', 'min']
          const endT = ['endHour', 'endMin']
          const key = state.startTimeModal ? startT[index ?? 0] : endT[index ?? 0]
          state.changeData[key] = Number(v)
        }}
        onConfirm={() => {
          const min = state.startTimeModal ? state.changeData.min : state.changeData.endMin
          let start = getNewDate(state.vacation)
          let end = getNewDate(state.vacation, true)
          if (state.startTimeModal) {
            start = getNewDate(state.changeData)
          } else {
            end = getNewDate({
              ...state.changeData,
              endMin: min
            }, true)
            start = getNewDate({
              ...state.vacation,
              min
            })
          }
          const rangeTime = getRangeTime(start, end)
          state.vacation = {
            ...state.changeData,
            rangeTime,
            min,
            endMin: min,
          }
          state.startTimeModal = false;
          state.endTimeModal = false;
        }}
      />

      <Modal.Picker
        title={I18n.getLang('thermostat_tempsetting')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        titleTextStyle={{fontSize: cx(16), fontWeight: 'bold'}}
        visible={state.tempModal}
        dataSource={getTempItemList()}
        label="℃"
        textSize={cx(20)}
        labelOffset={cx(30)}
        onMaskPress={() => {
          state.tempModal = false;
        }}
        value={`${state.vacation.temp}`}
        onCancel={() => {
          state.tempModal = false
        }}
        onConfirm={async (v) => {
          state.vacation.temp = Number(v)
          state.tempModal = false
        }}
      />

    </Page>
  )
}

const getTempItemList = () => {
  return Array.from({ length: 59 }, (_, idx) => ({ label: `${(idx + 1) * 0.5}`, value: `${(idx + 1) * 0.5}` }))
}

export default withTheme(VacationModePage)
