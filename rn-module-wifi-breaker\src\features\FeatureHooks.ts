import { AdvancedData, AdvancedStatus, getAdvancedStatusColor } from '@ledvance/base/src/components/AdvanceCard'
import { useDeviceId, useDp, useTimeSchedule } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { getGlobalParamsDp, isSupportFunctions, localeNumber } from '@ledvance/base/src/utils/common'
import { createParams } from '@ledvance/base/src/hooks/Hooks'
import { RouterKey } from '../navigation/Router'
import { RandomTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimePage';
import { FixedTimePageParams } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimePage';
import { useFixedTime } from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/FixedTimeActions';
import { useRandomTime } from '@ledvance/ui-biz-bundle/src/newModules/randomTime/RandomTimeActions';
import { SwitchInchingPageParams, useSwitchInching } from '@ledvance/ui-biz-bundle/src/newModules/swithInching/SwithInchingAction'
import { PowerBehaviorPageParams, usePowerBehavior } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/PowerOnBehaviorActions'
import { TimeSchedulePageParams } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/TimeSchedulePage';
import I18n, { I18nKey } from '@ledvance/base/src/i18n';
import { ApplyForItem } from '@ledvance/base/src/utils/interface';
import { cloneDeep } from 'lodash';
import { useReactive, useUpdateEffect } from 'ahooks';
import { useCallback, useEffect, useMemo } from 'react';
import { Result } from '@ledvance/base/src/models/modules/Result';
import { DeviceType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { DeviceStateType } from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Interface';
import { useCountdowns, timeFormatToRealTime } from '@ledvance/ui-biz-bundle/src/modules/timer/TimerPageAction';
import { NativeApi } from '@ledvance/base/src/api/native';
import { SwitchHistoryPageRouteParams } from "@ledvance/ui-biz-bundle/src/modules/history/HistoryPage";

export function useSwitch() {
  return useDp<boolean, any>(getGlobalParamsDp('switch_1'))
}

export function useSwitch2() {
  return useDp<boolean, any>(getGlobalParamsDp('switch_2'))
}

export function useElectricCurrent(): number {
  const current = useDp<number, any>(getGlobalParamsDp('cur_current'))[0] || 0
  return localeNumber(current, 1)
}

export function useVoltage(): number {
  const voltage = useDp<number, any>(getGlobalParamsDp('cur_voltage'))[0] / 10 || 0;
  return localeNumber(voltage, 1)
}

export function usePower(): number {
  const power = useDp<number, any>(getGlobalParamsDp('cur_power'))[0] / 10 || 0;
  return localeNumber(power, 1)
}

type SwitchType = 'flip' | 'sync' | 'button'
export function useSwitchType() {
  return useDp<SwitchType, any>(getGlobalParamsDp('switch_type'))
}

interface SwitchNames {
  title: string
  secondaryTitle: string
  key: number
}

export function useSwitchNames(): [SwitchNames[], (value: SwitchNames[]) => Promise<Result<any>>] {
  const devId = useDeviceId()
  const is2Channel = isSupportFunctions('switch_2')
  const singleChannel = [
    {
      title: I18n.getLang('Onoff_button_socket'),
      secondaryTitle: I18n.getLang('switchmodule_switchdescription'),
      key: 0
    }
  ]
  const twoChannel = [
    {
      title: I18n.getLang('switchmodule_switch1title'),
      secondaryTitle: I18n.getLang('switchmodule_switch1description'),
      key: 0
    },
    {
      title: I18n.getLang('switchmodule_switch2title'),
      secondaryTitle: I18n.getLang('switchmodule_switch2description'),
      key: 1
    }
  ]
  const state = useReactive({
    switchNames: is2Channel ? twoChannel : singleChannel
  })

  useEffect(() => {
    NativeApi.getJson(devId, 'breakerTitle')
      .then(res => {
        if (res.success && res.data && JSON.parse(res.data)?.length) {
          const data = JSON.parse(res.data)
          const newSwitchNames = [...state.switchNames]
          newSwitchNames.forEach(item => {
            item.secondaryTitle = data.find((it: SwitchNames) => it.key === item.key)?.secondaryTitle || item.secondaryTitle
          })
          state.switchNames = newSwitchNames
        }
      })
  }, [])
  const setRemoteSwitchNames = useCallback(async (value: SwitchNames[]) => {
    state.switchNames = value
    return await NativeApi.putJson(devId, 'breakerTitle', JSON.stringify(value))
  }, [])

  return [state.switchNames, setRemoteSwitchNames]
}

function joinSocketName(key: I18nKey, name?: string) {
  const arr = [I18n.getLang(key)]
  name && arr.push(name)
  return arr.join(' | ')
}

export function useAdvanceData(switchNames: SwitchNames[]): AdvancedData[] {
  const advanceData = [] as AdvancedData[]
  const deviceId = useDeviceId();
  const [timeSchedule, setTimeSchedule] = useTimeSchedule();
  const [randomTimeList] = useRandomTime(getGlobalParamsDp('random_time'), true, true);
  const [fixedTimeList] = useFixedTime(getGlobalParamsDp('cycle_time'), true, true);
  const [switchInching] = useSwitchInching(getGlobalParamsDp('switch_inching'))
  const [powerBehaviors] = usePowerBehavior(['relay_status'])
  const [switch1] = useSwitch()
  const [switch2] = useSwitch2()
  const state = useReactive({
    timeScheduleStatus: AdvancedStatus.Disable,
    randomTimeStatus: AdvancedStatus.Disable,
    fixedTimeStatus: AdvancedStatus.Disable,
  })

  useEffect(() => {
    if (deviceId) {
      NativeApi.timerList(deviceId).then(res => {
        if (res.result && res.value) {
          const status = !!res.value.find((item: any) => !!item.status);
          setTimeSchedule(status);
        }
      });
    }
  }, [deviceId]);

  useUpdateEffect(() => {
    state.timeScheduleStatus = timeSchedule ? AdvancedStatus.Enable : AdvancedStatus.Disable;
  }, [timeSchedule]);

  useEffect(() => {
    state.randomTimeStatus = randomTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(randomTimeList)]);

  useEffect(() => {
    state.fixedTimeStatus = fixedTimeList.some(item => item.enable)
      ? AdvancedStatus.Enable
      : AdvancedStatus.Disable;
  }, [JSON.stringify(fixedTimeList)]);

  const plugApplyFor = useMemo(() => {
    const applyFor: ApplyForItem[] = []
    if (isSupportFunctions('switch_1')) {
      applyFor.push({
        type: 'socket',
        key: switchNames[0].title,
        name: switchNames[0].title,
        dp: getGlobalParamsDp('switch_1'),
        enable: true,
      })
    }
    if (isSupportFunctions('switch_2')) {
      applyFor.push({
        type: 'socket',
        key: switchNames[1].title,
        name: switchNames[1].title,
        dp: getGlobalParamsDp('switch_2'),
        enable: true,
      })
    }
    return applyFor
  }, [isSupportFunctions('switch_1'), isSupportFunctions('switch_2')])

  const manualDataDp2Obj = useCallback(() => {
    const deviceState: DeviceStateType = {
      // @ts-ignore
      deviceData: {
        type: DeviceType.LightSource,
        deviceData: {
          h: 0,
          s: 100,
          v: 100,
          brightness: 100,
          temperature: 0,
          isColorMode: false,
        },
      },
      isManual: true,
      mood: undefined,
    };
    return deviceState;
  }, []);

  const manualDataObj2Dp = useCallback(
    (_, applyForList: ApplyForItem[]) => {
      const manualDps = {};
      applyForList.forEach(apply => {
        manualDps[apply.dp] = apply.enable;
      })
      return manualDps;
    },
    []
  );

  advanceData.push({
    title: I18n.getLang('timeschedule_add_schedule_system_back_text'),
    statusColor: getAdvancedStatusColor(state.timeScheduleStatus),
    dp: { key: '', code: '' }, // schedule 没有dp值
    router: {
      key: RouterKey.ui_biz_time_schedule_new,
      params: {
        applyForList: cloneDeep(plugApplyFor),
        applyForDisabled: !isSupportFunctions('switch_2'),
        manualDataDp2Obj,
        manualDataObj2Dp,
        isSupportColor: false,
        isSupportBrightness: false,
        isSupportTemperature: false,
      } as TimeSchedulePageParams,
    }
  })

  if (isSupportFunctions('countdown_1')) {
    const params = useMemo(() => {
      const dps: any[] = []
      dps.push({
        label: joinSocketName(isSupportFunctions('switch_2') ? 'switchmodule_switch1title' : 'Onoff_button_socket', switchNames[0]?.secondaryTitle),
        cloudKey: 'socketInfo',
        dpId: getGlobalParamsDp('countdown_1'),
        enableDp: getGlobalParamsDp('switch_1'),
        stringOn: isSupportFunctions('switch_2') ? 'timer_switch1_active_timer_field_description_on_text' : 'timer_switch_active_timer_field_description_on_text',
        stringOff: isSupportFunctions('switch_2') ? 'timer_switch1_active_timer_field_description_off_text' : 'timer_switch_active_timer_field_description_off_text',
      })
      if (isSupportFunctions('switch_2')) {
        dps.push({
          label: joinSocketName('switchmodule_switch2title', switchNames[1]?.secondaryTitle),
          cloudKey: 'socketInfo',
          dpId: getGlobalParamsDp('countdown_2'),
          enableDp: getGlobalParamsDp('switch_2'),
          stringOn: 'timer_switch2_active_timer_field_description_on_text',
          stringOff: 'timer_switch2_active_timer_field_description_off_text',
        })
      }
      return createParams({ dps })
    }, [JSON.stringify(switchNames)])
    console.log(params.dps, '< --- params.dps')
    const tasks = useCountdowns(params.dps)
    const timerTask = useMemo(() => {
      return tasks.filter(timer => timer.countdown[0] > 0).map(timer => {
        let switchLed = false
        let item = ''
        switch (timer.dpId) {
          case getGlobalParamsDp('countdown_1'):
            switchLed = switch1
            item = isSupportFunctions('countdown_2') ? '1' : ''
            break
          case getGlobalParamsDp('countdown_2'):
            switchLed = switch2
            item = '2'
            break
        }
        // @ts-ignore
        const key: I18nKey = `switch${item}_active_timer_field_small_${switchLed ? 'off' : 'on'}_text`
        return I18n.formatValue(key, timeFormatToRealTime(timer.countdown[0]))
      })
    }, [switch1, switch2, JSON.stringify(tasks)])

    advanceData.push({
      title: I18n.getLang('timer_ceiling_fan_headline_text'),
      subtitles: timerTask,
      statusColor: getAdvancedStatusColor(
        timerTask.length > 0 ? AdvancedStatus.Enable : AdvancedStatus.Disable
      ),
      dp: { key: 'countdown_1', code: getGlobalParamsDp('countdown_1') },
      router: {
        key: RouterKey.ui_biz_timer,
        params
      }
    })
  }

  if (isSupportFunctions('cycle_time')) {
    const params = createParams<FixedTimePageParams>({
      fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
      conflictDps: {
        randomTimeDpCode: getGlobalParamsDp('random_time'),
        switchIngCode: getGlobalParamsDp('switch_inching')
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
      showTags: isSupportFunctions('switch_2')
    });

    advanceData.push({
      title: I18n.getLang('fixedTimeCycle_socket_headline'),
      statusColor: getAdvancedStatusColor(state.fixedTimeStatus),
      dp: { key: 'cycle_time', code: getGlobalParamsDp('cycle_time') },
      router: {
        key: RouterKey.ui_biz_fixed_time_new,
        params
      }
    })
  }

  if (isSupportFunctions('random_time')) {
    const params = createParams<RandomTimePageParams>({
      randomTimeDpCode: getGlobalParamsDp('random_time'),
      conflictDps: {
        fixedTimeDpCode: getGlobalParamsDp('cycle_time'),
        switchIngCode: getGlobalParamsDp('switch_inching')
      },
      applyForList: cloneDeep(plugApplyFor),
      isPlug: true,
      showTags: isSupportFunctions('switch_2')
    });

    advanceData.push({
      title: I18n.getLang('randomtimecycle_sockets_headline_text'),
      statusColor: getAdvancedStatusColor(state.randomTimeStatus),
      dp: { key: 'random_time', code: getGlobalParamsDp('random_time') },
      router: {
        key: RouterKey.ui_biz_random_time_new,
        params
      }
    })
  }

  if (isSupportFunctions('relay_status')) {
    const params = createParams<PowerBehaviorPageParams>({
      powerBehaviorKeys: ['relay_status']
    })
    advanceData.push({
      title: I18n.getLang('sockets_specific_settings_relay_status'),
      statusColor: getAdvancedStatusColor(powerBehaviors.some(item => !!item) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'relay_status', code: getGlobalParamsDp('relay_status') },
      router: {
        key: RouterKey.ui_biz_power_behavior_plug,
        params
      },
    })
  }

  if (isSupportFunctions('switch_inching')) {
    const params = useMemo(() => {
      const channelConfig = [{channel: 0,countdownCode: getGlobalParamsDp('countdown_1'),channelTitle: I18n.getLang(isSupportFunctions('switch_2') ? 'switchmodule_switch1title' : 'Onoff_button_socket')}]
      if (isSupportFunctions('switch_2')) {
        channelConfig.push({
          channel: 1,
          countdownCode: getGlobalParamsDp('countdown_2'),
          channelTitle: I18n.getLang('switchmodule_switch2title'),
        })
      }
      return createParams<SwitchInchingPageParams>({
        switchIngCode: getGlobalParamsDp('switch_inching'),
        channelConfig,
        conflictDps: {
          randomTimeDpCode: getGlobalParamsDp('random_time'),
          fixedTimeDpCode: getGlobalParamsDp('cycle_time')
        }
      })
    }, [])
    advanceData.push({
      title: I18n.getLang('sockets_specific_settings_switch_inching'),
      statusColor: getAdvancedStatusColor(switchInching.some(item => item.enable) ? AdvancedStatus.Enable : AdvancedStatus.Disable),
      dp: { key: 'switch_inching', code: getGlobalParamsDp('switch_inching') },
      router: {
        key: RouterKey.ui_biz_switch_inching,
        params
      },
    })
  }

  if(isSupportFunctions('switch_type') || isSupportFunctions('switch_interlock') ){
    advanceData.push({
      title: I18n.getLang('contact_sensor_specific_settings'),
      dp: { key: '', code: 'setting' },
      router: {
        key: RouterKey.settings,
      },
    })
  }

  const historyParams = useMemo(() => {
    const dpIds =  [getGlobalParamsDp('switch_1')]
    if (isSupportFunctions('switch_2')) {
      dpIds.push(getGlobalParamsDp('switch_2'))
    }
    let tags: object | undefined = undefined
    if (isSupportFunctions('switch_2')) {
      tags = {
        [getGlobalParamsDp('switch_1')]:  I18n.getLang('switchmodule_switch1title'),
        [getGlobalParamsDp('switch_2')]: I18n.getLang('switchmodule_switch2title'),
      }
    }
    return createParams<SwitchHistoryPageRouteParams>({
      dpIds,
      tags,
      getActionsText: (dpData: any) => dpData.value === 'true' ? 'history_powerstrip_field1_text' : 'history_powerstrip_field1_text2',
    })
  }, [isSupportFunctions('switch_2')])

  advanceData.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return advanceData;
}
