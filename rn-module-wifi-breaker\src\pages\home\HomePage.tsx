import { useDeviceId, useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Utils } from 'tuya-panel-kit'
import { NativeApi, queryDpIds } from '@ledvance/base/src/api/native'
import { ScrollView, StyleSheet, Text, View } from "react-native"
import {
  useSwitch,
  useAdvanceData,
  useVoltage,
  useElectricCurrent,
  usePower,
  useSwitch2,
  useSwitchNames,
} from '../../features/FeatureHooks'
import { useReactive, useInterval } from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import I18n from '@ledvance/base/src/i18n'
import Card from '@ledvance/base/src/components/Card'
import AdvanceList from '@ledvance/base/src/components/AdvanceList'
import { getGlobalParamsDp, isSupportFunctions } from '@ledvance/base/src/utils/common'
import { useNavigation, useIsFocused } from '@react-navigation/core'
import { RouterKey } from 'navigation/Router'
import { EnergyConsumptionPageProps } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionPage'
import { cloneDeep } from 'lodash'
import SocketItem from '@ledvance/base/src/components/SocketItem'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const HomePage = (props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const navigation = useNavigation()
  const isFocused = useIsFocused()
  const devId = useDeviceId()
  const [switch1, setSwitch1] = useSwitch()
  const [switch2, setSwitch2] = useSwitch2()
  const power = usePower()
  const electricCurrent = useElectricCurrent()
  const voltage = useVoltage()
  const [switchNames, setSwitchNames] = useSwitchNames()
  const advanceData = useAdvanceData(switchNames)
  const state = useReactive({
    loading: false,
  })

  useInterval(() => {
    if (isFocused) {
      const jsonData = JSON.stringify([getGlobalParamsDp('cur_current'), getGlobalParamsDp('cur_power'), getGlobalParamsDp('cur_voltage')])
      queryDpIds(jsonData, devId).then()
    }
  },
    3000,
    { immediate: true }
  )

  const styles = StyleSheet.create({
    content: {
      flex: 1,
    },
    switchCard: {
      paddingVertical: cx(16),
      marginHorizontal: cx(24),
    },
    switchCardContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    switchCardTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    consumedEnergyCard: {
      marginHorizontal: cx(24),
    },
    consumedEnergyCardTitle: {
      marginHorizontal: cx(16),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    consumedEnergyContent: {
      flexDirection: 'row',
    },
    subContent: {
      flex: 1,
      alignItems: 'center',
      marginBottom: cx(9)
    },
    valueText: {
      fontSize: cx(24),
      fontWeight: 'bold',
      color: props.theme?.global.secondBrand,
    },
    titleText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme?.global.secondFontColor,
      textAlign: 'center',
    },
    unitText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme?.global.secondFontColor,
    },
  })

  const unitDivision = (str: string) => {
    if (!str) { return ['', ''] }
    const strIndex = str.indexOf('(') || str.indexOf('（')
    const unit = str.substring(strIndex)
    const name = str.split(unit)[0]
    return [name, unit]
  }

  const ConsumedEnergyItem = (props: { value: number, unit: string }) => {
    return (
      <View style={styles.subContent}>
        <Text style={styles.valueText}>{(props.value) || 0}</Text>
        <Spacer height={cx(4)} />
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[0]}
        </Text>
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[1]}
        </Text>
      </View>
    )
  }

  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}>
      <ScrollView nestedScrollEnabled={true}>
        <View style={styles.content}>
          <Spacer />
          <Card
            style={styles.consumedEnergyCard}
            onPress={() => navigation.navigate(RouterKey.ui_biz_energy_consumption, {
              electricDpCode: getGlobalParamsDp('cur_current'),
              powerDpCode: getGlobalParamsDp('cur_power'),
              voltageDpCode: getGlobalParamsDp('cur_voltage'),
              addEleDpCode: getGlobalParamsDp('add_ele'),
            } as EnergyConsumptionPageProps)}>
            <Spacer height={cx(16)} />
            <Text style={styles.consumedEnergyCardTitle}>{I18n.getLang('sockets_ce')}</Text>
            <Spacer height={cx(18)} />
            <View style={styles.consumedEnergyContent}>
              <ConsumedEnergyItem
                value={power}
                unit={I18n.getLang('consumption_data_field2_value_text1')} />
              <ConsumedEnergyItem
                value={electricCurrent}
                unit={I18n.getLang('consumption_data_field2_value_text2')} />
              <ConsumedEnergyItem
                value={voltage}
                unit={I18n.getLang('consumption_data_field2_value_text3')} />
            </View>
            <Spacer height={cx(17)} />
          </Card>
          <Spacer />
          <SocketItem
            title={I18n.getLang(isSupportFunctions('switch_2') ? 'switchmodule_switch1title' : 'Onoff_button_socket')}
            name={switchNames[0].secondaryTitle}
            icon={undefined}
            onNameChange={async newName => {
              state.loading = true
              const newSwitchNames = cloneDeep(switchNames)
              newSwitchNames[0].secondaryTitle = newName
              await setSwitchNames(newSwitchNames)
              state.loading = false
            }}
            enabled={switch1}
            onSwitchChange={async enable => {
              state.loading = true
              await setSwitch1(enable)
              state.loading = false
            }}
          />
          {isSupportFunctions('switch_2') && <>
            <Spacer />
            <SocketItem
              title={I18n.getLang('switchmodule_switch2title')}
              name={switchNames[1].secondaryTitle}
              icon={undefined}
              onNameChange={async newName => {
                state.loading = true
                const newSwitchNames = cloneDeep(switchNames)
                newSwitchNames[1].secondaryTitle = newName
                console.log('newSwitchNames22', newSwitchNames)
                const res = await setSwitchNames(newSwitchNames)
                console.log('res22', res)
                state.loading = false
              }}
              enabled={switch2}
              onSwitchChange={async enable => {
                state.loading = true
                await setSwitch2(enable)
                state.loading = false
              }}
            />
          </>}
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
