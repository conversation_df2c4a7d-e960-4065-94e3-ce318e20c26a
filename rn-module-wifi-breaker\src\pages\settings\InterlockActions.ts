import { useDp } from "@ledvance/base/src/models/modules/NativePropsSlice"
import { spliceByStep } from "@ledvance/base/src/utils/common"
import { useMemo } from "react"
import ThemeType from "@ledvance/base/src/config/themeType"
import { getGlobalParamsDp, isSupportFunctions } from "@ledvance/base/src/utils/common"

export interface InterlockPageParams {
    theme?: ThemeType,
    channelConfig: {
        channelTitle: string,
        channel: number,
    }[]
}

export interface InterlockItem {
    channels: number[]
}

export const useInterlock = () => {
    const [interlockHex, setInterlockHex] = useDp<string, any>(getGlobalParamsDp('switch_interlock'))
    console.log('interlockHex', interlockHex)
    const interlock = useMemo(() => {
        if (!interlockHex) return []
        const v = spliceByStep(interlockHex, 4).filter(item => item !== '0000').map(item => {
            const binary = parseInt(item, 16).toString(2).padStart(16, '0')
            const channels = binary.split('').reverse().reduce((acc, bit, index) => {
                if (bit === '1') acc.push(index)
                return acc
            }, [] as number[])
            return { channels }
        })
        return v
    }, [interlockHex])

    const setInterlock = (interlock: InterlockItem[]) => {
        const v = interlock.map(item => {
            // 创建一个16位的二进制字符串，初始全为0
            let binaryArray = new Array(16).fill('0');
            // 将channels中指定的位置设为1
            item.channels.forEach(channel => {
                binaryArray[channel] = '1';
            });
            // 将二进制字符串转换为16进制，并确保长度为4
            const binary = binaryArray.reverse().join('');
            return parseInt(binary, 2).toString(16).padStart(4, '0');
        }).join('');
        const hex = v.padEnd(16, '0');
        console.log('set interlockHex', hex)
        return setInterlockHex(hex);
    }
    return [interlock, setInterlock] as const
}

export const isSupportInterlock = () => {
    return  isSupportFunctions('switch_interlock', 'switch_1', 'switch_2')
}
