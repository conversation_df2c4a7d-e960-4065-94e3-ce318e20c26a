import {StyleSheet, Text} from "react-native";
import React from "react";
import Page from "@ledvance/base/src/components/Page";
import {useDeviceInfo} from "@ledvance/base/src/models/modules/NativePropsSlice";
import I18n from "@ledvance/base/src/i18n";
import {useReactive} from "ahooks";
import Card from "@ledvance/base/src/components/Card";
import {Utils} from "tuya-panel-kit";
import Spacer from "@ledvance/base/src/components/Spacer";
import ThemeType from "@ledvance/base/src/config/themeType";
import {useSwitchType} from "../../features/FeatureHooks";
import OptionGroup from "@ledvance/base/src/components/OptionGroup";
import LdvSwitch from "@ledvance/base/src/components/ldvSwitch";
import {isSupportInterlock, useInterlock} from "./InterlockActions";

const {convertX: cx} = Utils.RatioUtils;
const {withTheme} = Utils.ThemeUtils
const SettingsPage = (props: { theme?: ThemeType }) => {
  const deviceInfo = useDeviceInfo();
  const [switchType, setSwitchType] = useSwitchType()
  const [interlock, setInterlock] = useInterlock()
  const state = useReactive({
    loading: false,
  });

  const styles = StyleSheet.create({
    title: {
      marginHorizontal: cx(24),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
    },
    item: {
      marginHorizontal: cx(24),
    },
  })

  return (
    <Page
      backText={deviceInfo.name}
      headlineText={I18n.getLang('contact_sensor_specific_settings')}
      loading={state.loading}>
      <Text style={styles.title}>{I18n.getLang('switchmodule_typesetting')}</Text>
      <OptionGroup
        tips={I18n.getLang('switchmodule_typesettingdescription')}
        selected={switchType}
        options={[{
          title: I18n.getLang('switchstate1selection'),
          content: I18n.getLang('switchmodule_typesetting1'),
          value: 'flip',
          onPress: (value) => {
            setSwitchType(value).then()
          }
        }, {
          title: I18n.getLang('switchstate2selection'),
          content: I18n.getLang('switchmodule_typesetting2'),
          value: 'sync',
          onPress: (value) => {
            setSwitchType(value).then()
          }
        }, {
          title: I18n.getLang('switchstate3selection'),
          content: I18n.getLang('switchmodule_typesetting3'),
          value: 'button',
          onPress: (value) => {
            setSwitchType(value).then()
          }
        }]}
      />
      <Spacer/>
      {
        isSupportInterlock() ?
          <>
            <Card style={styles.item}>
              <LdvSwitch
                title={I18n.getLang('switch_interlock')}
                description={I18n.getLang('switch_interlockingdescription')}
                enable={interlock.length > 0}
                setEnable={async (v) => {
                  setInterlock([{channels: v ? [0, 1] : []}]).then()
                }}
              />
            </Card>
            <Spacer/>
          </>
          : <></>
      }
    </Page>
  )
}

export default withTheme(SettingsPage);
