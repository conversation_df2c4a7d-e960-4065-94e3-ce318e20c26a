import { NavigationRoute } from 'tuya-panel-kit'
import HomePage from 'pages/home/<USER>'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'
import TimeSchedulePageRouters from '@ledvance/ui-biz-bundle/src/newModules/timeSchedule/Router'
import TimerPageRouters from '@ledvance/ui-biz-bundle/src/modules/timer/Router'
import FixedTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/fixedTime/Router'
import RandomTimePageRouters from '@ledvance/ui-biz-bundle/src/newModules/randomTime/Router'
import { PlugPowerOnBehaviorPageRouters } from '@ledvance/ui-biz-bundle/src/newModules/powerOnBehavior/Router'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import EnergyConsumptionPageRouters from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/Router'
import OverchargeSwitchPageRouters from '@ledvance/ui-biz-bundle/src/newModules/overchargeSwitch/Router'

export const RouterKey = {
  main: 'main',
  ...ui_biz_routerKey

}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options:{
      hideTopbar: true,
      showOfflineView: false,
    }
  },
  ...EnergyConsumptionPageRouters,
  ...TimeSchedulePageRouters,
  ...TimerPageRouters,
  ...FixedTimePageRouters,
  ...RandomTimePageRouters,
  ...HistoryPageRouters,
  ...PlugPowerOnBehaviorPageRouters,
  ...OverchargeSwitchPageRouters
]

export default RouterKey