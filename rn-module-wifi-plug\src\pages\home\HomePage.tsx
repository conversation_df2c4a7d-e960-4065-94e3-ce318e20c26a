import React, {useEffect} from 'react'
import { ScrollView, View, Text, StyleSheet } from 'react-native'
import { useNavigation, useIsFocused } from '@react-navigation/core'
import Page from '@ledvance/base/src/components/Page'
import { useDeviceId, useDeviceInfo, useSolarPlug, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import res from '@ledvance/base/src/res'
import { NativeApi, queryDpIds } from '@ledvance/base/src/api/native'
import { Utils } from 'tuya-panel-kit'
import Card from '@ledvance/base/src/components/Card'
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import I18n from '@ledvance/base/src/i18n'
import { useInterval, useReactive } from 'ahooks'
import Spacer from '@ledvance/base/src/components/Spacer'
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import routerKey from 'navigation/Router'
import { useSwitch, useElectricCurrent, usePower, useVoltage, useAdvancedData } from 'hooks/FeatureHooks'
import { EnergyConsumptionPageProps } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionPage'
import { getGlobalParamsDp } from '@ledvance/base/src/utils/common'
import { getEnergyGenerationValue } from '@ledvance/ui-biz-bundle/src/newModules/energyConsumption/EnergyConsumptionActions'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const HomePage = (props: { theme?: any }) => {
  const devInfo = useDeviceInfo()
  const devId = useDeviceId()
  const familyName = useFamilyName()
  const navigation = useNavigation()
  const isFocused = useIsFocused()
  const [switchLed, setSwitchLed] = useSwitch()
  const power = usePower()
  const electricCurrent = useElectricCurrent()
  const voltage = useVoltage()
  const advanceData = useAdvancedData()
  const isSolarPlug = useSolarPlug()
  const state = useReactive({
    loading: false,
    flag: Symbol(),
    isGeneration: false
  })

  useInterval(() => {
      if (isFocused) {
        const jsonData = JSON.stringify([getGlobalParamsDp('cur_current'), getGlobalParamsDp('cur_power'), getGlobalParamsDp('cur_voltage')])
        queryDpIds(jsonData, devId).then()
      }
    },
    3000,
    {immediate: true}
  )

  useEffect(() => {
    getEnergyGenerationValue(devId).then(data => {
      state.isGeneration = isSolarPlug !== (data?.generationMode || false)
    })
  }, [isFocused, isSolarPlug])

  const unitDivision = (str: string) => {
    if (!str) { return ['', ''] }
    const strIndex = str.indexOf('(') || str.indexOf('（')
    const unit = str.substring(strIndex)
    const name = str.split(unit)[0]
    return [name, unit]
  }

  const styles = StyleSheet.create({
    content: {
      flex: 1,
    },
    consumedEnergyCard: {
      marginHorizontal: cx(24),
    },
    consumedEnergyCardTitle: {
      marginHorizontal: cx(16),
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    consumedEnergyContent: {
      flexDirection: 'row',
    },
    consumedEnergyItem: {
      flex: 1,
      alignItems: 'center',
    },
    consumedEnergyItemValue: {
      color: props.theme.global.secondBrand,
      fontSize: cx(24),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    consumedEnergyItemUnit: {
      color: props.theme.global.secondFontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
      textAlign: 'center'
    },
    switchCard: {
      paddingVertical: cx(16),
      marginHorizontal: cx(24),
    },
    switchCardContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    switchCardTitle: {
      color: props.theme.global.fontColor,
      fontSize: cx(16),
      // fontFamily: 'helvetica_neue_lt_std_bd',
      fontWeight: 'bold',
    },
    advancedList: {
      marginHorizontal: cx(14),
    },
    advancedCard: {
      marginHorizontal: cx(10),
      marginTop: cx(1),
      marginBottom: cx(19),
    },
    advancedCardContainerStyle: {
      height: cx(118),
      justifyContent: 'center',
      alignItems: 'center',
    },
    advancedText: {
      color: props.theme.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_roman',
      textAlign: 'center'
    },
    tipText: {
      fontSize: cx(10),
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
    subContent: {
      flex: 1,
      alignItems: 'center',
      marginBottom: cx(9)
    },
    valueText: {
      fontSize: cx(24),
      fontWeight: 'bold',
      color: props.theme.global.secondBrand,
    },
    titleText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme.global.secondFontColor,
      textAlign: 'center',
    },
    unitText: {
      fontFamily: 'helvetica_neue_lt_std_roman',
      fontSize: cx(14),
      color: props.theme.global.secondFontColor,
    },
  })

  const ConsumedEnergyItem = (props: { value: number, unit: string }) => {
    return (
      <View style={styles.subContent}>
        <Text style={styles.valueText}>{(props.value) || 0}</Text>
        <Spacer height={cx(4)} />
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[0]}
        </Text>
        <Text style={styles.titleText}>
          {unitDivision(props.unit)[1]}
        </Text>
      </View>
    )
  }

  return (
    <Page
      backText={familyName}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onBackClick={() => NativeApi.back()}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
      loading={state.loading}>
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          <Card
            style={styles.consumedEnergyCard}
            onPress={() => navigation.navigate(routerKey.ui_biz_energy_consumption, {
              electricDpCode: getGlobalParamsDp('cur_current'),
              powerDpCode: getGlobalParamsDp('cur_power'),
              voltageDpCode: getGlobalParamsDp('cur_voltage'),
              addEleDpCode: getGlobalParamsDp('add_ele'),
            } as EnergyConsumptionPageProps)}>
            <Spacer height={cx(16)} />
            <Text style={styles.consumedEnergyCardTitle}>{I18n.getLang(state.isGeneration ? 'sockets_headline_power' : 'sockets_ce')}</Text>
            <Spacer height={cx(18)} />
            <View style={styles.consumedEnergyContent}>
              <ConsumedEnergyItem
                value={power}
                unit={I18n.getLang('consumption_data_field2_value_text1')} />
              <ConsumedEnergyItem
                value={electricCurrent}
                unit={I18n.getLang('consumption_data_field2_value_text2')} />
              <ConsumedEnergyItem
                value={voltage}
                unit={I18n.getLang('consumption_data_field2_value_text3')} />
            </View>
            <Spacer height={cx(17)} />
          </Card>
          <Spacer />
          <Card style={{ marginHorizontal: cx(24) }}>
            <View>
              <LdvSwitch
                title={I18n.getLang('Onoff_button_socket')}
                color={props.theme.card.background}
                colorAlpha={1}
                enable={switchLed}
                setEnable={async (enable: boolean) => {
                  state.loading = true
                  await setSwitchLed(enable)
                  state.loading = false
                }} />
            </View>
          </Card>
          <Spacer />
          <AdvanceList advanceData={advanceData} />
        </View>
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
