import Card from '@ledvance/base/src/components/Card';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { IconFont, Utils } from 'tuya-panel-kit';
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils;
const { withTheme } = Utils.ThemeUtils

interface CardItemProps {
  theme?: ThemeType
  title: string;
  content: string;
  onPress: () => void;
}

const CardItem = (props: CardItemProps) => {

  const styles = StyleSheet.create({
    itemTitle: {
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontWeight: 'bold',
      paddingVertical: cx(16),
      maxWidth: cx(220),
    },
    itemContent: {
      color: props.theme?.global.secondFontColor,
      maxWidth: cx(100),
      fontSize: cx(14),
    },
    item: {
      marginHorizontal: cx(24),
    },
    itemContainerStyle: {
      paddingHorizontal: cx(10),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <Card style={styles.item} containerStyle={styles.itemContainerStyle} onPress={props.onPress}>
      <Text style={styles.itemTitle}>{props.title}</Text>
      <View style={{ flex: 1 }} />
      <Text style={styles.itemContent} numberOfLines={1}>
        {props.content}
      </Text>
      <IconFont name={'arrow'} color={props.theme?.global.secondFontColor} size={cx(11)} />
    </Card>
  );
};

export default withTheme(CardItem)
