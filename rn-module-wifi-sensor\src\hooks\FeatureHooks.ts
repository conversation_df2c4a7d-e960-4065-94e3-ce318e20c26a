import I18n, { lang } from "@ledvance/base/src/i18n";
import { useDeviceId, useDeviceInfo, useDp } from "@ledvance/base/src/models/modules/NativePropsSlice";
import { Result } from "@ledvance/base/src/models/modules/Result";
import { getGlobalParamsDp, isSupportFunctions } from "@ledvance/base/src/utils/common";
import { useReactive, useUpdateEffect } from "ahooks";
import { AdvancedData } from '@ledvance/base/src/components/AdvanceCard';
import { RouterKey } from '../navigation/Router'
import { useEffect, useState } from "react";
import { commonApi } from '@tuya/tuya-panel-api';
import res from "@ledvance/base/src/res";
import { createParams } from "@ledvance/base/src/hooks/Hooks";
import { SwitchHistoryPageRouteParams } from "@ledvance/ui-biz-bundle/src/modules/history/HistoryPage";
import { TYSdk } from "tuya-panel-kit";
import { PIRSensitivity } from "pages/setting/SettingPage";

export type CheckingResult = 'checking' | 'check_success' | 'check_failure'
export type SensorState = 'alarm' | 'normal'

export function useBatteryPercentage(): number {
  return useDp<number, any>(getGlobalParamsDp('battery_percentage'))[0] || 0;
}

export function usePirSensitivity(): [string, (value: string) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('pir_sensitivity'));
}

export function usePirDelay(): [number, (value: number) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('pir_delay'));
}

export function useCheckingResult() {
  return useDp<CheckingResult, any>(getGlobalParamsDp('checking_result'))[0]
}

export function useMuffling(): [boolean, (v: boolean) => Promise<Result<any>>] {
  return useDp(getGlobalParamsDp('muffling'))
}

export function useCoValue() {
  return useDp<number, any>(getGlobalParamsDp('co_value'))[0]
}

export function useDoorContactState() {
  return useDp<boolean, any>(getGlobalParamsDp('doorcontact_state'))[0];
}

export function usePirState() {
  return useDp<'pir' | 'none', any>(getGlobalParamsDp('pir_state'))[0]
}

export function useCoState() {
  return useDp<SensorState, any>(getGlobalParamsDp('co_state'))[0]
}

export function useSmokeSensorState() {
  return useDp<SensorState, any>(getGlobalParamsDp('smoke_sensor_state'))[0]
}

export function useWaterSensorState() {
  return useDp<SensorState, any>(getGlobalParamsDp('watersensor_state'))[0]
}

export function isSupportPirSensitivity(): boolean {
  return isSupportFunctions('pir_sensitivity')
}

export function isSupportPirDelay(): boolean {
  return isSupportFunctions('pir_delay')
}

export function isSupportMuffling(): boolean {
  return isSupportFunctions('muffling')
}

// 门磁
export function isSupportDoorContactState(): boolean {
  return isSupportFunctions('doorcontact_state')
}
// 人体传感器
export function isSupportPirState(): boolean {
  return isSupportFunctions('pir_state')
}
// 水浸传感器
export function isSupportWaterSensorState(): boolean {
  return isSupportFunctions('watersensor_state')
}
// 烟雾报警器
export function isSupportSmokeSensorState(): boolean {
  return isSupportFunctions('smoke_sensor_state')
}
// CO报警器
export function isSupportCoState(): boolean {
  return isSupportFunctions('co_state')
}

export function isSupportCheckResult(): boolean {
  return isSupportFunctions('checking_result')
}

const getSensorContent = (pirState: 'pir' | 'none', contactState: boolean, coState: SensorState, smokeState: SensorState, waterState: SensorState, ppm?: string) => {
  const getTitleAndImage = (
    isSupported: boolean,
    title: string,
    image: string | number,
    text: string,
    ppm?: string
  ) => {
    return isSupported ? { title, image, text, ppm } : null;
  };

  let sensorContent = getTitleAndImage(
    isSupportPirState(),
    I18n.getLang('motion_detector_tile_headline'),
    pirState === 'pir' ? res.rn_image_motion_detected : res.rn_image_no_motion_detected,
    I18n.getLang(pirState === 'pir' ? 'motion_detector_tile_status_1' : 'motion_detector_tile_status_2')
  );

  if (!sensorContent) {
    sensorContent = getTitleAndImage(
      isSupportDoorContactState(),
      I18n.getLang('contact_sensor_tile_headline'),
      contactState ? res.rn_image_contact_sensor_open : res.rn_image_contact_sensor_close,
      I18n.getLang(contactState ? 'contact_sensor_tile_status_1' : 'contact_sensor_tile_status_2')
    );
  }

  if (!sensorContent) {
    sensorContent = getTitleAndImage(
      isSupportCoState(),
      I18n.getLang('title_COsensor'),
      coState === 'alarm' ? res.ic_co_alert_effect : res.ic_co_alert_free,
      coState === 'alarm' ? I18n.getLang('CO_alerteffect') : I18n.getLang('CO_alertfree'),
      ppm
    );
  }

  if (!sensorContent) {
    sensorContent = getTitleAndImage(
      isSupportSmokeSensorState(),
      I18n.getLang('title_smokesensor'),
      smokeState === 'alarm' ? res.ic_smoke_alert_effect : res.ic_smoke_alert_free,
      smokeState === 'alarm' ? I18n.getLang('smoke_alerteffect') : I18n.getLang('smoke_alertfree')
    );
  }

  if (!sensorContent) {
    sensorContent = getTitleAndImage(
      isSupportWaterSensorState(),
      I18n.getLang('title_watersensor'),
      waterState === 'alarm' ? res.ic_water_alert_effect : res.ic_water_alert_free,
      waterState === 'alarm' ? I18n.getLang('water_alerteffect') : I18n.getLang('waterleakage_alertfree')
    );
  }

  return sensorContent || {
    title: I18n.getLang('motion_detector_tile_headline'),
    image: res.rn_image_no_motion_detected,
    text: I18n.getLang('motion_detector_tile_status_2')
  };
}

interface SensorContent {
  title: string;
  image: string | number;
  text: string;
  ppm?: string;
}

export function useSensorContent(): SensorContent {
  const pirState = usePirState()
  const contactState = useDoorContactState()
  const coState = useCoState()
  const smokeState = useSmokeSensorState()
  const waterState = useWaterSensorState()
  const coValue = useCoValue()
  const [sensorContent, setSensorContent] = useState(getSensorContent(pirState, contactState, coState, smokeState, waterState, coValue + ' ppm'))

  useUpdateEffect(() => {
    const content = getSensorContent(pirState, contactState, coState, smokeState, waterState, coValue + ' ppm')
    setSensorContent(content)
  }, [pirState, contactState, coState, smokeState, waterState, coValue])

  return sensorContent
}

interface AlarmData {
  id: string
  title: string
  enable: boolean
}

const alarmMap = {
  "自检": { name: I18n.getLang('smoke_equipmentselftest'), order: 1 },
  "设备自检": { name: I18n.getLang('smoke_equipmentselftest'), order: 1 },
  "CO报警": { name: I18n.getLang('settings_coalarm'), order: 2 },
  "CO报警恢复": { name: I18n.getLang('settings_coalarmrecovery'), order: 3 },
  "漏水报警": { name: I18n.getLang('settings_leakagealarm'), order: 4 },
  "漏水报警恢复": { name: I18n.getLang('settings_wateralarmrecovery'), order: 5 },
  "烟雾报警": { name: I18n.getLang('settings_smokealarm'), order: 6 },
  "烟雾报警恢复": { name: I18n.getLang('settings_smokealarmrecovery'), order: 7 },
  "关门提醒：": { name: I18n.getLang('push_notifications_contact_sensor_door_closing_title'), order: 8 },
  "开门报警": { name: I18n.getLang('device_menu_contact_sensors_secondbox_text2'), order: 9 },
  "有人报警": { name: I18n.getLang('device_menu_motion_sensors_secondbox_text2'), order: 10 },
  "无人": { name: I18n.getLang('device_menu_motion_sensors_secondbox_text3'), order: 11 },
  "低电量提醒": { name: I18n.getLang('device_menu_contact_sensors_secondbox_text1'), order: 12 },
}

export function useSensorAlarm(): [AlarmData[], (alarmData: AlarmData[]) => Promise<boolean>, boolean] {
  const devId = useDeviceId()
  const [alarmData, setAlarmData] = useState<AlarmData[]>([])
  const [onLoading, setOnLoading] = useState(true)
  useEffect(() => {
    commonApi.alarmApi.getDevAlarmList(devId).then(res => {
      setOnLoading(false)
      if (res && res.length) {
        const data = res.map(alarm => {
          return {
            id: alarm.id,
            title: alarmMap[alarm.name]?.name || alarm.i18nData.name[lang] || alarm.i18nData.name.en,
            enable: alarm.enabled,
            order: alarmMap[alarm.name]?.order || 99
          }
        })
        data.sort((a, b) => a.order - b.order)
        setAlarmData(data)
      }
    })
  }, [])

  const setAlarmSwitch = async (alarmData: AlarmData[]) => {
    // 判断是否全部打开
    const isAllOpen = alarmData.every(alarm => alarm.enable)
    const closeIds = alarmData.reduce((pre, cur) => {
      if (!cur.enable) {
        pre.push(cur.id)
      }
      return pre
    }, [] as string[]).join(',')
    return commonApi.alarmApi.setAlarmSwitch({
      devId,
      // @ts-ignore
      disabled: !isAllOpen,
      ruleIds: isAllOpen ? '' : closeIds
    })
  }
  return [alarmData, setAlarmSwitch, onLoading]
}

interface DPS {
  dps: string,
  pushStatus: boolean
}

interface CacheDPInfo {
  pirDelay?: number,
  pirSensitivity?: PIRSensitivity
  muffling?: boolean
  isLowPowDevice: boolean
}

export function useCacheDpData(): [CacheDPInfo, (cacheData: any) => Promise<Result<any>>, boolean] {
  const devInfo = useDeviceInfo()
  const cacheDPInfo = useReactive<CacheDPInfo>({
    pirDelay: undefined,
    pirSensitivity: undefined,
    muffling: undefined,
    isLowPowDevice: false
  });
  const [onLoading, setOnLoading] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const isLowPowDevice = await TYSdk.apiRequest<boolean>("tuya.p.support.dp.cache.get", { productId: devInfo?.productId });
        cacheDPInfo.isLowPowDevice = isLowPowDevice
        if (isLowPowDevice) {
          const result = await TYSdk.apiRequest<Map<number, DPS>>("tuya.m.device.cache.dp.get", { devId: devInfo?.devId });
          result.forEach((value) => {
            const dps = JSON.parse(value.dps)
            if (dps.pir_delay !== undefined) {
              cacheDPInfo.pirDelay = dps.pir_delay
            }
            if (dps.pir_sensitivity !== undefined) {
              cacheDPInfo.pirSensitivity = dps.pir_sensitivity
            }
            if (dps.muffling !== undefined) {
              cacheDPInfo.muffling = dps.muffling
            }
          });
        }
      } catch (error) {
        console.log("useCacheDPInfo", error)
      }
      setOnLoading(false);
    };
    if (devInfo?.devId) {
      fetchData().then();
    }
  }, [devInfo?.devId]);

  const setCacheDpFn = async (cacheData: any) =>{
    const res = await TYSdk.apiRequest<Boolean>('tuya.m.device.cache.dp.add', { devId: devInfo.devId, time: Date.now(), dps: JSON.stringify(cacheData)})
    return {
      success: !!res
    }
  }
  return [cacheDPInfo, setCacheDpFn, onLoading]
}

const getSensorActions = (dpData: any) => {
  console.log(dpData.dpId, getGlobalParamsDp('smoke_sensor_state'), dpData.dpId === getGlobalParamsDp('smoke_sensor_state'), '< --- dpppppp')
  if (isSupportPirState()) {
    return dpData.value === 'pir' ? 'motion_detector_tile_status_1' : 'motion_detector_tile_status_2'
  }
  if (isSupportSmokeSensorState()) {
    return dpData.dpId === Number(getGlobalParamsDp('smoke_sensor_state')) ? (dpData.value === 'alarm' ? 'smoke_alerteffect' : 'smoke_alertfree') : (dpData.value === 'check_success' ? 'sensor_deviceselftest' : 'sensor_deviceselftestfail')
  }
  if (isSupportWaterSensorState()) {
    return dpData.value === 'alarm' ? 'water_alerteffect' : 'waterleakage_alertfree'
  }
  if (isSupportCoState()) {
    return dpData.dpId === Number(getGlobalParamsDp('co_state')) ? (dpData.value === 'alarm' ? 'CO_alerteffect' : 'CO_alertfree') : (dpData.value === 'check_success' ? 'sensor_deviceselftest' : 'sensor_deviceselftestfail')
  }
  return dpData.value === 'true' ? 'history_contact_sensor_field2_text2' : 'history_contact_sensor_field2_text'
}


export function useAdvancedData(): AdvancedData[] {
  const res: AdvancedData[] = []

  res.push({
    title: I18n.getLang('add_matter_to_fabric_box4_text2'),
    dp: { key: '', code: 'routines' },
    router: {
      key: RouterKey.routines,
    },
  })

  res.push({
    title: I18n.getLang('contact_sensor_specific_settings'),
    dp: { key: '', code: 'setting' },
    router: {
      key: RouterKey.settings,
    },
  })

  const historyParams = createParams<SwitchHistoryPageRouteParams>({
    dpIds: [getGlobalParamsDp('smoke_sensor_state'), getGlobalParamsDp('checking_result'), getGlobalParamsDp('doorcontact_state'), getGlobalParamsDp('pir_state'), getGlobalParamsDp('co_state'), getGlobalParamsDp('watersensor_state')],
    headlineText: I18n.getLang('history_contact_sensor_headline_text'),
    getActionsText: getSensorActions,
    showLimit: isSupportDoorContactState()
  })

  res.push({
    title: I18n.getLang('history_socket_headline_text'),
    dp: { key: '', code: 'history' },
    router: {
      key: RouterKey.ui_biz_history,
      params: historyParams
    },
  })

  return res
}
