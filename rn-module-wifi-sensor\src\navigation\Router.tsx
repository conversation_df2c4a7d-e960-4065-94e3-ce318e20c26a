import { NavigationRoute } from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import HistoryPageRouters from '@ledvance/ui-biz-bundle/src/modules/history/Router'
import SettingPage from '../pages/setting/SettingPage'
import { ui_biz_routerKey } from '@ledvance/ui-biz-bundle/src/navigation/Routers'

export const RouterKey = {
  main: 'main',
  routines: 'routines',
  settings: 'Settings',
  ...ui_biz_routerKey
}

export const AppRouters: NavigationRoute[] = [
  {
    name: RouterKey.main,
    component: HomePage,
    options: {
      hideTopbar: true,
      showOfflineView: false,
    },
  },
  {
    name: RouterKey.settings,
    component: SettingPage,
    options: {
      gesture: true,
      hideTopbar: true,
    },
  },
  ...HistoryPageRouters
]