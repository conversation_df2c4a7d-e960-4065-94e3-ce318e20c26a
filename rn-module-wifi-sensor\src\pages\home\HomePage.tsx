import {Image, ScrollView, StyleSheet, Text, View} from 'react-native'
import React, {useEffect} from 'react'
import {useDeviceInfo, useFamilyName} from '@ledvance/base/src/models/modules/NativePropsSlice'
import {Utils} from 'tuya-panel-kit'
import Spacer from '@ledvance/base/src/components/Spacer'
import res from '@ledvance/base/src/res'
import {
  isSupportMuffling,
  useAdvancedData,
  useBatteryPercentage,
  useCacheDpData,
  useMuffling,
  useSensorContent,
  useSmokeSensorState
} from 'hooks/FeatureHooks'
import Page from '@ledvance/base/src/components/Page'
import {NativeApi} from '@ledvance/base/src/api/native'
import Card from '@ledvance/base/src/components/Card'
import AdvanceList from '@ledvance/base/src/components/AdvanceList';
import {RouterKey} from 'navigation/Router'
import {showDialog} from '@ledvance/base/src/utils/common'
import I18n from '@ledvance/base/src/i18n'
import ThemeType from '@ledvance/base/src/config/themeType'
import BatteryPercentageView from '@ledvance/base/src/components/BatteryPercentageView'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

export default withTheme(function HomePage(props: { theme?: ThemeType}) {
  const deviceInfo = useDeviceInfo()
  const advancedData = useAdvancedData()
  const batteryPercentage = useBatteryPercentage()
  const content = useSensorContent()
  const [cacheDPInfo, setCacheDPInfo] = useCacheDpData()
  const smokeState = useSmokeSensorState()
  const [muffling, setMuffling] = useMuffling()

  useEffect(() => {
    if (isSupportMuffling()) {
      const mufflingState = cacheDPInfo.muffling ?? muffling
      if (mufflingState && smokeState === 'alarm') {
        showDialog({
          method: 'alert',
          title: I18n.getLang('smoke_alerteffect'),
          confirmText: I18n.getLang('mute_smokealarm'),
          onConfirm: async (_, { close }) => {
            close()
            if (cacheDPInfo.isLowPowDevice){
              setCacheDPInfo({muffling: false})
            }else{
              await setMuffling(false)
            }
          }
        })
      }
    }
  }, [smokeState, JSON.stringify(cacheDPInfo), muffling])

  const styles = StyleSheet.create({
    contactCard: {
      marginHorizontal: cx(24)
    },
    contentText: {
      marginTop: cx(24),
      alignSelf: 'center',
      color: props.theme?.global.fontColor,
      fontSize: cx(14),
      fontFamily: 'helvetica_neue_lt_std_bd',
    }
  })

  return (
    <Page
      backText={useFamilyName()}
      onBackClick={NativeApi.back}
      headlineText={deviceInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(deviceInfo.devId)
      }}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View style={{ flex: 1 }}>
          <Spacer height={cx(4)} />
          <BatteryPercentageView value={batteryPercentage} />
          <Spacer />
          <Card style={styles.contactCard}>
            <View
              style={{
                flexDirection: 'row',
                marginTop: cx(16),
                marginHorizontal: cx(16),
                alignItems: 'center',
              }}>
              <Text
                style={{
                  color: props.theme?.global.fontColor,
                  fontSize: cx(16),
                  fontFamily: 'helvetica_neue_lt_std_bd',
                }}>
                {content.title}
              </Text>
            </View>
            <Image
              style={{
                width: cx(150),
                height: cx(150),
                marginTop: cx(24),
                alignSelf: 'center',
              }}
              source={typeof content.image === 'number' ? content.image : { uri: content.image }} />
            {content?.ppm ? <Text style={[styles.contentText, { fontSize: cx(12) }]}>{content.ppm}</Text> : null}
            <Text style={[styles.contentText, { marginTop: content?.ppm ? cx(5) : cx(24) }]}>
              {content.text}
            </Text>
            <Spacer height={cx(24)} />
          </Card>
          <Spacer height={cx(16)} />
          <AdvanceList
            advanceData={advancedData}
            onAdvanceItemClick={async (advance) => {
              if (advance.router.key === RouterKey.routines) {
                NativeApi.toRoutinesPage({
                  backTitle: deviceInfo.name,
                  deviceId: deviceInfo.devId
                })
              }
            }}
          />
        </View>
      </ScrollView >
    </Page >
  )
})
