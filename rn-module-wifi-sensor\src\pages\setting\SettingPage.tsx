import React, { useMemo } from "react";
import { ScrollView, View } from 'react-native';
import Page from "@ledvance/base/src/components/Page";
import { useDeviceInfo } from "@ledvance/base/src/models/modules/NativePropsSlice";
import LdvSwitch from '@ledvance/base/src/components/ldvSwitch'
import Card from "@ledvance/base/src/components/Card";
import { Utils, Modal, ListDate, PickerDataProps } from 'tuya-panel-kit';
import Spacer from "@ledvance/base/src/components/Spacer";
import { useSensorAlarm, usePirSensitivity, usePirDelay, useCacheDpData } from '../../hooks/FeatureHooks';
import { useReactive, useUpdateEffect } from "ahooks";
import { cloneDeep } from "lodash";
import I18n from "@ledvance/base/src/i18n";
import CardItem from "components/CardItem";
import ThemeType from '@ledvance/base/src/config/themeType'

const { convertX: cx } = Utils.RatioUtils
const { withTheme } = Utils.ThemeUtils

const Setting = (props: { theme?: ThemeType}) => {
  const devInfo = useDeviceInfo()
  const [alarmData, setAlarmSwitch, loadingAlarmData] = useSensorAlarm()
  const [pirSensitivity, setPIRSensitivity] = usePirSensitivity();
  const [pirDelay, setPIRDelay] = usePirDelay();
  const [cacheDPInfo, setCacheDPInfo, loadingCacheDPInfo] = useCacheDpData();
  const state = useReactive({
    loading: loadingAlarmData || loadingCacheDPInfo,
    loadingCacheDPInfo,
    alarmData: cloneDeep(alarmData),
    cacheDPInfo: cloneDeep(cacheDPInfo),
    showPIRSensitivityModal: false,
    showPIRDelayModal: false,
    pirSensitivity,
    pirDelay
  });

  useUpdateEffect(() => {
    state.alarmData = cloneDeep(alarmData);
    state.loading = loadingAlarmData || loadingCacheDPInfo;
    state.loadingCacheDPInfo = loadingCacheDPInfo
  }, [alarmData, loadingAlarmData,loadingCacheDPInfo])

  useUpdateEffect(() =>{
    state.cacheDPInfo = cacheDPInfo
  }, [JSON.stringify(cacheDPInfo)])

  useUpdateEffect(() =>{
    state.pirSensitivity = state.cacheDPInfo.pirSensitivity ?? pirSensitivity
    state.pirDelay = state.cacheDPInfo.pirDelay ?? pirDelay
  }, [pirSensitivity, pirDelay, JSON.stringify(state.cacheDPInfo)])

  return (
    <Page
      backText={devInfo.name}
      headlineText={I18n.getLang('contact_sensor_specific_settings')}
      loading={state.loading}
    >
      <ScrollView nestedScrollEnabled={true}>
        <View>
          <Spacer />
          {state.alarmData.map((alarm, idx) => (
            <View key={alarm.id}>
              <Card style={{ marginHorizontal: cx(24) }}>
                <LdvSwitch
                  title={alarm.title}
                  enable={alarm.enable}
                  setEnable={async (v: boolean) => {
                    state.loading = true
                    const cloneAlarm = cloneDeep(state.alarmData)
                    cloneAlarm[idx].enable = v
                    const res = await setAlarmSwitch(cloneAlarm)
                    if (res) state.alarmData = cloneAlarm
                    state.loading = false
                  }}
                  color={props.theme?.card.background}
                  colorAlpha={1}
                />
              </Card>
              <Spacer />
            </View>
          ))}
          {!state.loadingCacheDPInfo &&!!state.pirSensitivity && (
            <>
              <CardItem
                title={I18n.getLang('ldv_pir_sensitivity')}
                content={getPIRSensitivityDisplayName(state.pirSensitivity)}
                onPress={() => {
                  state.showPIRSensitivityModal = true;
                }}
              />
              <Spacer />
            </>
          )}
          {!state.loadingCacheDPInfo &&!!state.pirDelay && (
            <>
              <CardItem
                title={I18n.getLang('ldv_pir_delay')}
                content={`${state.pirDelay} ${I18n.getLang('ldv_second')}`}
                onPress={() => {
                  state.showPIRDelayModal = true;
                }}
              />
              <Spacer />
            </>
          )}
        </View>
      </ScrollView>
      <Modal.List
        title={I18n.getLang('ldv_pir_sensitivity')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        visible={state.showPIRSensitivityModal}
        dataSource={getPIRSensitivityItemList()}
        type="radio"
        value={state.pirSensitivity}
        onMaskPress={() => {
          state.showPIRSensitivityModal = false;
        }}
        onCancel={() => {
          state.showPIRSensitivityModal = false;
        }}
        onConfirm={async value => {
          state.showPIRSensitivityModal = false;
          state.loading = true
          if(cacheDPInfo.isLowPowDevice){
            await setCacheDPInfo({pir_sensitivity: value})
          }else{
            await setPIRSensitivity(value)
          }
          state.loading = false
          state.cacheDPInfo.pirSensitivity = value
        }}
        motionConfig={{hideDuration: 100}}
      />

      <Modal.Picker
        title={I18n.getLang('ldv_pir_delay')}
        cancelText={I18n.getLang('bt_shs_google_button_cancel_enabling')}
        confirmText={I18n.getLang('auto_scan_system_wifi_confirm')}
        visible={state.showPIRDelayModal}
        dataSource={getPIRDelayItemList()}
        value={`${state.pirDelay}`}
        onMaskPress={() => {
          state.showPIRDelayModal = false;
        }}
        label={I18n.getLang('ldv_second')}
        labelOffset={cx(30)}
        onConfirm={async value => {
          state.showPIRDelayModal = false;
          const delay = parseInt(value);
          state.loading = true
          if (cacheDPInfo.isLowPowDevice){
            await setCacheDPInfo({pir_delay: delay})
          }else{
            await setPIRDelay(delay)
          }
          state.loading = false
          state.cacheDPInfo.pirDelay = delay
        }}
        onCancel={() => {
          state.showPIRDelayModal = false;
        }}
        motionConfig={{hideDuration: 100}}
      />
    </Page>
  );
};

export enum PIRSensitivity {
  Low = 'low',
  Middle = 'middle',
  High = 'high',
}

const getPIRSensitivityDisplayName = (value: string): string => {
  switch (value) {
    case PIRSensitivity.High:
      return I18n.getLang('contact_sensor_battery_state1');
    case PIRSensitivity.Middle:
      return I18n.getLang('contact_sensor_battery_state2');
    case PIRSensitivity.Low:
    default:
      return I18n.getLang('contact_sensor_battery_state3');
  }
};

const getPIRSensitivityItemList = (): ListDate[] => {
  return useMemo(() => {
    return Object.entries(PIRSensitivity).map(([key, value]) => {
      return { title: getPIRSensitivityDisplayName(value), value: value, key: key };
    });
  }, []);
};

const getPIRDelayItemList = (): PickerDataProps[] => {
  return useMemo(() => {
    return Array.from({ length: 120 - 30 + 1 }, (_, i) => i + 30).map(value => {
      return { label: `${value}`, value: `${value}` };
    });
  }, []);
};

export default withTheme(Setting)
