import React, {useCallback, useEffect, useState} from 'react';
import Card from '@ledvance/base/src/components/Card';
import {Utils} from 'tuya-panel-kit';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import Spacer from '@ledvance/base/src/components/Spacer';
import ThemeType from '@ledvance/base/src/config/themeType';
import {useNavigation} from '@react-navigation/core';
import {NativeApi} from "@ledvance/base/src/api/native";
import I18n from "@ledvance/base/src/i18n/index";
import { RoutineParam } from '@ledvance/base/src/utils/interface';

const cx = Utils.RatioUtils.convertX;
const {withTheme} = Utils.ThemeUtils;

type PressActionProps = {
  channel: 1 | 2 | 4
  backTitle: string
  deviceId: string
  dpIds: string[]
  dpIndex: number
  routerKey?: string
  theme?: ThemeType
};

enum PressType {
  Single = 'single_click',
  Double = 'double_click',
  Long = 'long_press'
}

interface ActionData {
  title: string
  onPress: () => void
}

const PressActionView = (props: PressActionProps) => {
  const navigation = useNavigation()
  const [actionData, setActionData] = useState<ActionData[]>([])

  const toSettingPage = useCallback((dpIndex: number) => {
    navigation.navigate(props.routerKey ?? 'setting', {
      dpIds: props.dpIds,
      dpIndex: dpIndex
    })
  }, [props.routerKey, props.backTitle, props.deviceId, props.dpIds, props.dpIndex])

  const toRoutinesPage = useCallback((dpValue: PressType) => {
    const routineParam: RoutineParam = {
      pageType: 'CreateAndList',
      conditionType: 'Any',
      conditions: [{
        type: "DeviceStatusChange",
        targetId: props.dpIds[props.dpIndex],
        operator: "Equal",
        value: dpValue
      }]
    }
    NativeApi.toRoutinesPage({
      backTitle: props.backTitle,
      deviceId: props.deviceId,
      routineParam: routineParam
    })
  }, [props.backTitle, props.deviceId, props.dpIds, props.dpIndex])

  useEffect(() => {
    let data: ActionData[]
    if (props.channel === 1) {
      data = [
        {
          title: I18n.getLang('switch_singlepress'), onPress: () => toRoutinesPage(PressType.Single)
        },
        {
          title: I18n.getLang('switch_doublepress'), onPress: () => toRoutinesPage(PressType.Double)
        },
        {
          title: I18n.getLang('switch_longpress'), onPress: () => toRoutinesPage(PressType.Long)
        },
      ]
    } else {
      data = [
        {
          title: I18n.getLang('switch_4channelfunctions1'),
          onPress: () => toSettingPage(0)
        },
        {
          title: I18n.getLang('switch_4channelfunctions2'),
          onPress: () => toSettingPage(1)
        },
      ]
      if (props.channel === 4) {
        data.splice(1, 0, {
          title: I18n.getLang('switch_4channelfunctions3'),
          onPress: () => toSettingPage(2)
        })
        data.push({
          title: I18n.getLang('switch_4channelfunctions4'),
          onPress: () => toSettingPage(3)
        })
      }
    }
    setActionData(data)
  }, [props.channel, props.backTitle, props.deviceId])

  const styles = StyleSheet.create({
    container: {
      marginHorizontal: cx(14),
    },
    item: {
      flex: 0.5,
      justifyContent: 'center',
      alignItems: 'center',
    },
    itemContainer: {
      width: cx(154),
      borderRadius: cx(16),
    },
    itemContent: {},
    titleBg: {
      height: cx(118),
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      marginHorizontal: cx(16),
      color: props.theme?.global.fontColor ?? 'black', // 添加默认值
      fontSize: cx(14),
      textAlign: 'center',
      fontFamily: 'helvetica_neue_lt_std_roman',
    },
  })

  const renderItem = useCallback(({item}) => (
    <View style={styles.item}>
      <Card
        style={styles.itemContainer}
        containerStyle={styles.itemContent}
        onPress={item.onPress}>
        <View style={styles.titleBg}>
          <Text style={styles.title}>{item.title}</Text>
          <Spacer height={cx(8)}/>
        </View>
      </Card>
    </View>
  ), [styles])

  const keyExtractor = useCallback((_: ActionData, index: number) => `key-${index}`, [])
  const Header = useCallback(() => <Spacer height={cx(10)}/>, [])
  const Separator = useCallback(() => <Spacer/>, [])
  const Footer = useCallback(() => <Spacer height={cx(30)}/>, [])

  return (
    <FlatList
      scrollEnabled={false}
      numColumns={2}
      style={styles.container}
      data={actionData}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={Header}
      ItemSeparatorComponent={Separator}
      ListFooterComponent={Footer}
    />
  )
}

export default withTheme(PressActionView)
