import {useDp} from "@ledvance/base/src/models/modules/NativePropsSlice"
import {getGlobalParamsDp, isSupportFunctions} from "@ledvance/base/src/utils/common"

export function useBatteryPercentage(): number {
  return useDp<number, any>(getGlobalParamsDp('battery_percentage'))[0]
}

export function useChannel() {
  if (isSupportFunctions('switch_type_4')) {
    return 4
  } else if (isSupportFunctions('switch_type_2')) {
    return 2
  } else {
    return 1
  }
}
