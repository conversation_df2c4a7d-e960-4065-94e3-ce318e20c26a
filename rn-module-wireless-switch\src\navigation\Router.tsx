import {NavigationRoute} from 'tuya-panel-kit'
import HomePage from '../pages/home/<USER>'
import SettingPage from "../pages/setting/Setting";

export const RouterKey = {
    main: 'main',
    setting: 'setting'
}

export const AppRouters: NavigationRoute[] = [
    {
        name: RouterKey.main,
        component: HomePage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
    {
        name: RouterKey.setting,
        component: SettingPage,
        options: {
            hideTopbar: true,
            showOfflineView: false,
        },
    },
]
