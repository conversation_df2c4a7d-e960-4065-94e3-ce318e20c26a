import { useDeviceInfo, useFamilyName } from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, { useEffect} from 'react'
import ThemeType from '@ledvance/base/src/config/themeType'
import Page from '@ledvance/base/src/components/Page'
import res from '@ledvance/base/src/res'
import { Utils } from 'tuya-panel-kit'
import { NativeApi } from '@ledvance/base/src/api/native'
import {Image, ScrollView, StyleSheet, Text, View} from "react-native"
import {useBatteryPercentage, useChannel} from "../../features/FeatureHooks";
import BatteryPercentageView from "@ledvance/base/src/components/BatteryPercentageView";
import Spacer from "@ledvance/base/src/components/Spacer";
import Card from "@ledvance/base/src/components/Card";
import PressActionView from "../../components/PressActionView";
import {getGlobalParamsDp} from "@ledvance/base/src/utils/common";
import {RouterKey} from "../../navigation/Router";
import {useReactive} from "ahooks";
import I18n from "@ledvance/base/src/i18n";
import switchRes from '@res'

const cx = Utils.RatioUtils.convertX
const { withTheme } = Utils.ThemeUtils

interface HomeProps {
  theme?: ThemeType
}

const HomePage = (props: HomeProps) => {
  const devInfo = useDeviceInfo()
  const familyName = useFamilyName()
  const batteryPercentage = useBatteryPercentage()
  const channel = useChannel()
  const state = useReactive({
    title: '',
    picture: undefined as any,
  })
  const dpIds = [getGlobalParamsDp('switch_type_1'), getGlobalParamsDp('switch_type_2'), getGlobalParamsDp('switch_type_3'), getGlobalParamsDp('switch_type_4'),]

  useEffect(() => {
    if (channel === 1) {
      state.title = I18n.getLang('switchname_1channel')
      state.picture = switchRes.pic_switch_one_channel
    } else if (channel === 2) {
      state.title = I18n.getLang('switchname_2channel')
      state.picture = switchRes.pic_switch_two_channel
    } else {
      state.title = I18n.getLang('switchname_4channel')
      state.picture = switchRes.pic_switch_four_channel
    }
  }, [channel])
  const styles = StyleSheet.create({
    cardTitle: {
      marginStart: cx(16),
      marginVertical: cx(18),
      color: props.theme?.global.fontColor,
      fontSize: cx(16),
      fontFamily: 'helvetica_neue_lt_std_bd',
    },
    imageContainer: {
      justifyContent: "center",
      alignItems: "center",
    }
  })
  return (
    <Page
      backText={familyName}
      onBackClick={NativeApi.back}
      headlineText={devInfo.name}
      headlineIcon={res.ic_more}
      onHeadlineIconClick={() => {
        NativeApi.toDeviceSettingsPage(devInfo.devId)
      }}
    >
      <ScrollView>
        <BatteryPercentageView value={batteryPercentage} />
        <Spacer />
        <Card style={{marginHorizontal: cx(24)}}>
          <Text style={styles.cardTitle}>{state.title}</Text>
          <View style={styles.imageContainer}>
            <Image source={ state.picture } width={cx(120)} height={cx(120)} />
          </View>
          <Spacer />
        </Card>
        <Spacer />
        <PressActionView
          channel={channel}
          backTitle={devInfo.name}
          deviceId={devInfo.devId}
          dpIds={dpIds}
          dpIndex={0}
          routerKey={RouterKey.setting}
        />
      </ScrollView>
    </Page>
  )
}

export default withTheme(HomePage)
