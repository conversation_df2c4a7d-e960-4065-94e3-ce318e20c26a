import {useDeviceInfo} from '@ledvance/base/src/models/modules/NativePropsSlice'
import React, {useMemo} from 'react'
import Page from '@ledvance/base/src/components/Page'
import {Utils} from 'tuya-panel-kit'
import PressActionView from "../../components/PressActionView";
import {useParams} from "@ledvance/base/src/hooks/Hooks";
import I18n from "@ledvance/base/src/i18n";

const { withTheme } = Utils.ThemeUtils

interface SettingParam {
  dpIds: string[]
  dpIndex: number
}

const SettingPage = () => {
  const devInfo = useDeviceInfo()
  const params = useParams<SettingParam>()
  const headlines = useMemo(() => [
    I18n.getLang('switch_4channels1setting'),
    I18n.getLang('switch_4channels2setting'),
    I18n.getLang('switch_4channels3setting'),
    I18n.getLang('switch_4channels4setting')
  ], [])

  return (
    <Page
      backText={devInfo.name}
      headlineText={headlines[params.dpIndex]}
    >
      <PressActionView
        channel={1}
        backTitle={headlines[params.dpIndex]}
        deviceId={devInfo.devId}
        dpIds={params.dpIds}
        dpIndex={params.dpIndex}
      />
    </Page>
  )
}

export default withTheme(SettingPage)
